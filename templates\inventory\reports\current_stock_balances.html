{% extends "base.html" %}
{% load static %}

{% block title %}تقرير الأرصدة الحالية - KamaVerse{% endblock %}

{% block extra_css %}
<style>
    :root {
        --brand-red: #D62828;
        --brand-red-dark: #8B1116;
        --brand-red-light: #FCE8E8;
        --brand-gold: #C89A3C;
        --brand-gold-light: #F4D488;
        --brand-gold-dark: #8C6420;
        --ink: #1A1A1A;
        --slate: #4A4F57;
        --line: #E6E8ED;
        --canvas: #F7F8FB;
        --white: #FFFFFF;
        --success: #2E7D32;
        --warning: #F39C12;
        --error: #C21807;
    }

    .page-container {
        background: linear-gradient(180deg, #FFFFFF 0%, #F7F8FB 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .page-header {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    .page-header h1 {
        color: var(--ink);
        font-weight: 700;
        font-size: 2.5rem;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .page-header .icon {
        color: var(--brand-gold);
        font-size: 3rem;
    }

    .page-header p {
        color: var(--slate);
        font-size: 1.1rem;
        margin: 1rem 0 0 0;
    }

    .filters-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    .filters-title {
        color: var(--ink);
        font-weight: 600;
        font-size: 1.2rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filters-title i {
        color: var(--brand-gold);
    }

    .filter-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .form-group label {
        color: var(--slate);
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        border: 1px solid var(--line);
        border-radius: 8px;
        padding: 0.75rem;
        color: var(--ink);
        background: var(--white);
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: var(--brand-gold);
        box-shadow: 0 0 0 0.2rem rgba(200, 154, 60, 0.25);
        outline: none;
    }

    .btn-primary {
        background: var(--brand-red);
        border: 1px solid var(--brand-red);
        color: var(--white);
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary:hover {
        background: var(--brand-red-dark);
        border-color: var(--brand-red-dark);
        color: var(--white);
        text-decoration: none;
        transform: translateY(-1px);
    }

    .btn-secondary {
        background: var(--white);
        border: 1px solid var(--brand-gold);
        color: var(--brand-gold-dark);
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-secondary:hover {
        background: var(--brand-gold-light);
        border-color: var(--brand-gold);
        color: var(--brand-gold-dark);
        text-decoration: none;
    }

    .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .summary-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
        position: relative;
        overflow: hidden;
    }

    .summary-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, var(--brand-gold) 0%, var(--brand-gold-dark) 100%);
    }

    .summary-content {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .summary-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.3rem;
        color: var(--brand-gold);
        background: var(--brand-gold-light);
    }

    .summary-info h3 {
        color: var(--ink);
        font-weight: 600;
        font-size: 1rem;
        margin: 0 0 0.3rem 0;
    }

    .summary-info .value {
        color: var(--brand-red);
        font-weight: 700;
        font-size: 1.5rem;
        margin: 0;
    }

    .table-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
        overflow: hidden;
    }

    .table-header {
        color: var(--ink);
        font-weight: 600;
        font-size: 1.2rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .table-header i {
        color: var(--brand-gold);
    }

    .table-responsive {
        border-radius: 8px;
        border: 1px solid var(--line);
    }

    .table {
        margin: 0;
        color: var(--ink);
    }

    .table thead th {
        background: var(--canvas);
        color: var(--ink);
        font-weight: 600;
        border-bottom: 1px solid var(--line);
        padding: 1rem 0.75rem;
        font-size: 0.9rem;
    }

    .table tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid var(--line);
        vertical-align: middle;
    }

    .table tbody tr:hover {
        background: var(--canvas);
    }

    .item-code {
        font-family: 'Courier New', monospace;
        background: var(--canvas);
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.85rem;
        color: var(--slate);
    }

    .quantity-badge {
        background: var(--brand-red-light);
        color: var(--brand-red);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
    }

    .warehouse-badge {
        background: var(--brand-gold-light);
        color: var(--brand-gold-dark);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
    }

    .value-text {
        color: var(--brand-red);
        font-weight: 600;
    }

    .breadcrumb {
        background: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }

    .breadcrumb-item {
        color: var(--slate);
    }

    .breadcrumb-item.active {
        color: var(--brand-red);
        font-weight: 600;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: var(--brand-gold);
        margin: 0 0.5rem;
    }

    .no-data {
        text-align: center;
        padding: 3rem;
        color: var(--slate);
    }

    .no-data i {
        font-size: 4rem;
        color: var(--brand-gold);
        margin-bottom: 1rem;
    }

    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2rem;
        }
        
        .page-header .icon {
            font-size: 2.5rem;
        }

        .filter-row {
            grid-template-columns: 1fr;
        }

        .summary-cards {
            grid-template-columns: 1fr;
        }

        .table-responsive {
            font-size: 0.85rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="container-fluid">
        <!-- مسار التنقل -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:dashboard' %}">الصفحة الرئيسية</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:reports_main_dashboard' %}">التقارير</a>
                </li>
                <li class="breadcrumb-item active">الأرصدة الحالية</li>
            </ol>
        </nav>

        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1>
                <i class="fas fa-inventory icon"></i>
                تقرير الأرصدة الحالية
            </h1>
            <p>عرض جميع الأرصدة الحالية للأصناف في المخازن مع إمكانية البحث والتصفية</p>
        </div>

        <!-- الفلاتر -->
        <div class="filters-card">
            <div class="filters-title">
                <i class="fas fa-filter"></i>
                فلاتر البحث والتصفية
            </div>
            
            <form method="GET" action="">
                <div class="filter-row">
                    <div class="form-group">
                        <label for="search">البحث في الأصناف</label>
                        <input type="text" 
                               id="search" 
                               name="search" 
                               class="form-control" 
                               placeholder="اسم الصنف أو الكود..."
                               value="{{ search_query }}">
                    </div>
                    
                    <div class="form-group">
                        <label for="warehouse">المخزن</label>
                        <select id="warehouse" name="warehouse" class="form-control">
                            <option value="">جميع المخازن</option>
                            {% for warehouse in warehouses %}
                                <option value="{{ warehouse.id }}" 
                                        {% if warehouse_filter == warehouse.id|stringformat:"s" %}selected{% endif %}>
                                    {{ warehouse.warehouse_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="category">فئة المادة</label>
                        <select id="category" name="category" class="form-control">
                            <option value="">جميع الفئات</option>
                            {% for value, label in categories %}
                                <option value="{{ value }}" 
                                        {% if category_filter == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div class="d-flex gap-2 flex-wrap">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                    <a href="{% url 'inventory:current_stock_balances_report' %}" class="btn-secondary">
                        <i class="fas fa-times"></i>
                        إزالة الفلاتر
                    </a>
                </div>
            </form>
        </div>

        <!-- الإحصائيات العامة -->
        <div class="summary-cards">
            <div class="summary-card">
                <div class="summary-content">
                    <div class="summary-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="summary-info">
                        <h3>إجمالي الأصناف</h3>
                        <p class="value">{{ summary_data.total_items|floatformat:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="summary-card">
                <div class="summary-content">
                    <div class="summary-icon">
                        <i class="fas fa-weight"></i>
                    </div>
                    <div class="summary-info">
                        <h3>إجمالي الكمية</h3>
                        <p class="value">{{ summary_data.total_quantity|floatformat:2 }}</p>
                    </div>
                </div>
            </div>

            <div class="summary-card">
                <div class="summary-content">
                    <div class="summary-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="summary-info">
                        <h3>إجمالي القيمة</h3>
                        <p class="value">{{ summary_data.total_value|floatformat:2 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الأرصدة -->
        <div class="table-card">
            <div class="table-header">
                <i class="fas fa-table"></i>
                تفاصيل الأرصدة الحالية
            </div>

            {% if stock_balances %}
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>كود الصنف</th>
                                <th>اسم الصنف</th>
                                <th>المخزن</th>
                                <th>موقع التخزين</th>
                                <th>الكمية الحالية</th>
                                <th>الوحدة</th>
                                <th>سعر الوحدة</th>
                                <th>إجمالي القيمة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for balance in stock_balances %}
                                <tr>
                                    <td>
                                        <span class="item-code">{{ balance.item.item_code }}</span>
                                    </td>
                                    <td>
                                        <strong>{{ balance.item.item_name_ar }}</strong>
                                        {% if balance.item.item_name_en %}
                                            <br><small class="text-muted">{{ balance.item.item_name_en }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="warehouse-badge">{{ balance.warehouse.warehouse_name }}</span>
                                    </td>
                                    <td>
                                        {% if balance.bin_location %}
                                            {{ balance.bin_location.bin_code }}
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="quantity-badge">{{ balance.current_quantity|floatformat:2 }}</span>
                                    </td>
                                    <td>{{ balance.item.get_primary_unit_display }}</td>
                                    <td class="value-text">{{ balance.unit_cost|floatformat:2 }}</td>
                                    <td class="value-text">
                                        <strong>{{ balance.total_value|floatformat:2 }}</strong>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="no-data">
                    <i class="fas fa-inbox"></i>
                    <h4>لا توجد أرصدة متاحة</h4>
                    <p>لا توجد أرصدة حالية تطابق معايير البحث المحددة</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}