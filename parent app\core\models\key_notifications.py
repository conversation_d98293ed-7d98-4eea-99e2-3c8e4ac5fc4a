"""
KeyNotifications - نظام الإشعارات
إدارة جميع الإشعارات في النظام
"""

from django.db import models
import uuid


class KeyNotification(models.Model):
    """
    نموذج الإشعارات الأساسي
    """
    
    NOTIFICATION_TYPES = [
        ('INFO', 'معلومات'),
        ('WARNING', 'تحذير'),
        ('ERROR', 'خطأ'),
        ('SUCCESS', 'نجاح'),
        ('REMINDER', 'تذكير'),
        ('APPROVAL', 'موافقة'),
        ('SYSTEM', 'نظام'),
    ]
    
    PRIORITY_LEVELS = [
        ('LOW', 'منخفض'),
        ('NORMAL', 'عادي'),
        ('HIGH', 'عالي'),
        ('URGENT', 'عاجل'),
    ]
    
    # Primary key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Notification Information
    notification_type = models.CharField(
        max_length=20,
        choices=NOTIFICATION_TYPES,
        verbose_name='نوع الإشعار'
    )
    
    title = models.CharField(
        max_length=200,
        verbose_name='العنوان'
    )
    
    message = models.TextField(
        verbose_name='الرسالة'
    )
    
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_LEVELS,
        default='NORMAL',
        verbose_name='الأولوية'
    )
    
    # Recipients
    recipient = models.ForeignKey(
        'core.KeyUser',
        on_delete=models.CASCADE,
        related_name='notifications',
        verbose_name='المستقبل'
    )

    sender = models.ForeignKey(
        'core.KeyUser',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='sent_notifications',
        verbose_name='المرسل'
    )
    
    # Status
    is_read = models.BooleanField(
        default=False,
        verbose_name='مقروء'
    )
    
    read_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='تاريخ القراءة'
    )
    
    # Additional Information
    action_url = models.URLField(
        verbose_name='رابط الإجراء',
        blank=True,
        null=True
    )
    
    expires_at = models.DateTimeField(
        verbose_name='تاريخ الانتهاء',
        blank=True,
        null=True
    )
    
    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    
    class Meta:
        verbose_name = 'إشعار'
        verbose_name_plural = 'الإشعارات'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', 'is_read']),
            models.Index(fields=['notification_type']),
            models.Index(fields=['priority']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.recipient.arabic_name}"
    
    def mark_as_read(self):
        """تحديد الإشعار كمقروء"""
        from django.utils import timezone
        self.is_read = True
        self.read_at = timezone.now()
        self.save(update_fields=['is_read', 'read_at'])
