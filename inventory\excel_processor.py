"""
معالج ملفات Excel لإيصالات الاستلام
Excel Processor for Goods Receipts
"""

import openpyxl
from decimal import Decimal, InvalidOperation
from django.core.exceptions import ValidationError
from .models import ItemMaster, BinLocation


class ExcelProcessor:
    """معالج ملفات Excel"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.processed_data = []
        
        # خريطة تحويل الوحدات من العربية إلى الكود
        self.unit_mapping = {
            'كيلو': 'KG',
            'كيلوجرام': 'KG', 
            'كجم': 'KG',
            'طن': 'TON',
            'لتر': 'LITER',
            'جالون': 'GALLON',
            'شيكارة': 'BAG',
            'كيس': 'BAG',
            'برميل': 'DRUM',
            'كرتونة': 'CARTON',
            'صندوق': 'CARTON',
            'قطعة': 'PIECE',
            'متر': 'METER',
            'لفة': 'ROLL',
            'متر مكعب': 'CUBIC_METER'
        }
    
    def process_excel_file(self, file_path_or_obj, warehouse):
        """معالجة ملف Excel وإرجاع البيانات المعالجة"""
        self.errors = []
        self.warnings = []
        self.processed_data = []
        
        try:
            # فتح ملف Excel
            workbook = openpyxl.load_workbook(file_path_or_obj)
            worksheet = workbook.active
            
            # التحقق من وجود بيانات
            if worksheet.max_row < 2:
                self.errors.append("الملف فارغ أو لا يحتوي على بيانات")
                return False
            
            # قراءة رأس الجدول
            headers = self._read_headers(worksheet)
            if not self._validate_headers(headers):
                return False
            
            # معالجة البيانات
            for row_num in range(2, worksheet.max_row + 1):
                row_data = self._process_row(worksheet, row_num, headers, warehouse)
                if row_data:
                    self.processed_data.append(row_data)
            
            return len(self.processed_data) > 0
            
        except Exception as e:
            self.errors.append(f"خطأ في قراءة الملف: {str(e)}")
            return False
    
    def _read_headers(self, worksheet):
        """قراءة رأس الجدول"""
        headers = {}
        for col in range(1, worksheet.max_column + 1):
            cell_value = worksheet.cell(row=1, column=col).value
            if cell_value:
                headers[col] = str(cell_value).strip()
        return headers
    
    def _validate_headers(self, headers):
        """التحقق من صحة رأس الجدول"""
        required_headers = ['كود الصنف', 'اسم الصنف', 'الكمية', 'الوحدة']
        header_values = list(headers.values())
        
        missing_headers = []
        for required in required_headers:
            if not any(required in header for header in header_values):
                missing_headers.append(required)
        
        if missing_headers:
            self.errors.append(f"أعمدة مطلوبة مفقودة: {', '.join(missing_headers)}")
            return False
        
        return True
    
    def _map_unit_to_code(self, unit_text):
        """تحويل نص الوحدة بالعربية إلى كود قاعدة البيانات"""
        if not unit_text:
            return None
            
        unit_text = unit_text.strip()
        
        # البحث المباشر
        if unit_text in self.unit_mapping:
            return self.unit_mapping[unit_text]
        
        # البحث بدون حساسية لحالة الأحرف
        for arabic_unit, code in self.unit_mapping.items():
            if unit_text.lower() == arabic_unit.lower():
                return code
        
        # البحث الجزئي
        for arabic_unit, code in self.unit_mapping.items():
            if arabic_unit in unit_text or unit_text in arabic_unit:
                return code
        
        return None
    
    def _process_row(self, worksheet, row_num, headers, warehouse):
        """معالجة صف واحد من البيانات"""
        row_data = {}
        row_errors = []
        
        # استخراج البيانات من الصف - الأعمدة المطلوبة فقط
        required_columns = ['كود الصنف', 'اسم الصنف', 'الكمية', 'الوحدة']
        
        for col, header in headers.items():
            if any(req_col in header for req_col in required_columns):
                cell_value = worksheet.cell(row=row_num, column=col).value
                if cell_value is not None:
                    row_data[header] = str(cell_value).strip()
        
        # التحقق من البيانات المطلوبة
        item_code = ''
        item_name = ''
        quantity_str = ''
        unit = ''
        
        # البحث عن القيم في رأس الجدول
        for header, value in row_data.items():
            if 'كود الصنف' in header:
                item_code = value
            elif 'اسم الصنف' in header:
                item_name = value
            elif 'الكمية' in header:
                quantity_str = value
            elif 'الوحدة' in header:
                unit = value
        
        # التحقق من وجود البيانات المطلوبة
        if not item_code:
            row_errors.append("كود الصنف مطلوب")
        
        if not item_name:
            row_errors.append("اسم الصنف مطلوب")
        
        if not quantity_str:
            row_errors.append("الكمية مطلوبة")
        
        # تحويل الوحدة من العربية إلى الكود
        unit_code = self._map_unit_to_code(unit)
        if not unit_code:
            row_errors.append(f"وحدة غير معروفة: {unit}")
        
        # التحقق من الكمية
        quantity = None
        if quantity_str:
            try:
                quantity = Decimal(str(quantity_str))
                if quantity <= 0:
                    row_errors.append("الكمية يجب أن تكون أكبر من صفر")
            except (InvalidOperation, ValueError):
                row_errors.append("الكمية غير صحيحة")
        
        # البحث عن الصنف أو إنشاء جديد
        item = None
        item_status = "غير محدد"
        
        if item_code and item_name:
            # البحث بالكود أولاً
            try:
                item = ItemMaster.objects.get(item_code=item_code)
                item_status = "صنف موجود"
                
                # التحقق من تطابق الاسم
                if item.item_name_ar != item_name:
                    self.warnings.append(f"الصف {row_num}: اسم الصنف مختلف عن المسجل ({item.item_name_ar})")
                    
            except ItemMaster.DoesNotExist:
                # البحث بالاسم
                try:
                    item = ItemMaster.objects.get(item_name_ar=item_name)
                    item_status = "صنف موجود - اسم مطابق"
                    self.warnings.append(f"الصف {row_num}: كود الصنف مختلف عن المسجل ({item.item_code})")
                    
                except ItemMaster.DoesNotExist:
                    # صنف جديد
                    item_status = "صنف جديد - سيتم إنشاؤه"
        
        # إضافة الأخطاء إلى القائمة العامة
        if row_errors:
            for error in row_errors:
                self.errors.append(f"الصف {row_num}: {error}")
            return None
        
        # إرجاع البيانات المعالجة
        return {
            'row_number': row_num,
            'item_code': item_code,
            'item_name': item_name,
            'quantity': quantity,
            'unit': unit_code or unit,  # استخدام الكود الموحد أو النص الأصلي
            'unit_display': unit,  # النص العربي للعرض
            'item': item,
            'item_status': item_status
        }
    
    def create_items_from_data(self):
        """إنشاء الأصناف الجديدة من البيانات المعالجة"""
        created_items = []
        
        for data in self.processed_data:
            if data['item_status'] == "صنف جديد - سيتم إنشاؤه":
                try:
                    # التحقق من عدم وجود الصنف مسبقاً
                    if not ItemMaster.objects.filter(item_code=data['item_code']).exists():
                        item = ItemMaster.objects.create(
                            item_code=data['item_code'],
                            item_name_ar=data['item_name'],
                            item_name_en=data['item_name'],  # يمكن تحسينه لاحقاً
                            unit_of_measure=data['unit'],  # استخدام الكود الموحد
                            category='CHEMICALS',  # افتراضي
                            material_type='RAW_MATERIAL',  # افتراضي
                            is_active=True
                        )
                        data['item'] = item
                        data['item_status'] = "صنف جديد - تم إنشاؤه"
                        created_items.append(item)
                        
                except Exception as e:
                    self.errors.append(f"خطأ في إنشاء الصنف {data['item_code']}: {str(e)}")
        
        return created_items
    
    def get_summary(self):
        """الحصول على ملخص المعالجة"""
        total_rows = len(self.processed_data)
        new_items = len([d for d in self.processed_data if 'جديد' in d['item_status']])
        existing_items = total_rows - new_items
        
        return {
            'total_rows': total_rows,
            'new_items': new_items,
            'existing_items': existing_items,
            'errors_count': len(self.errors),
            'warnings_count': len(self.warnings),
            'errors': self.errors,
            'warnings': self.warnings
        }
    
    def generate_template(self):
        """توليد قالب Excel"""
        workbook = openpyxl.Workbook()
        worksheet = workbook.active
        worksheet.title = "قالب إذن الاستلام"
        
        # رأس الجدول - الأعمدة المطلوبة فقط
        headers = [
            'كود الصنف',
            'اسم الصنف', 
            'الكمية',
            'الوحدة'
        ]
        
        for col, header in enumerate(headers, 1):
            cell = worksheet.cell(row=1, column=col)
            cell.value = header
            cell.font = openpyxl.styles.Font(bold=True)
            cell.fill = openpyxl.styles.PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
        
        # بيانات تجريبية
        sample_data = [
            ['CH001', 'حمض الكبريتيك', '100', 'لتر'],
            ['PL002', 'بولي إيثيلين', '50', 'كيلو'],
            ['CH003', 'هيدروكسيد الصوديوم', '75', 'كيلو']
        ]
        
        for row, data in enumerate(sample_data, 2):
            for col, value in enumerate(data, 1):
                worksheet.cell(row=row, column=col).value = value
        
        # تنسيق الأعمدة
        for col in range(1, len(headers) + 1):
            worksheet.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 20
        
        return workbook


    def process_issue_excel(self, file_path_or_obj):
        """معالجة ملف Excel لإذن الصرف"""
        self.errors = []
        self.warnings = []
        self.processed_data = []

        try:
            # فتح ملف Excel
            workbook = openpyxl.load_workbook(file_path_or_obj)
            worksheet = workbook.active

            # التحقق من وجود البيانات
            if worksheet.max_row < 2:
                raise ValidationError("الملف فارغ أو لا يحتوي على بيانات")

            # قراءة رأس الجدول (الصف الأول)
            headers = []
            for col in range(1, worksheet.max_column + 1):
                cell_value = worksheet.cell(row=1, column=col).value
                headers.append(str(cell_value).strip() if cell_value else '')

            # التحقق من وجود الأعمدة المطلوبة
            required_columns = ['كود الصنف', 'اسم الصنف', 'الكمية']
            missing_columns = []

            for required_col in required_columns:
                if not any(required_col in header for header in headers):
                    missing_columns.append(required_col)

            if missing_columns:
                raise ValidationError(f"الأعمدة المطلوبة مفقودة: {', '.join(missing_columns)}")

            # تحديد مؤشرات الأعمدة
            item_code_col = None
            item_name_col = None
            quantity_col = None
            bin_location_col = None

            for i, header in enumerate(headers):
                if 'كود الصنف' in header:
                    item_code_col = i + 1
                elif 'اسم الصنف' in header:
                    item_name_col = i + 1
                elif 'الكمية' in header:
                    quantity_col = i + 1
                elif 'موقع التخزين' in header:
                    bin_location_col = i + 1

            # معالجة البيانات
            for row_num in range(2, worksheet.max_row + 1):
                try:
                    # قراءة البيانات
                    item_code = str(worksheet.cell(row=row_num, column=item_code_col).value or '').strip()
                    item_name = str(worksheet.cell(row=row_num, column=item_name_col).value or '').strip()
                    quantity_value = worksheet.cell(row=row_num, column=quantity_col).value
                    bin_location = str(worksheet.cell(row=row_num, column=bin_location_col).value or '').strip() if bin_location_col else ''

                    # تخطي الصفوف الفارغة
                    if not item_code and not item_name and not quantity_value:
                        continue

                    # التحقق من البيانات الأساسية
                    if not item_code:
                        self.warnings.append(f"الصف {row_num}: كود الصنف مفقود")
                        continue

                    if not quantity_value:
                        self.warnings.append(f"الصف {row_num}: الكمية مفقودة")
                        continue

                    # تحويل الكمية
                    try:
                        quantity = Decimal(str(quantity_value))
                        if quantity <= 0:
                            self.warnings.append(f"الصف {row_num}: الكمية يجب أن تكون أكبر من صفر")
                            continue
                    except (InvalidOperation, ValueError):
                        self.warnings.append(f"الصف {row_num}: الكمية غير صحيحة - {quantity_value}")
                        continue

                    # إضافة البيانات المعالجة
                    row_data = {
                        'item_code': item_code,
                        'item_name': item_name,
                        'quantity': quantity,
                        'bin_location': bin_location,
                        'row_number': row_num
                    }

                    self.processed_data.append(row_data)

                except Exception as e:
                    self.errors.append(f"خطأ في الصف {row_num}: {str(e)}")
                    continue

            if not self.processed_data:
                raise ValidationError("لا توجد بيانات صحيحة للمعالجة")

            return self.processed_data

        except Exception as e:
            if isinstance(e, ValidationError):
                raise e
            else:
                raise ValidationError(f"خطأ في معالجة ملف Excel: {str(e)}")

    def process_issue_excel_file(self, file_path_or_obj, warehouse):
        """معالجة ملف Excel لإذن الصرف مع التحقق من وجود 4 أعمدة فقط"""
        self.errors = []
        self.warnings = []
        self.processed_data = []
        
        try:
            # فتح ملف Excel
            workbook = openpyxl.load_workbook(file_path_or_obj)
            worksheet = workbook.active
            
            # التحقق من وجود بيانات
            if worksheet.max_row < 2:
                self.errors.append("الملف فارغ أو لا يحتوي على بيانات")
                return False
            
            # قراءة رأس الجدول
            headers = self._read_headers(worksheet)
            if not self._validate_issue_headers(headers):
                return False
            
            # معالجة البيانات
            for row_num in range(2, worksheet.max_row + 1):
                row_data = self._process_issue_row(worksheet, row_num, headers, warehouse)
                if row_data:
                    self.processed_data.append(row_data)
            
            return len(self.processed_data) > 0
            
        except Exception as e:
            self.errors.append(f"خطأ في قراءة الملف: {str(e)}")
            return False

    def _validate_issue_headers(self, headers):
        """التحقق من صحة رأس الجدول لإذن الصرف - 4 أعمدة فقط"""
        required_headers = ['كود الصنف', 'اسم الصنف', 'الكمية', 'الوحدة']
        header_values = list(headers.values())
        
        missing_headers = []
        for required in required_headers:
            if not any(required in header for header in header_values):
                missing_headers.append(required)
        
        if missing_headers:
            self.errors.append(f"أعمدة مطلوبة مفقودة: {', '.join(missing_headers)}")
            return False
        
        return True

    def _process_issue_row(self, worksheet, row_num, headers, warehouse):
        """معالجة صف واحد من بيانات إذن الصرف"""
        row_data = {}
        row_errors = []
        
        # استخراج البيانات من الصف - الأعمدة المطلوبة فقط
        required_columns = ['كود الصنف', 'اسم الصنف', 'الكمية', 'الوحدة']
        
        for col, header in headers.items():
            if any(req_col in header for req_col in required_columns):
                cell_value = worksheet.cell(row=row_num, column=col).value
                if cell_value is not None:
                    row_data[header] = str(cell_value).strip()
        
        # التحقق من البيانات المطلوبة
        item_code = ''
        item_name = ''
        quantity_str = ''
        unit = ''
        
        # البحث عن القيم في رأس الجدول
        for header, value in row_data.items():
            if 'كود الصنف' in header:
                item_code = value
            elif 'اسم الصنف' in header:
                item_name = value
            elif 'الكمية' in header:
                quantity_str = value
            elif 'الوحدة' in header:
                unit = value
        
        # تخطي الصفوف الفارغة
        if not item_code and not item_name and not quantity_str:
            return None
        
        # التحقق من وجود البيانات المطلوبة
        if not item_code:
            row_errors.append("كود الصنف مطلوب")
        
        if not item_name:
            row_errors.append("اسم الصنف مطلوب")
        
        if not quantity_str:
            row_errors.append("الكمية مطلوبة")
        
        if not unit:
            row_errors.append("الوحدة مطلوبة")
        
        # تحويل الوحدة من العربية إلى الكود
        unit_code = self._map_unit_to_code(unit)
        if not unit_code:
            row_errors.append(f"وحدة غير معروفة: {unit}")
        
        # التحقق من الكمية
        quantity = None
        if quantity_str:
            try:
                quantity = Decimal(str(quantity_str))
                if quantity <= 0:
                    row_errors.append("الكمية يجب أن تكون أكبر من صفر")
            except (InvalidOperation, ValueError):
                row_errors.append("الكمية غير صحيحة")
        
        # إذا وجدت أخطاء، أضفها لقائمة الأخطاء
        if row_errors:
            for error in row_errors:
                self.errors.append(f"الصف {row_num}: {error}")
            return None
        
        # البحث عن الصنف أو تحديد حالته
        item_status = "غير محدد"
        
        if item_code and item_name:
            # البحث بالكود أولاً
            try:
                item = ItemMaster.objects.get(item_code=item_code)
                item_status = "صنف موجود"
                
                # التحقق من تطابق الاسم
                if item.item_name_ar != item_name:
                    self.warnings.append(f"الصف {row_num}: اسم الصنف مختلف عن المسجل ({item.item_name_ar})")
                    
            except ItemMaster.DoesNotExist:
                # البحث بالاسم
                try:
                    item = ItemMaster.objects.get(item_name_ar=item_name)
                    item_status = "صنف موجود - اسم مطابق"
                    self.warnings.append(f"الصف {row_num}: كود الصنف مختلف عن المسجل ({item.item_code})")
                    
                except ItemMaster.DoesNotExist:
                    # صنف جديد
                    item_status = "صنف غير موجود"
        
        # إعداد البيانات النهائية
        result = {
            'item_code': item_code,
            'item_name': item_name,
            'quantity': quantity,
            'unit': unit_code,
            'unit_display': unit,  # النص العربي للعرض
            'item_status': item_status,
            'row_number': row_num
        }
        
        return result

    def get_issue_template_data(self):
        """إنشاء بيانات قالب Excel لإذن الصرف"""
        return [
            ['كود الصنف', 'اسم الصنف', 'الكمية', 'موقع التخزين'],
            ['CHM001', 'بولي إيثيلين عالي الكثافة', '100', 'A01'],
            ['CHM002', 'بولي بروبيلين', '50', 'A02'],
            ['CHM003', 'كربونات الكالسيوم', '200', 'B01'],
        ]


# مثيل عام للاستخدام
excel_processor = ExcelProcessor()
