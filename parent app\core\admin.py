"""
Core Admin Configuration for KamaVerse
إعدادات لوحة الإدارة للنواة الأساسية
"""

from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from .models import (
    KeyUser, KeyUserPermission, KeyUserSession,
    KeyCompany, KeyCompanyContact,
    KeyProduct, KeyProductImage,
    KeyTransaction, KeyDocument, KeyApproval,
    KeyShipment, KeyInventoryMovement,
    KeyNotification, KeyAuditLog
)


@admin.register(KeyUser)
class KeyUserAdmin(UserAdmin):
    """إدارة المستخدمين في لوحة الإدارة"""
    
    list_display = ('username', 'arabic_name', 'employee_id', 'department', 
                    'user_level', 'is_active', 'last_login')
    list_filter = ('user_level', 'department', 'is_active', 'date_joined')
    search_fields = ('username', 'arabic_name', 'employee_id', 'email')
    ordering = ('arabic_name',)
    
    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        (_('معلومات شخصية'), {
            'fields': ('arabic_name', 'first_name', 'last_name', 'email',
                      'employee_id', 'national_id', 'phone_number')
        }),
        (_('معلومات العمل'), {
            'fields': ('user_level', 'department', 'job_title', 'hire_date',
                      'direct_manager')
        }),
        (_('الصلاحيات'), {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups',
                      'user_permissions'),
        }),
        (_('تواريخ مهمة'), {'fields': ('last_login', 'date_joined')}),
        (_('معلومات النظام'), {
            'fields': ('last_login_ip', 'failed_login_attempts', 'created_by'),
            'classes': ('collapse',)
        }),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('username', 'arabic_name', 'employee_id', 'department',
                      'user_level', 'password1', 'password2'),
        }),
    )


@admin.register(KeyUserPermission)
class KeyUserPermissionAdmin(admin.ModelAdmin):
    """إدارة صلاحيات المستخدمين"""
    
    list_display = ('user', 'module', 'permission_type', 'is_granted',
                    'financial_limit', 'granted_at')
    list_filter = ('module', 'permission_type', 'is_granted')
    search_fields = ('user__arabic_name', 'user__username')
    ordering = ('user', 'module')


@admin.register(KeyCompany)
class KeyCompanyAdmin(admin.ModelAdmin):
    """إدارة الشركات"""
    
    list_display = ('company_name_ar', 'company_code', 'company_type',
                    'country', 'is_active', 'rating')
    list_filter = ('company_type', 'country', 'is_active', 'is_approved')
    search_fields = ('company_name_ar', 'company_name_en', 'company_code')
    ordering = ('company_name_ar',)
    
    fieldsets = (
        (_('معلومات أساسية'), {
            'fields': ('company_code', 'company_name_ar', 'company_name_en',
                      'company_type', 'company_size')
        }),
        (_('معلومات الاتصال'), {
            'fields': ('primary_email', 'secondary_email', 'primary_phone',
                      'secondary_phone', 'fax', 'website')
        }),
        (_('العنوان'), {
            'fields': ('country', 'city', 'address_line_1', 'address_line_2',
                      'postal_code')
        }),
        (_('معلومات تجارية'), {
            'fields': ('tax_number', 'commercial_register', 'industry',
                      'established_year')
        }),
        (_('معلومات مالية'), {
            'fields': ('payment_terms', 'credit_limit', 'currency')
        }),
        (_('التقييم والحالة'), {
            'fields': ('rating', 'is_active', 'is_approved', 'is_blacklisted')
        }),
        (_('ملاحظات'), {
            'fields': ('notes', 'internal_notes'),
            'classes': ('collapse',)
        }),
    )


@admin.register(KeyProduct)
class KeyProductAdmin(admin.ModelAdmin):
    """إدارة المنتجات"""
    
    list_display = ('product_name_ar', 'product_code', 'category',
                    'hazard_level', 'is_active', 'selling_price')
    list_filter = ('category', 'hazard_level', 'is_active', 'manufacturer')
    search_fields = ('product_name_ar', 'product_name_en', 'product_code',
                    'barcode', 'sku')
    ordering = ('product_name_ar',)
    
    fieldsets = (
        (_('تعريف المنتج'), {
            'fields': ('product_code', 'barcode', 'sku', 'product_name_ar',
                      'product_name_en', 'commercial_name', 'scientific_name')
        }),
        (_('التصنيف'), {
            'fields': ('category', 'subcategory', 'brand', 'manufacturer')
        }),
        (_('الخصائص الفيزيائية'), {
            'fields': ('unit_of_measure', 'weight_per_unit', 'density', 'color')
        }),
        (_('الخصائص الكيميائية'), {
            'fields': ('chemical_formula', 'cas_number', 'purity_percentage'),
            'classes': ('collapse',)
        }),
        (_('معلومات الأمان'), {
            'fields': ('hazard_level', 'safety_data_sheet', 'hazard_symbols')
        }),
        (_('التخزين'), {
            'fields': ('storage_conditions', 'storage_temperature_min',
                      'storage_temperature_max', 'shelf_life_months')
        }),
        (_('التعبئة'), {
            'fields': ('packaging_type', 'package_size', 'packages_per_pallet'),
            'classes': ('collapse',)
        }),
        (_('معلومات مالية'), {
            'fields': ('standard_cost', 'last_purchase_price', 'selling_price',
                      'currency')
        }),
        (_('التحكم في المخزون'), {
            'fields': ('minimum_stock_level', 'maximum_stock_level',
                      'reorder_point')
        }),
        (_('الحالة'), {
            'fields': ('is_active', 'is_discontinued', 'requires_approval')
        }),
    )


@admin.register(KeyTransaction)
class KeyTransactionAdmin(admin.ModelAdmin):
    """إدارة المعاملات المالية"""
    
    list_display = ('transaction_number', 'transaction_type', 'amount',
                    'currency', 'company', 'status', 'transaction_date')
    list_filter = ('transaction_type', 'status', 'currency', 'transaction_date')
    search_fields = ('transaction_number', 'reference_number', 'description')
    ordering = ('-transaction_date',)
    date_hierarchy = 'transaction_date'


@admin.register(KeyDocument)
class KeyDocumentAdmin(admin.ModelAdmin):
    """إدارة المستندات"""
    
    list_display = ('document_name', 'document_type', 'confidentiality_level',
                    'related_company', 'created_at')
    list_filter = ('document_type', 'confidentiality_level', 'created_at')
    search_fields = ('document_name', 'description', 'tags')
    ordering = ('-created_at',)


@admin.register(KeyApproval)
class KeyApprovalAdmin(admin.ModelAdmin):
    """إدارة الموافقات"""
    
    list_display = ('title', 'approval_type', 'requester', 'approver',
                    'status', 'priority', 'requested_at')
    list_filter = ('approval_type', 'status', 'priority', 'requested_at')
    search_fields = ('title', 'description')
    ordering = ('-requested_at',)


@admin.register(KeyShipment)
class KeyShipmentAdmin(admin.ModelAdmin):
    """إدارة الشحنات"""
    
    list_display = ('shipment_number', 'shipment_type', 'status',
                    'sender', 'receiver', 'planned_dispatch_date')
    list_filter = ('shipment_type', 'status', 'transport_method')
    search_fields = ('shipment_number', 'tracking_number')
    ordering = ('-created_at',)


@admin.register(KeyInventoryMovement)
class KeyInventoryMovementAdmin(admin.ModelAdmin):
    """إدارة حركات المخزون"""
    
    list_display = ('movement_number', 'product', 'movement_type',
                    'quantity', 'warehouse', 'movement_date')
    list_filter = ('movement_type', 'movement_reason', 'warehouse',
                   'movement_date')
    search_fields = ('movement_number', 'product__product_name_ar')
    ordering = ('-movement_date',)


@admin.register(KeyNotification)
class KeyNotificationAdmin(admin.ModelAdmin):
    """إدارة الإشعارات"""
    
    list_display = ('title', 'notification_type', 'recipient', 'priority',
                    'is_read', 'created_at')
    list_filter = ('notification_type', 'priority', 'is_read', 'created_at')
    search_fields = ('title', 'message', 'recipient__arabic_name')
    ordering = ('-created_at',)


@admin.register(KeyAuditLog)
class KeyAuditLogAdmin(admin.ModelAdmin):
    """إدارة سجل العمليات"""
    
    list_display = ('user', 'operation_type', 'module', 'table_name',
                    'success', 'created_at')
    list_filter = ('operation_type', 'module', 'success', 'created_at')
    search_fields = ('user__arabic_name', 'operation_description', 'table_name')
    ordering = ('-created_at',)
    readonly_fields = ('created_at',)
    
    def has_add_permission(self, request):
        return False
    
    def has_change_permission(self, request, obj=None):
        return False
    
    def has_delete_permission(self, request, obj=None):
        return False


# تخصيص عنوان لوحة الإدارة
admin.site.site_header = "KamaVerse - نظام إدارة شركة القماش"
admin.site.site_title = "KamaVerse Admin"
admin.site.index_title = "لوحة التحكم الرئيسية"
