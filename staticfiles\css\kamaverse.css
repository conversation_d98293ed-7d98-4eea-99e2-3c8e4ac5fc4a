/* 
KamaVerse Inventory System - CSS Styles
نظام إدارة المخزون - شركة القماش للاستيراد والتصدير
*/

/* ===== CSS Variables - الباليته الجديدة ===== */
:root {
  /* Core Palette (Brand) */
  --brand-red: #D62828;
  --brand-red-dark: #8B1116;
  --brand-red-light: #FCE8E8;

  --brand-gold: #C89A3C;
  --brand-gold-light: #F4D488;
  --brand-gold-dark: #8C6420;

  /* Neutrals (UI) */
  --ink: #1A1A1A;
  --slate: #4A4F57;
  --line: #E6E8ED;
  --canvas: #F7F8FB;
  --white: #FFFFFF;

  /* Accents */
  --accent-sand: #FFF3E0;
  --success: #2E7D32;
  --warning: #F39C12;
  --error: #C21807;

  /* Gradients */
  --gradient-red: linear-gradient(135deg, #D62828 0%, #8B1116 100%);
  --gradient-gold: linear-gradient(135deg, #F4D488 0%, #C89A3C 60%, #8C6420 100%);
  --gradient-soft: linear-gradient(180deg, #FFFFFF 0%, #F7F8FB 100%);
}

/* ===== Base Styles ===== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Red home icon in sidebar */
.sidebar-nav ul li a .fa-home {
  color: var(--brand-red) !important;
}

body {
  font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  color: var(--ink);
  background: var(--canvas);
  direction: rtl;
  text-align: right;
}

/* ===== Typography ===== */
h1, h2, h3, h4, h5, h6 {
  color: var(--ink);
  font-weight: 600;
  margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.1rem; }
h6 { font-size: 1rem; }

p {
  color: var(--slate);
  margin-bottom: 1rem;
}

/* ===== Layout ===== */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.col {
  flex: 1;
  padding: 0 15px;
}

/* ===== Header ===== */
.main-header {
  background: var(--white);
  border-bottom: 1px solid var(--line);
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo-section img {
  height: 50px;
  width: auto;
}

.logo-section h1 {
  color: var(--brand-red);
  font-size: 1.8rem;
  margin: 0;
}

.user-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.notifications {
  position: relative;
  cursor: pointer;
}

.notification-icon {
  width: 24px;
  height: 24px;
  background: var(--brand-gold);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 14px;
}

.notification-count {
  position: absolute;
  top: -8px;
  left: -8px;
  background: var(--brand-red);
  color: var(--white);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* ===== Buttons ===== */
.btn {
  display: inline-block;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.btn-primary {
  background: var(--brand-red);
  color: var(--white);
}

.btn-primary:hover {
  background: var(--brand-red-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(214, 40, 40, 0.3);
}

.btn-secondary {
  background: var(--white);
  color: var(--brand-gold-dark);
  border: 2px solid var(--brand-gold);
}

.btn-secondary:hover {
  background: var(--brand-gold-light);
  border-color: var(--brand-gold-dark);
}

.btn-success {
  background: var(--success);
  color: var(--white);
}

.btn-warning {
  background: var(--warning);
  color: var(--white);
}

.btn-error {
  background: var(--error);
  color: var(--white);
}

/* ===== Cards ===== */
.card {
  background: var(--white);
  border: 1px solid var(--line);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
}

.card-header {
  border-bottom: 1px solid var(--line);
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.card-title {
  color: var(--ink);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

/* ===== Dashboard Stats ===== */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: var(--white);
  border: 1px solid var(--line);
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: var(--white);
}

.stat-icon.inventory { background: var(--brand-gold); }
.stat-icon.warehouse { background: var(--brand-red); }
.stat-icon.alerts { background: var(--warning); }
.stat-icon.value { background: var(--success); }

.stat-content h3 {
  color: var(--slate);
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.stat-number {
  color: var(--ink);
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.stat-change {
  font-size: 12px;
  font-weight: 600;
  margin-top: 4px;
}

.stat-change.positive { color: var(--success); }
.stat-change.negative { color: var(--error); }

/* ===== Navigation ===== */
.nav-tabs {
  display: flex;
  list-style: none;
  border-bottom: 1px solid var(--line);
  margin-bottom: 24px;
}

.nav-item {
  margin-left: 32px;
}

.nav-item a {
  display: block;
  padding: 12px 0;
  color: var(--slate);
  text-decoration: none;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.nav-item.active a,
.nav-item a:hover {
  color: var(--brand-red);
  border-bottom-color: var(--brand-red);
}

/* ===== Forms ===== */
.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  color: var(--ink);
  font-weight: 600;
  margin-bottom: 8px;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--line);
  border-radius: 8px;
  font-size: 14px;
  color: var(--ink);
  background: var(--white);
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--brand-red);
  box-shadow: 0 0 0 3px rgba(214, 40, 40, 0.1);
}

/* ===== Tables ===== */
.table-container {
  background: var(--white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 16px;
  text-align: right;
  border-bottom: 1px solid var(--line);
}

.table th {
  background: var(--canvas);
  color: var(--ink);
  font-weight: 600;
}

.table tbody tr:hover {
  background: var(--accent-sand);
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .nav-tabs {
    flex-direction: column;
  }
  
  .nav-item {
    margin: 0;
    margin-bottom: 8px;
  }
}

/* ===== Utilities ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }
