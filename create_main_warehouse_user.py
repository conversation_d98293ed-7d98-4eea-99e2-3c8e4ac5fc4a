#!/usr/bin/env python
"""
إضافة مستخدم جديد للمخزن الرئيسي - أنس مجدي
Add new user for Main Warehouse - Anas Magdy
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

from django.contrib.auth.models import User
from inventory.models import UserProfile, Warehouse

def create_main_warehouse_user():
    """إنشاء مستخدم للمخزن الرئيسي"""
    print("🚀 بدء إنشاء مستخدم المخزن الرئيسي...")
    
    try:
        # الحصول على المخزن الرئيسي
        main_warehouse = Warehouse.objects.get(warehouse_code='15054')
        print(f"✅ تم العثور على المخزن الرئيسي: {main_warehouse.warehouse_name}")
        
        # بيانات المستخدم الجديد
        user_data = {
            'username': 'main_manager',
            'email': '<EMAIL>',
            'first_name': 'أنس',
            'last_name': 'مجدي',
            'password': 'main123',
            'role': 'مسؤول المخزن الرئيسي',
            'phone': '01234567895',
            'department': 'المخزن الرئيسي'
        }
        
        # إنشاء المستخدم
        user, created = User.objects.get_or_create(
            username=user_data['username'],
            defaults={
                'email': user_data['email'],
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name'],
                'is_staff': True,  # يمكنه الدخول للـ admin
                'is_active': True,
            }
        )
        
        if created:
            user.set_password(user_data['password'])
            user.save()
            print(f"✅ تم إنشاء المستخدم: {user_data['first_name']} {user_data['last_name']} ({user_data['username']})")
        else:
            print(f"ℹ️ المستخدم موجود بالفعل: {user_data['username']}")
        
        # إنشاء ملف المستخدم (UserProfile)
        profile, profile_created = UserProfile.objects.get_or_create(
            user=user,
            defaults={
                'role': 'WAREHOUSE_MANAGER',
                'assigned_warehouse': main_warehouse,
                'phone_number': user_data['phone'],
                'department': user_data['department'],
                'is_active': True,
            }
        )
        
        if profile_created:
            print(f"✅ تم إنشاء ملف المستخدم: {profile.user.get_full_name()} - {main_warehouse.warehouse_name}")
        else:
            print(f"ℹ️ ملف المستخدم موجود بالفعل: {profile.user.get_full_name()}")
        
        # عرض معلومات المستخدم الجديد
        print("\n📋 معلومات المستخدم الجديد:")
        print("=" * 50)
        print(f"👤 الاسم: {user.get_full_name()}")
        print(f"📧 البريد الإلكتروني: {user.email}")
        print(f"🔑 اسم المستخدم: {user.username}")
        print(f"🔒 كلمة المرور: {user_data['password']}")
        print(f"📞 الهاتف: {profile.phone_number}")
        print(f"🏢 القسم: {profile.department}")
        print(f"🏭 المخزن المخصص: {main_warehouse.warehouse_name}")
        print(f"👔 الدور: {profile.get_role_display()}")
        print(f"✅ الحالة: {'نشط' if profile.is_active else 'غير نشط'}")
        
        # اختبار الصلاحيات
        print(f"\n🔐 اختبار الصلاحيات:")
        accessible_warehouses = profile.get_accessible_warehouses()
        print(f"📦 يمكنه الوصول إلى {accessible_warehouses.count()} مخزن:")
        for warehouse in accessible_warehouses:
            print(f"   - {warehouse.warehouse_name} ({warehouse.warehouse_code})")
        
        print(f"\n🎉 تم إنشاء المستخدم بنجاح للمخزن الرئيسي!")
        
    except Warehouse.DoesNotExist:
        print("❌ خطأ: لم يتم العثور على المخزن الرئيسي")
        print("المخازن المتاحة:")
        for warehouse in Warehouse.objects.filter(is_active=True):
            print(f"   - {warehouse.warehouse_name} ({warehouse.warehouse_code})")
    
    except Exception as e:
        print(f"❌ خطأ أثناء إنشاء المستخدم: {str(e)}")
        import traceback
        traceback.print_exc()

def show_all_users():
    """عرض جميع المستخدمين والمخازن المخصصة لهم"""
    print("\n📊 ملخص جميع المستخدمين:")
    print("=" * 70)
    
    for profile in UserProfile.objects.all().select_related('user', 'assigned_warehouse'):
        warehouse_name = profile.assigned_warehouse.warehouse_name if profile.assigned_warehouse else 'جميع المخازن'
        print(f"👤 {profile.user.get_full_name()}")
        print(f"   اسم المستخدم: {profile.user.username}")
        print(f"   الدور: {profile.get_role_display()}")
        print(f"   المخزن: {warehouse_name}")
        print(f"   الهاتف: {profile.phone_number}")
        print()

if __name__ == '__main__':
    create_main_warehouse_user()
    show_all_users()