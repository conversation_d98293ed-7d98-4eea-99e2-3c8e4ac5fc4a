{% extends 'base.html' %}
{% load static %}
{% load inventory_filters %}

{% block title %}تقرير الأصناف بالكميات - KamaVerse{% endblock %}

{% block extra_css %}
<style>
    :root {
        --brand-red: #D62828;
        --brand-red-dark: #8B1116;
        --brand-red-light: #FCE8E8;
        --brand-gold: #C89A3C;
        --brand-gold-light: #F4D488;
        --brand-gold-dark: #8C6420;
        --ink: #1A1A1A;
        --slate: #4A4F57;
        --line: #E6E8ED;
        --canvas: #F7F8FB;
        --white: #FFFFFF;
        --success: #2E7D32;
        --warning: #F39C12;
        --error: #C21807;
    }

    .page-container {
        background: linear-gradient(180deg, #FFFFFF 0%, #F7F8FB 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .page-header {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    .page-header h1 {
        color: var(--ink);
        font-weight: 700;
        font-size: 2.5rem;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .page-header .icon {
        color: var(--brand-gold);
        font-size: 3rem;
    }

    .page-header p {
        color: var(--slate);
        font-size: 1.1rem;
        margin: 1rem 0 0 0;
    }

    .filters-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    .filters-title {
        color: var(--ink);
        font-weight: 600;
        font-size: 1.3rem;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filter-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .form-group label {
        color: var(--ink);
        font-weight: 600;
        font-size: 0.95rem;
    }

    .form-control {
        padding: 0.75rem 1rem;
        border: 1px solid var(--line);
        border-radius: 12px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--brand-gold);
        box-shadow: 0 0 0 3px rgba(200, 154, 60, 0.1);
    }

    .btn-primary {
        background: var(--brand-red);
        color: var(--white);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary:hover {
        background: var(--brand-red-dark);
        transform: translateY(-2px);
    }

    .btn-secondary {
        background: var(--slate);
        color: var(--white);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        text-decoration: none;
    }

    .btn-secondary:hover {
        background: var(--ink);
        text-decoration: none;
        color: var(--white);
    }

    .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .summary-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
        position: relative;
        overflow: hidden;
    }

    .summary-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, var(--brand-gold) 0%, var(--brand-gold-dark) 100%);
    }

    .summary-content {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .summary-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.3rem;
        color: var(--brand-gold);
        background: var(--brand-gold-light);
    }

    .summary-info h3 {
        color: var(--ink);
        font-weight: 600;
        font-size: 1rem;
        margin: 0 0 0.5rem 0;
    }

    .summary-info .value {
        color: var(--brand-red);
        font-weight: 700;
        font-size: 1.8rem;
        margin: 0;
    }

    .items-table-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
        margin-bottom: 2rem;
    }

    .table-title {
        color: var(--ink);
        font-weight: 600;
        font-size: 1.3rem;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .table-responsive {
        overflow-x: auto;
    }

    .items-table {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid var(--line);
        border-radius: 12px;
        overflow: hidden;
        table-layout: fixed;
    }

    .items-table th {
        background: var(--canvas);
        color: var(--ink);
        font-weight: 600;
        padding: 1rem;
        text-align: right;
        border-bottom: 1px solid var(--line);
        border-left: 1px solid var(--line);
        border-right: 1px solid var(--line);
    }
    
    /* توسيط عنوان المخازن */
    .items-table th:nth-child(4) {
        text-align: center;
    }
    
    /* تحديد عرض الأعمدة لتناسب المخازن بشكل أفضل */
    .items-table th:nth-child(1) { width: 25%; } /* الصنف */
    .items-table th:nth-child(2) { width: 15%; } /* إجمالي الكمية */
    .items-table th:nth-child(3) { width: 12%; } /* عدد المواقع */
    .items-table th:nth-child(4) { width: 28%; } /* المخازن - مساحة أكبر */
    .items-table th:nth-child(5) { width: 20%; } /* آخر حركة */

    .items-table td {
        padding: 1rem;
        border-bottom: 1px solid var(--line);
        border-left: 1px solid var(--line);
        border-right: 1px solid var(--line);
        color: var(--slate);
    }

    .items-table tbody tr:hover {
        background: var(--canvas);
    }

    /* Styling for zero stock items */
    .items-table tbody tr.zero-stock {
        background-color: #f8f9fa;
    }

    .items-table tbody tr.zero-stock td {
        color: #6c757d;
    }

    .items-table tbody tr.zero-stock .item-name {
        color: #495057;
    }

    .items-table tbody tr.zero-stock .item-code {
        color: #868e96;
    }

    .quantity-badge.zero {
        background: #e9ecef;
        color: #6c757d;
    }

    .location-count.zero {
        background: #e9ecef;
        color: #6c757d;
    }

    .item-name {
        color: var(--ink);
        font-weight: 600;
    }

    .item-code {
        color: var(--brand-gold);
        font-weight: 500;
        font-size: 0.9rem;
    }

    .quantity-badge {
        background: var(--brand-gold-light);
        color: var(--brand-gold-dark);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .location-count {
        background: var(--brand-red-light);
        color: var(--brand-red-dark);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
    }

    .date-text {
        color: var(--slate);
        font-size: 0.9rem;
    }

    /* تنسيق خاص لخانة المخازن */
    .warehouses-container {
        display: flex;
        flex-direction: column;
        gap: 0.3rem;
        text-align: center;
    }
    
    .warehouses-container div {
        margin-bottom: 0 !important;
        font-size: 0.85rem;
        line-height: 1.4;
        padding: 0.2rem 0.5rem;
        background-color: transparent;
        color: var(--slate);
    }
    
    /* للأصناف ذات الكمية صفر */
    .zero-stock .warehouses-container div {
        background-color: transparent;
        color: #6c757d;
    }

    .no-data {
        text-align: center;
        padding: 3rem;
        color: var(--slate);
    }

    .no-data i {
        font-size: 3rem;
        color: var(--line);
        margin-bottom: 1rem;
    }

    .print-button {
        background: var(--success);
        color: var(--white);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .print-button:hover {
        background: #1B5E20;
        transform: translateY(-2px);
    }

    @media print {
        /* Hide all elements except the page title and items table */
        .no-print,
        .breadcrumb,
        .print-button,
        .btn-primary,
        .filters-card,
        .summary-cards,
        .table-title,
        header,
        nav,
        footer,
        .user-menu,
        .nav-menu,
        .sidebar,
        .sidebar-container,
        .navbar,
        .navbar-brand,
        .user-profile {
            display: none !important;
        }
        
        /* Reset page container for print */
        .page-container {
            background: white;
            padding: 0;
            margin: 0;
            position: absolute;
            top: 0;
            left: 0;
        }
        
        /* Hide top navigation bar and header completely */
        body > header,
        body > .navbar,
        body > .navbar-fixed-top,
        body > .header-container,
        body > .nav-container,
        body > .nav-wrapper {
            display: none !important;
        }
        
        /* Keep only the page header with title */
        .page-header {
            border: none;
            box-shadow: none;
            padding: 1rem 0;
            background: white;
            margin-bottom: 1rem;
            position: relative;
            top: 0;
        }
        
        .page-header h1 {
            font-size: 1.8rem;
            margin: 0;
        }
        
        .page-header p,
        .page-header div {
            display: none;
        }
        
        /* Style the items table for print */
        .items-table-card {
            border: none;
            box-shadow: none;
            padding: 0;
            margin: 0;
        }
        
        /* Ensure table is properly formatted for print */
        .items-table {
            border: 1px solid #000;
        }
        
        .items-table th {
            background: #f0f0f0;
            border: 1px solid #000;
        }
        
        .items-table td {
            border: 1px solid #000;
        }
        
        /* Ensure table fits on page */
        .table-responsive {
            overflow-x: visible;
        }
        
        /* تعديل عرض أعمدة الجدول للطباعة */
        .items-table {
            width: 100%;
            table-layout: fixed;
            border-collapse: collapse;
        }
        
        .items-table th:nth-child(1) { width: 25%; } /* الصنف */
        .items-table th:nth-child(2) { width: 15%; } /* إجمالي الكمية */
        .items-table th:nth-child(3) { width: 15%; } /* عدد المواقع */
        .items-table th:nth-child(4) { width: 25%; } /* المخازن - مساحة أكبر */
        .items-table th:nth-child(5) { width: 20%; } /* آخر حركة */
        
        /* إزالة شريط التمرير من خانة المخازن عند الطباعة */
        .warehouses-container {
            max-height: none !important;
            overflow-y: visible !important;
            display: block !important;
            gap: 0 !important;
            text-align: right !important;
        }
        
        /* تنسيق خاص للمخازن في الطباعة */
        .warehouses-container div {
            margin-bottom: 0.25rem !important;
            font-size: 10pt !important;
            line-height: 1.2 !important;
            border-left: none !important;
            background-color: transparent !important;
            padding: 0 !important;
        }
    }

    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2rem;
        }
        
        .filter-row {
            grid-template-columns: 1fr;
        }
        
        .summary-cards {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .table-responsive {
            font-size: 0.85rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="container-fluid">
        <!-- مسار التنقل -->
        <nav aria-label="breadcrumb" class="no-print">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:dashboard' %}">الصفحة الرئيسية</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:reports_main_dashboard' %}">التقارير</a>
                </li>
                <li class="breadcrumb-item active">تقرير الأصناف بالكميات</li>
            </ol>
        </nav>

        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1>
                <i class="fas fa-cubes icon"></i>
                تقرير الأصناف بالكميات المحسن
            </h1>
            <p>عرض شامل لجميع الأصناف مع كمياتها الحالية في جميع المخازن والمواقع - تحديث يومي تلقائي</p>
            <div style="margin-top: 1rem; color: var(--slate); font-size: 0.95rem;">
                <i class="fas fa-calendar"></i>
                تاريخ التقرير: {{ report_date|date:"Y/m/d" }}
            </div>
        </div>

        <!-- أزرار الطباعة والتصدير -->
        <div class="no-print" style="margin-bottom: 2rem;">
            <button onclick="window.print()" class="print-button">
                <i class="fas fa-print"></i>
                طباعة التقرير
            </button>
        </div>

        <!-- الفلاتر -->
        <div class="filters-card no-print">
            <div class="filters-title">
                <i class="fas fa-filter"></i>
                فلاتر البحث والتصفية
            </div>
            
            <form method="GET" action="">
                <div class="filter-row">
                    <div class="form-group">
                        <label for="search">البحث في الأصناف</label>
                        <input type="text" 
                               id="search" 
                               name="search" 
                               class="form-control" 
                               placeholder="اسم الصنف أو الكود"
                               value="{{ search_query }}">
                    </div>
                    
                    <div class="form-group">
                        <label for="warehouse">المخزن</label>
                        <select id="warehouse" name="warehouse" class="form-control">
                            <option value="">جميع المخازن</option>
                            {% for warehouse in warehouses %}
                                <option value="{{ warehouse.id }}" 
                                        {% if warehouse_filter == warehouse.id|stringformat:"s" %}selected{% endif %}>
                                    {{ warehouse.warehouse_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="category">الفئة</label>
                        <select id="category" name="category" class="form-control">
                            <option value="">جميع الفئات</option>
                            {% for value, label in categories %}
                                <option value="{{ value }}" 
                                        {% if category_filter == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="date_filter">فترة التحديث</label>
                        <select id="date_filter" name="date_filter" class="form-control">
                            <option value="all" {% if date_filter == 'all' %}selected{% endif %}>جميع الفترات</option>
                            <option value="today" {% if date_filter == 'today' %}selected{% endif %}>اليوم</option>
                            <option value="week" {% if date_filter == 'week' %}selected{% endif %}>آخر أسبوع</option>
                            <option value="month" {% if date_filter == 'month' %}selected{% endif %}>آخر شهر</option>
                        </select>
                    </div>
                </div>
                
                <div class="d-flex gap-2 flex-wrap">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                    <a href="{% url 'inventory:enhanced_items_quantity_report' %}" class="btn-secondary">
                        <i class="fas fa-times"></i>
                        إزالة الفلاتر
                    </a>
                </div>
            </form>
        </div>

        <!-- الإحصائيات العامة -->
        <div class="summary-cards" style="grid-template-columns: 1fr;">
            <div class="summary-card">
                <div class="summary-content">
                    <div class="summary-icon">
                        <i class="fas fa-cubes"></i>
                    </div>
                    <div class="summary-info">
                        <h3>إجمالي الأصناف</h3>
                        <p class="value">{{ summary_data.total_items|floatformat:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الأصناف -->
        <div class="items-table-card">
            <div class="table-title">
                <i class="fas fa-table"></i>
                تفاصيل الأصناف والكميات
            </div>
            
            {% if items_summary %}
                <div class="table-responsive">
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>الصنف</th>
                                <th>إجمالي الكمية</th>
                                <th>عدد المواقع</th>
                                <th>المخازن</th>
                                <th>آخر حركة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item_code, item_data in items_summary.items %}
                                <tr{% if item_data.total_quantity == 0 %} class="zero-stock"{% endif %}>
                                    <td>
                                        <div class="item-name">{{ item_data.item.item_name_ar }}</div>
                                        <div class="item-code">{{ item_data.item.item_code }}</div>
                                    </td>
                                    <td>
                                        {% if item_data.total_quantity > 0 %}
                                        <span class="quantity-badge">
                                            {{ item_data.total_quantity|floatformat:0 }} {{ item_data.item.unit_of_measure }}
                                        </span>
                                        {% else %}
                                        <span class="quantity-badge zero">
                                            0 {{ item_data.item.unit_of_measure }}
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if item_data.locations_count > 0 %}
                                        <span class="location-count">
                                            {{ item_data.locations_count }} موقع
                                        </span>
                                        {% else %}
                                        <span class="location-count zero">
                                            0 موقع
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="warehouses-container">
                                            {% if item_data.warehouses %}
                                                {% regroup item_data.warehouses by warehouse.warehouse_name as warehouse_list %}
                                                {% for warehouse in warehouse_list %}
                                                    <div>
                                                        {{ warehouse.grouper }}
                                                    </div>
                                                {% endfor %}
                                            {% else %}
                                                <div style="color: #6c757d;">لا يوجد مخزون</div>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if item_data.last_movement %}
                                            <span class="date-text">{{ item_data.last_movement|date:"Y/m/d H:i" }}</span>
                                        {% else %}
                                            <span style="color: var(--slate);">لا توجد حركات</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="no-data">
                    <div>
                        <i class="fas fa-inbox"></i>
                    </div>
                    <h3>لا توجد أصناف</h3>
                    <p>لا توجد أصناف تطابق معايير البحث المحددة</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// تحديث تلقائي للصفحة كل دقيقة لعرض أحدث البيانات
setInterval(function() {
    window.location.reload();
}, 60000);
</script>
{% endblock %}