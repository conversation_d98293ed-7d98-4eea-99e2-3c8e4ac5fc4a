/*
 * Ka<PERSON>Verse Items Management Styles
 * تصميم صفحة إدارة الأصناف - Core Brand Palette
 */

/* CSS Variables - Brand Palette */
:root {
  --brand-red: #D62828;
  --brand-red-dark: #8B1116;
  --brand-red-light: #FCE8E8;

  --brand-gold: #C89A3C;
  --brand-gold-light: #F4D488;
  --brand-gold-dark: #8C6420;

  --ink: #1A1A1A;
  --slate: #4A4F57;
  --line: #E6E8ED;
  --canvas: #F7F8FB;
  --white: #FFFFFF;

  --accent-sand: #FFF3E0;
  --success: #2E7D32;
  --warning: #F39C12;
  --error: #C21807;
}

/* Header Styles */
.items-header {
    background: linear-gradient(135deg, var(--brand-red) 0%, var(--brand-red-dark) 100%);
    color: var(--white);
    padding: 2rem 0;
    margin: -1rem -15px 2rem -15px;
    border-radius: 0 0 15px 15px;
    box-shadow: 0 4px 20px rgba(214, 40, 40, 0.3);
}

.items-header h1 {
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--white);
}

.items-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    color: var(--white);
}

/* Button Styles */
.btn-add-item {
    background: var(--brand-red);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    color: var(--white);
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(214, 40, 40, 0.2);
}

.btn-add-item:hover {
    background: var(--brand-red-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(214, 40, 40, 0.3);
    color: var(--white);
    text-decoration: none;
}

/* Stats Cards */
.stats-cards {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.stat-card {
    background: var(--white);
    padding: 1.5rem;
    border-radius: 12px;
    border-left: 4px solid var(--brand-gold);
    flex: 1;
    min-width: 200px;
    box-shadow: 0 2px 10px rgba(26, 26, 26, 0.08);
    transition: all 0.3s ease;
    border: 1px solid var(--line);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(200, 154, 60, 0.15);
    border-left-color: var(--brand-red);
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--ink);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--slate);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Search and Filters */
.search-filters {
    background: var(--canvas);
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    border: 1px solid var(--line);
    box-shadow: 0 2px 8px rgba(26, 26, 26, 0.05);
}

.search-filters .form-label {
    font-weight: 600;
    color: var(--ink);
    margin-bottom: 0.5rem;
}

.search-filters .form-control,
.search-filters .form-select {
    border-radius: 8px;
    border: 1px solid var(--line);
    padding: 0.75rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    background: var(--white);
    color: var(--slate);
}

.search-filters .form-control:focus,
.search-filters .form-select:focus {
    border-color: var(--brand-gold);
    box-shadow: 0 0 0 0.2rem rgba(200, 154, 60, 0.25);
    outline: none;
}

/* Table Styles */
.items-table {
    background: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(26, 26, 26, 0.08);
    border: 1px solid var(--line);
}

.table {
    margin-bottom: 0;
}

.table th {
    background: var(--ink);
    color: var(--white);
    border: none;
    font-weight: 600;
    padding: 1rem 0.75rem;
    text-align: center;
    white-space: nowrap;
    position: relative;
}

.table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--brand-gold);
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--line);
    text-align: center;
    color: var(--slate);
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: var(--accent-sand);
    transform: scale(1.01);
}

/* Badge Styles */
.badge-category {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    border-radius: 8px;
    font-weight: 600;
    background: var(--brand-gold-light);
    color: var(--brand-gold-dark);
    border: 1px solid var(--brand-gold);
}

.badge-hazard-none {
    background-color: var(--success);
    color: var(--white);
    border: 1px solid var(--success);
}

.badge-hazard-low {
    background-color: var(--brand-gold-light);
    color: var(--brand-gold-dark);
    border: 1px solid var(--brand-gold);
}

.badge-hazard-medium {
    background-color: var(--warning);
    color: var(--white);
    border: 1px solid var(--warning);
}

.badge-hazard-high {
    background-color: var(--brand-red-light);
    color: var(--brand-red-dark);
    border: 1px solid var(--brand-red);
}

.badge-hazard-critical {
    background-color: var(--error);
    color: var(--white);
    border: 1px solid var(--error);
}

/* Action Buttons */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.btn-outline-primary {
    border-color: var(--brand-gold);
    color: var(--brand-gold-dark);
}

.btn-outline-primary:hover {
    background-color: var(--brand-gold);
    border-color: var(--brand-gold);
    color: var(--white);
}

.btn-outline-warning {
    border-color: var(--warning);
    color: var(--warning);
}

.btn-outline-warning:hover {
    background-color: var(--warning);
    border-color: var(--warning);
    color: var(--white);
}

.btn-outline-danger {
    border-color: var(--error);
    color: var(--error);
}

.btn-outline-danger:hover {
    background-color: var(--error);
    border-color: var(--error);
    color: var(--white);
}

/* Secondary Buttons */
.btn-outline-secondary {
    background: var(--white);
    border: 1px solid var(--brand-gold);
    color: var(--brand-gold-dark);
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background: var(--brand-gold-light);
    border-color: var(--brand-gold);
    color: var(--brand-gold-dark);
    transform: translateY(-1px);
}

/* Pagination */
.pagination {
    justify-content: center;
    margin-top: 2rem;
}

.page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 1px solid var(--line);
    color: var(--slate);
    padding: 0.5rem 0.75rem;
    transition: all 0.2s ease;
    background: var(--white);
}

.page-link:hover {
    background-color: var(--brand-red);
    border-color: var(--brand-red);
    color: var(--white);
    transform: translateY(-1px);
}

.page-item.active .page-link {
    background-color: var(--brand-red);
    border-color: var(--brand-red);
    color: var(--white);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--slate);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
    color: var(--brand-gold);
}

.empty-state h4 {
    margin-bottom: 1rem;
    color: var(--ink);
}

.empty-state p {
    margin-bottom: 1.5rem;
    color: var(--slate);
}

/* Item Code Styling */
.item-code {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: var(--brand-red);
    background: var(--brand-red-light);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid var(--brand-red);
}

/* Status Badges */
.status-active {
    background: var(--success);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-inactive {
    background: var(--slate);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stats-cards {
        flex-direction: column;
    }
    
    .stat-card {
        min-width: auto;
    }
    
    .items-header {
        text-align: center;
        padding: 1.5rem 0;
    }
    
    .items-header .col-md-4 {
        margin-top: 1rem;
    }
    
    .search-filters .row {
        gap: 1rem 0;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
    }
}

@media (max-width: 576px) {
    .btn-add-item {
        width: 100%;
        justify-content: center;
    }
    
    .stats-cards {
        gap: 0.5rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(214, 40, 40, 0.3);
    border-radius: 50%;
    border-top-color: var(--brand-red);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Hover Effects */
.table tbody tr:hover .item-code {
    background: var(--brand-red);
    color: var(--white);
    transform: scale(1.05);
}

.table tbody tr:hover .badge-category {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(200, 154, 60, 0.3);
}

/* Gold Accents */
.gold-accent {
    color: var(--brand-gold);
}

.gold-border {
    border-color: var(--brand-gold) !important;
}

.gold-bg {
    background-color: var(--brand-gold-light);
    color: var(--brand-gold-dark);
}

/* Premium Card Effect */
.premium-card {
    position: relative;
    overflow: hidden;
}

.premium-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-gold);
}

.premium-card:hover::before {
    height: 4px;
    background: var(--gradient-red);
}

/* Tooltip Styles */
.tooltip-inner {
    background-color: #495057;
    color: white;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

/* Focus Styles for Accessibility */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Quantity Badge */
.quantity-badge {
    background: var(--brand-red);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
    display: inline-block;
    min-width: 50px;
    text-align: center;
    transition: all 0.2s ease;
}

.quantity-badge:hover {
    background: var(--brand-red-dark);
    transform: scale(1.05);
}

/* Print Styles */
@media print {
    .items-header,
    .search-filters,
    .pagination,
    .btn-group {
        display: none !important;
    }

    .items-table {
        box-shadow: none;
        border: 1px solid #000;
    }

    .table th {
        background: #f8f9fa !important;
        color: #000 !important;
    }
}

/* تحسينات أزرار التصدير والطباعة */
.export-btn {
    transition: all 0.3s ease;
    font-weight: 500;
    border-width: 2px;
    padding: 0.5rem 1rem;
}

.export-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-outline-success.export-btn:hover {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

#printBtn {
    transition: all 0.3s ease;
    font-weight: 500;
    border-width: 2px;
    padding: 0.5rem 1rem;
}

#printBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* تحسينات إضافية للأزرار */
.btn-group .btn {
    margin-left: 2px;
    margin-right: 2px;
}

.export-btn i, #printBtn i {
    font-size: 1.1em;
}

/* تحسين مظهر الأزرار عند التركيز */
.export-btn:focus, #printBtn:focus {
    box-shadow: 0 0 0 0.2rem rgba(214, 40, 40, 0.25);
}

.btn-outline-success.export-btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}
