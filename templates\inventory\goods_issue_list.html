{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if issue_type == 'SALE' %}
فواتير المبيعات - KamaVerse
{% else %}
فواتير التلف - KamaVerse
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, var(--brand-red), #FF6B6B);
        color: var(--white);
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .page-header h1 {
        font-weight: 700;
        margin: 0;
        color: var(--white);
    }

    .btn-back {
        background: rgba(255, 255, 255, 0.2);
        color: var(--white);
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-back:hover {
        background: rgba(255, 255, 255, 0.3);
        color: var(--white);
        text-decoration: none;
    }

    .search-filters {
        background: var(--white);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border: 1px solid var(--line);
    }

    .filters-row {
        display: flex;
        gap: 1rem;
        align-items: end;
    }

    .form-group {
        flex: 1;
    }

    .form-label {
        font-weight: 600;
        color: var(--charcoal);
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid var(--line);
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--brand-red);
        box-shadow: 0 0 0 3px rgba(214, 40, 40, 0.1);
    }

    .btn-search {
        background: var(--brand-red);
        color: var(--white);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-search:hover {
        background: var(--brand-red-dark);
    }

    .issues-table {
        background: var(--white);
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        border: 1px solid var(--line);
    }

    .table {
        margin: 0;
    }

    .table thead th {
        background: var(--brand-red);
        color: var(--white);
        font-weight: 600;
        border: none;
        padding: 1rem;
    }

    .table tbody td {
        padding: 1rem;
        vertical-align: middle;
        border-top: 1px solid var(--line);
    }

    .table tbody tr:hover {
        background: rgba(214, 40, 40, 0.05);
    }

    .issue-number {
        font-weight: 600;
        color: var(--brand-red);
    }

    .issue-type-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .badge-sale {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .badge-damage {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .status-confirmed {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .status-draft {
        background: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
    }

    .btn-action {
        padding: 0.5rem;
        border-radius: 8px;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 35px;
        height: 35px;
    }

    .btn-view {
        background: rgba(0, 123, 255, 0.1);
        color: #007bff;
    }

    .btn-print {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: var(--slate);
    }

    .empty-state i {
        font-size: 4rem;
        color: var(--brand-gold);
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <h1>
            {% if issue_type == 'SALE' %}
                <i class="fas fa-shopping-cart me-2"></i>
                فواتير المبيعات
            {% else %}
                <i class="fas fa-exclamation-triangle me-2"></i>
                فواتير التلف
            {% endif %}
        </h1>
        <a href="{% url 'inventory:goods_issue_dashboard' %}" class="btn-back">
            <i class="fas fa-arrow-right"></i>
            العودة
        </a>
    </div>

    <!-- فلاتر البحث -->
    <div class="search-filters">
        <form method="get" class="filters-row">
            <div class="form-group">
                <label for="search" class="form-label">البحث</label>
                <input type="text" 
                       class="form-control" 
                       id="search" 
                       name="search" 
                       value="{{ search_query }}"
                       placeholder="البحث في رقم الإذن أو الملاحظات...">
            </div>
            <div class="form-group">
                <button type="submit" class="btn-search">
                    <i class="fas fa-search me-1"></i>
                    بحث
                </button>
            </div>
        </form>
    </div>

    {% if issues %}
    <!-- جدول الفواتير -->
    <div class="issues-table">
        <table class="table">
            <thead>
                <tr>
                    <th>رقم الإذن</th>
                    <th>النوع</th>
                    <th>التاريخ</th>
                    <th>عدد الأصناف</th>
                    <th>إجمالي الكمية</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for issue in issues %}
                <tr>
                    <td>
                        <span class="issue-number">{{ issue.issue_number }}</span>
                    </td>
                    <td>
                        <span class="issue-type-badge {% if issue.issue_type == 'SALE' %}badge-sale{% else %}badge-damage{% endif %}">
                            {{ issue.get_issue_type_display }}
                        </span>
                    </td>
                    <td>{{ issue.issue_date|date:"Y/m/d H:i" }}</td>
                    <td>{{ issue.total_items }}</td>
                    <td>{{ issue.total_quantity|floatformat:0 }}</td>
                    <td>
                        <span class="status-badge status-{{ issue.status|lower }}">
                            {{ issue.get_status_display }}
                        </span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <a href="{% url 'inventory:goods_issue_detail' issue.pk %}" 
                               class="btn-action btn-view" 
                               title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{% url 'inventory:goods_issue_print' issue.pk %}" 
                               class="btn-action btn-print" 
                               title="طباعة">
                                <i class="fas fa-print"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- ترقيم الصفحات -->
    {% if issues.has_other_pages %}
    <div class="pagination-wrapper">
        <nav aria-label="ترقيم الصفحات">
            <ul class="pagination">
                {% if issues.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ issues.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">السابق</a>
                    </li>
                {% endif %}
                
                {% for num in issues.paginator.page_range %}
                    {% if issues.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% else %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if issues.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ issues.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
    {% endif %}

    {% else %}
    <!-- حالة فارغة -->
    <div class="empty-state">
        <i class="fas fa-clipboard-list"></i>
        <h3>
            {% if issue_type == 'SALE' %}
                لا توجد فواتير مبيعات حتى الآن
            {% else %}
                لا توجد فواتير تلف حتى الآن
            {% endif %}
        </h3>
        <p>
            {% if issue_type == 'SALE' %}
                ابدأ بإنشاء إذن صرف للمبيعات
            {% else %}
                ابدأ بإنشاء إذن صرف للتلف
            {% endif %}
        </p>
    </div>
    {% endif %}
</div>
{% endblock %}
