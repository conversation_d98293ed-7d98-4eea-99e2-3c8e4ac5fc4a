{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل إذن الصرف - {{ issue.issue_number }} - KamaVerse{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, var(--brand-red), #FF6B6B);
        color: var(--white);
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .page-header h1 {
        font-weight: 700;
        margin: 0;
        color: var(--white);
    }

    .btn-back {
        background: rgba(255, 255, 255, 0.2);
        color: var(--white);
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-back:hover {
        background: rgba(255, 255, 255, 0.3);
        color: var(--white);
        text-decoration: none;
    }

    .issue-info {
        background: var(--white);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(26, 26, 26, 0.08);
        border: 1px solid var(--line);
        margin-bottom: 2rem;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    .info-item {
        display: flex;
        flex-direction: column;
    }

    .info-label {
        font-weight: 600;
        color: var(--slate);
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .info-value {
        font-size: 1.1rem;
        color: var(--ink);
        font-weight: 500;
    }

    .issue-type-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        display: inline-block;
    }

    .badge-sale {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .badge-damage {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;
        display: inline-block;
    }

    .status-confirmed {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }

    .status-draft {
        background: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }

    .items-section {
        background: var(--white);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(26, 26, 26, 0.08);
        border: 1px solid var(--line);
        margin-bottom: 2rem;
    }

    .section-title {
        color: var(--brand-red);
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: var(--brand-gold);
    }

    .items-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 1rem;
    }

    .items-table th,
    .items-table td {
        padding: 1rem;
        text-align: right;
        border: 1px solid var(--line);
    }

    .items-table th {
        background: var(--brand-red);
        color: var(--white);
        font-weight: 600;
    }

    .items-table tbody tr:hover {
        background: rgba(214, 40, 40, 0.05);
    }

    .totals-section {
        background: var(--gray-50);
        border-radius: 12px;
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .totals-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
    }

    .totals-row:last-child {
        margin-bottom: 0;
        font-weight: 700;
        color: var(--brand-red);
        border-top: 1px solid var(--line);
        padding-top: 0.5rem;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }

    .btn-print {
        background: var(--brand-gold);
        color: var(--white);
        padding: 1rem 2rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 4px 15px rgba(200, 154, 60, 0.3);
    }

    .btn-print:hover {
        background: var(--brand-gold-dark);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(200, 154, 60, 0.4);
        color: var(--white);
        text-decoration: none;
    }

    .notes-section {
        background: var(--white);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(26, 26, 26, 0.08);
        border: 1px solid var(--line);
        margin-bottom: 2rem;
    }

    .notes-content {
        background: var(--gray-50);
        border-radius: 8px;
        padding: 1rem;
        color: var(--slate);
        line-height: 1.6;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <h1>
            <i class="fas fa-file-alt me-2"></i>
            تفاصيل إذن الصرف - {{ issue.issue_number }}
        </h1>
        <a href="{% url 'inventory:goods_issue_dashboard' %}" class="btn-back">
            <i class="fas fa-arrow-right"></i>
            العودة
        </a>
    </div>

    <!-- معلومات الإذن -->
    <div class="issue-info">
        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">رقم الإذن</span>
                <span class="info-value">{{ issue.issue_number }}</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">نوع الصرف</span>
                <span class="info-value">
                    <span class="issue-type-badge {% if issue.issue_type == 'SALE' %}badge-sale{% else %}badge-damage{% endif %}">
                        {{ issue.get_issue_type_display }}
                    </span>
                </span>
            </div>
            
            <div class="info-item">
                <span class="info-label">التاريخ</span>
                <span class="info-value">{{ issue.issue_date|date:"Y/m/d H:i" }}</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">المخزن</span>
                <span class="info-value">{{ issue.warehouse.warehouse_name }}</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">المسؤول</span>
                <span class="info-value">{{ issue.created_by.get_full_name|default:issue.created_by.username }}</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">الحالة</span>
                <span class="info-value">
                    <span class="status-badge status-{{ issue.status|lower }}">
                        {{ issue.get_status_display }}
                    </span>
                </span>
            </div>
        </div>
    </div>

    <!-- تفاصيل الأصناف -->
    <div class="items-section">
        <h3 class="section-title">
            <i class="fas fa-list"></i>
            تفاصيل الأصناف
        </h3>
        
        <table class="items-table">
            <thead>
                <tr>
                    <th>م</th>
                    <th>كود الصنف</th>
                    <th>اسم الصنف</th>
                    <th>الكمية</th>
                    <th>الوحدة</th>
                    <th>موقع التخزين</th>
                </tr>
            </thead>
            <tbody>
                {% for item in issue.issue_items.all %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ item.item.item_code }}</td>
                    <td>{{ item.item.item_name_ar }}</td>
                    <td>{{ item.quantity|floatformat:3 }}</td>
                    <td>{{ item.unit_of_measure }}</td>
                    <td>{{ item.bin_location.location_code|default:"-" }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" style="text-align: center; color: var(--slate);">
                        لا توجد أصناف مسجلة
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- المجاميع -->
        <div class="totals-section">
            <div class="totals-row">
                <span>عدد الأصناف:</span>
                <span>{{ issue.total_items }} صنف</span>
            </div>
            <div class="totals-row">
                <span>إجمالي الكمية:</span>
                <span>{{ issue.total_quantity|floatformat:3 }}</span>
            </div>
        </div>
    </div>

    <!-- الملاحظات -->
    {% if issue.notes %}
    <div class="notes-section">
        <h3 class="section-title">
            <i class="fas fa-sticky-note"></i>
            الملاحظات
        </h3>
        <div class="notes-content">
            {{ issue.notes|linebreaks }}
        </div>
    </div>
    {% endif %}

    <!-- أزرار الإجراءات -->
    <div class="action-buttons">
        <a href="{% url 'inventory:goods_issue_print' issue.pk %}" class="btn-print" target="_blank">
            <i class="fas fa-print"></i>
            طباعة الإذن
        </a>
    </div>
</div>
{% endblock %}
