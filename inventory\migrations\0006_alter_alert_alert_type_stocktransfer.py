# Generated by Django 5.2.5 on 2025-08-24 15:08

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0005_add_goods_issue_models'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='alert',
            name='alert_type',
            field=models.CharField(choices=[('LOW_STOCK', 'مخزون منخفض'), ('OUT_OF_STOCK', 'نفاد المخزون'), ('NEAR_EXPIRY', 'اقتراب انتهاء الصلاحية'), ('EXPIRED', 'منتهي الصلاحية'), ('SLOW_MOVING', 'بطيء الحركة'), ('WAREHOUSE_FULL', 'امتلاء المخزن'), ('ITEM_ADDED', 'إضافة صنف جديد'), ('ITEM_MODIFIED', 'تعديل صنف'), ('ITEM_DELETED', 'حذف صنف'), ('RECEIPT_CREATED', 'إذن استلام جديد'), ('ISSUE_CREATED', 'إذن صرف جديد'), ('TRANSFER_REQUESTED', 'طلب نقل مخزون'), ('TRANSFER_APPROVED', 'اعتماد نقل مخزون'), ('TRANSFER_REJECTED', 'رفض نقل مخزون'), ('TRANSFER_COMPLETED', 'إكمال نقل مخزون'), ('SHIPMENT_ARRIVED', 'وصول شحنة'), ('DAMAGE_DETECTED', 'اكتشاف تلف'), ('LOCATION_CHANGED', 'تغيير موقع تخزين'), ('WAREHOUSE_ADDED', 'إضافة مخزن جديد'), ('LOCATION_STATUS_CHANGED', 'تغيير حالة موقع التخزين')], max_length=30, verbose_name='نوع التنبيه'),
        ),
        migrations.CreateModel(
            name='StockTransfer',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('transfer_number', models.CharField(max_length=50, unique=True, verbose_name='رقم طلب النقل')),
                ('transfer_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, validators=[django.core.validators.MinValueValidator(0.001)], verbose_name='الكمية المطلوبة')),
                ('reason', models.CharField(max_length=200, verbose_name='سبب النقل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('status', models.CharField(choices=[('PENDING', 'في الانتظار'), ('APPROVED', 'معتمد'), ('REJECTED', 'مرفوض'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي')], default='PENDING', max_length=20, verbose_name='حالة الطلب')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الإكمال')),
                ('approval_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات الاعتماد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transfers', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('destination_bin_location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='incoming_transfers', to='inventory.binlocation', verbose_name='موقع التخزين الهدف')),
                ('destination_warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='incoming_transfers', to='inventory.warehouse', verbose_name='المخزن الهدف')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfers', to='inventory.itemmaster', verbose_name='الصنف')),
                ('requested_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='requested_transfers', to=settings.AUTH_USER_MODEL, verbose_name='طلب بواسطة')),
                ('source_bin_location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='outgoing_transfers', to='inventory.binlocation', verbose_name='موقع التخزين المصدر')),
                ('source_warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='outgoing_transfers', to='inventory.warehouse', verbose_name='المخزن المصدر')),
            ],
            options={
                'verbose_name': 'طلب نقل مخزون',
                'verbose_name_plural': 'طلبات نقل المخزون',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['transfer_number'], name='inventory_s_transfe_639a89_idx'), models.Index(fields=['status'], name='inventory_s_status_8e22d4_idx'), models.Index(fields=['source_warehouse'], name='inventory_s_source__f02fcd_idx'), models.Index(fields=['destination_warehouse'], name='inventory_s_destina_8bd927_idx'), models.Index(fields=['item'], name='inventory_s_item_id_92610e_idx'), models.Index(fields=['requested_by'], name='inventory_s_request_98afa8_idx'), models.Index(fields=['transfer_date'], name='inventory_s_transfe_b40a2a_idx')],
            },
        ),
    ]
