{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
    }
    
    .form-header {
        border-bottom: 2px solid #e3f2fd;
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    }
    
    .form-header h2 {
        color: #1976d2;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .form-row {
        display: flex;
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .form-group {
        flex: 1;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 600;
        color: #333;
    }
    
    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        font-size: 1rem;
        transition: border-color 0.3s;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #1976d2;
        box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
    }
    
    .form-check {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .form-check-input {
        width: auto;
    }
    
    .btn-group {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #e0e0e0;
    }
    
    .btn {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .btn-primary {
        background: #1976d2;
        color: white;
    }
    
    .btn-primary:hover {
        background: #1565c0;
        transform: translateY(-2px);
    }
    
    .btn-secondary {
        background: #757575;
        color: white;
    }
    
    .btn-secondary:hover {
        background: #616161;
    }
    
    .error-message {
        color: #d32f2f;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    
    .required {
        color: #d32f2f;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="form-container">
                <div class="form-header">
                    <h2>
                        <i class="fas fa-warehouse"></i>
                        {{ page_title }}
                    </h2>
                </div>
                
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <!-- معلومات أساسية -->
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.warehouse_code.id_for_label }}">
                                {{ form.warehouse_code.label }} <span class="required">*</span>
                            </label>
                            {{ form.warehouse_code }}
                            {% if form.warehouse_code.errors %}
                                {% for error in form.warehouse_code.errors %}
                                    <div class="error-message">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.warehouse_name.id_for_label }}">
                                {{ form.warehouse_name.label }} <span class="required">*</span>
                            </label>
                            {{ form.warehouse_name }}
                            {% if form.warehouse_name.errors %}
                                {% for error in form.warehouse_name.errors %}
                                    <div class="error-message">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.warehouse_type.id_for_label }}">
                                {{ form.warehouse_type.label }} <span class="required">*</span>
                            </label>
                            {{ form.warehouse_type }}
                            {% if form.warehouse_type.errors %}
                                {% for error in form.warehouse_type.errors %}
                                    <div class="error-message">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.total_capacity.id_for_label }}">
                                {{ form.total_capacity.label }} <span class="required">*</span>
                            </label>
                            {{ form.total_capacity }}
                            {% if form.total_capacity.errors %}
                                {% for error in form.total_capacity.errors %}
                                    <div class="error-message">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- معلومات الموقع -->
                    <div class="form-group">
                        <label for="{{ form.address.id_for_label }}">
                            {{ form.address.label }}
                        </label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            {% for error in form.address.errors %}
                                <div class="error-message">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.city.id_for_label }}">
                                {{ form.city.label }}
                            </label>
                            {{ form.city }}
                            {% if form.city.errors %}
                                {% for error in form.city.errors %}
                                    <div class="error-message">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.governorate.id_for_label }}">
                                {{ form.governorate.label }}
                            </label>
                            {{ form.governorate }}
                            {% if form.governorate.errors %}
                                {% for error in form.governorate.errors %}
                                    <div class="error-message">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- معلومات المدير -->
                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.warehouse_manager.id_for_label }}">
                                {{ form.warehouse_manager.label }}
                            </label>
                            {{ form.warehouse_manager }}
                            {% if form.warehouse_manager.errors %}
                                {% for error in form.warehouse_manager.errors %}
                                    <div class="error-message">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="{{ form.manager_phone.id_for_label }}">
                                {{ form.manager_phone.label }}
                            </label>
                            {{ form.manager_phone }}
                            {% if form.manager_phone.errors %}
                                {% for error in form.manager_phone.errors %}
                                    <div class="error-message">{{ error }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- إعدادات المخزن -->
                    <div class="form-row">
                        <div class="form-check">
                            {{ form.is_active }}
                            <label for="{{ form.is_active.id_for_label }}">
                                {{ form.is_active.label }}
                            </label>
                        </div>
                        
                        <div class="form-check">
                            {{ form.temperature_controlled }}
                            <label for="{{ form.temperature_controlled.id_for_label }}">
                                {{ form.temperature_controlled.label }}
                            </label>
                        </div>
                        
                        <div class="form-check">
                            {{ form.humidity_controlled }}
                            <label for="{{ form.humidity_controlled.id_for_label }}">
                                {{ form.humidity_controlled.label }}
                            </label>
                        </div>
                    </div>
                    
                    <!-- أزرار التحكم -->
                    <div class="btn-group">
                        <a href="{% url 'inventory:warehouses_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            {% if form_action == 'create' %}
                                إنشاء المخزن
                            {% else %}
                                حفظ التغييرات
                            {% endif %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
