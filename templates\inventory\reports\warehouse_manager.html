{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير مدير المخزن - KamaVerse{% endblock %}

{% block extra_css %}
<style>
    :root {
        --brand-red: #D62828;
        --brand-red-dark: #8B1116;
        --brand-red-light: #FCE8E8;
        --brand-gold: #C89A3C;
        --brand-gold-light: #F4D488;
        --brand-gold-dark: #8C6420;
        --ink: #1A1A1A;
        --slate: #4A4F57;
        --line: #E6E8ED;
        --canvas: #F7F8FB;
        --white: #FFFFFF;
        --success: #2E7D32;
        --warning: #F39C12;
        --error: #C21807;
    }

    .page-container {
        background: linear-gradient(180deg, #FFFFFF 0%, #F7F8FB 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .manager-header {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
        position: relative;
        overflow: hidden;
    }

    .manager-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(135deg, var(--brand-gold) 0%, var(--brand-gold-dark) 100%);
    }

    .manager-info {
        display: flex;
        align-items: center;
        gap: 2rem;
        margin-bottom: 1.5rem;
    }

    .manager-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--brand-gold-light);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: var(--brand-gold-dark);
    }

    .manager-details h1 {
        color: var(--ink);
        font-weight: 700;
        font-size: 2rem;
        margin: 0 0 0.5rem 0;
    }

    .warehouse-name {
        color: var(--brand-gold);
        font-weight: 600;
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
    }

    .manager-role {
        color: var(--slate);
        font-size: 1rem;
    }

    .warehouse-selector {
        background: var(--canvas);
        border: 1px solid var(--line);
        border-radius: 12px;
        padding: 1rem;
        margin-top: 1rem;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(26, 26, 26, 0.12);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
    }

    .stat-card.movements::before { background: var(--brand-red); }
    .stat-card.today::before { background: var(--brand-gold); }
    .stat-card.month::before { background: var(--success); }
    .stat-card.items::before { background: var(--warning); }

    .stat-content {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.3rem;
    }

    .stat-card.movements .stat-icon { color: var(--brand-red); background: var(--brand-red-light); }
    .stat-card.today .stat-icon { color: var(--brand-gold); background: var(--brand-gold-light); }
    .stat-card.month .stat-icon { color: var(--success); background: rgba(46, 125, 50, 0.1); }
    .stat-card.items .stat-icon { color: var(--warning); background: rgba(243, 156, 18, 0.1); }

    .stat-info h3 {
        color: var(--ink);
        font-weight: 600;
        font-size: 1rem;
        margin: 0 0 0.5rem 0;
    }

    .stat-info .value {
        font-weight: 700;
        font-size: 1.8rem;
        margin: 0;
    }

    .stat-card.movements .value { color: var(--brand-red); }
    .stat-card.today .value { color: var(--brand-gold); }
    .stat-card.month .value { color: var(--success); }
    .stat-card.items .value { color: var(--warning); }

    .content-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .section-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    .section-title {
        color: var(--ink);
        font-weight: 600;
        font-size: 1.3rem;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .movement-breakdown {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .movement-item {
        padding: 1rem;
        border-radius: 12px;
        text-align: center;
        transition: all 0.3s ease;
    }

    .movement-item:hover {
        transform: scale(1.05);
    }

    .movement-item.receipts {
        background: rgba(46, 125, 50, 0.1);
        border: 1px solid rgba(46, 125, 50, 0.3);
    }

    .movement-item.issues {
        background: rgba(194, 24, 7, 0.1);
        border: 1px solid rgba(194, 24, 7, 0.3);
    }

    .movement-item.transfers-in {
        background: var(--brand-gold-light);
        border: 1px solid var(--brand-gold);
    }

    .movement-item.transfers-out {
        background: rgba(243, 156, 18, 0.1);
        border: 1px solid rgba(243, 156, 18, 0.3);
    }

    .movement-item .icon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .movement-item.receipts .icon { color: var(--success); }
    .movement-item.issues .icon { color: var(--error); }
    .movement-item.transfers-in .icon { color: var(--brand-gold); }
    .movement-item.transfers-out .icon { color: var(--warning); }

    .movement-item .count {
        font-weight: 700;
        font-size: 1.5rem;
        margin-bottom: 0.25rem;
    }

    .movement-item .label {
        color: var(--slate);
        font-size: 0.9rem;
    }

    .recent-movements {
        max-height: 400px;
        overflow-y: auto;
    }

    .movement-entry {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border-bottom: 1px solid var(--line);
        transition: all 0.3s ease;
    }

    .movement-entry:hover {
        background: var(--canvas);
    }

    .movement-entry:last-child {
        border-bottom: none;
    }

    .movement-type-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .movement-type-icon.receipt {
        background: rgba(46, 125, 50, 0.1);
        color: var(--success);
    }

    .movement-type-icon.issue {
        background: rgba(194, 24, 7, 0.1);
        color: var(--error);
    }

    .movement-type-icon.transfer {
        background: var(--brand-gold-light);
        color: var(--brand-gold);
    }

    .movement-details {
        flex: 1;
    }

    .movement-item-name {
        color: var(--ink);
        font-weight: 600;
        font-size: 0.95rem;
        margin-bottom: 0.25rem;
    }

    .movement-info {
        color: var(--slate);
        font-size: 0.85rem;
    }

    .movement-quantity {
        color: var(--brand-gold);
        font-weight: 600;
        font-size: 1.1rem;
    }

    .filters-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    /* Print styles - Enhanced for visual comfort and better organization */
    @media print {
        /* Hide non-printable elements */
        .no-print {
            display: none !important;
        }
        
        /* Base print settings */
        .page-container {
            background: white;
            padding: 0;
            margin: 0;
        }
        
        body {
            font-size: 12pt;
            line-height: 1.5;
            color: #000;
        }
        
        /* Improved print header */
        .manager-header {
            box-shadow: none;
            border: 1px solid #666;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.2rem;
            background-color: #f9f9f9;
            page-break-inside: avoid;
        }
        
        .manager-details h1 {
            font-size: 22pt;
            margin-bottom: 0.5rem;
            color: #000;
        }
        
        .warehouse-name {
            font-size: 16pt;
            color: #333;
            margin-bottom: 0.3rem;
        }
        
        .manager-role {
            font-size: 12pt;
            color: #444;
        }
        
        /* Stats layout optimization */
        .stats-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 1.5rem;
            page-break-inside: avoid;
        }
        
        .stat-card {
            width: 24%;
            margin-bottom: 1rem;
            box-shadow: none;
            border: 1px solid #999;
            border-radius: 8px;
            background-color: #f9f9f9;
            page-break-inside: avoid;
        }
        
        .stat-content {
            justify-content: center;
            text-align: center;
        }
        
        .stat-info h3 {
            font-size: 11pt;
            margin-bottom: 0.3rem;
        }
        
        .stat-info .value {
            font-size: 16pt;
            font-weight: bold;
        }
        
        /* Content grid layout */
        .content-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .content-grid .section-card {
            width: calc(50% - 0.5rem);
            box-sizing: border-box;
        }
        
        /* Section cards styling */
        .section-card {
            box-shadow: none;
            border: 1px solid #999;
            border-radius: 8px;
            padding: 1.2rem;
            margin-bottom: 1rem;
            background-color: #f9f9f9;
            page-break-inside: avoid;
        }
        
        .section-title {
            font-size: 14pt;
            font-weight: bold;
            color: #333;
            margin-bottom: 1rem;
            text-align: center;
            border-bottom: 1px solid #ccc;
            padding-bottom: 0.5rem;
        }
        
        /* Movement breakdown */
        .movement-breakdown {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 0.8rem;
        }
        
        .movement-item {
            width: 48%;
            border-radius: 6px;
            padding: 0.8rem;
            margin-bottom: 0.5rem;
            page-break-inside: avoid;
            text-align: center;
        }
        
        .movement-item .icon {
            font-size: 14pt;
            margin-bottom: 0.3rem;
        }
        
        .movement-item .count {
            font-size: 15pt;
            font-weight: bold;
            margin-bottom: 0.2rem;
        }
        
        .movement-item .label {
            font-size: 10pt;
            color: #333;
        }
        
        /* Recent movements section */
        .recent-movements {
            max-height: none;
            overflow-y: visible;
        }
        
        .movement-entry {
            display: flex;
            align-items: center;
            padding: 0.7rem;
            border-bottom: 1px solid #ddd;
            page-break-inside: avoid;
        }
        
        .movement-type-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.8rem;
        }
        
        .movement-item-name {
            font-weight: bold;
            font-size: 11pt;
            margin-bottom: 0.2rem;
        }
        
        .movement-info {
            font-size: 9pt;
            color: #555;
        }
        
        .movement-quantity {
            font-weight: bold;
            font-size: 11pt;
            margin-left: auto;
            color: #333;
        }
        
        /* Page break control */
        h1, h2, h3, .section-title {
            page-break-after: avoid;
        }
        
        .movement-entry, .stat-card, .movement-item {
            break-inside: avoid;
        }
        
        /* Color adjustments for print */
        .movement-item.receipts { background-color: #e8f5e9; border: 1px solid #81c784; }
        .movement-item.issues { background-color: #ffebee; border: 1px solid #e57373; }
        .movement-item.transfers-in { background-color: #fff8e1; border: 1px solid #ffd54f; }
        .movement-item.transfers-out { background-color: #e1f5fe; border: 1px solid #4fc3f7; }
        
        .movement-item.receipts .icon { color: #2e7d32; }
        .movement-item.issues .icon { color: #c62828; }
        .movement-item.transfers-in .icon { color: #f9a825; }
        .movement-item.transfers-out .icon { color: #0277bd; }
        
        .movement-type-icon.receipt { background-color: #e8f5e9; color: #2e7d32; }
        .movement-type-icon.issue { background-color: #ffebee; color: #c62828; }
        .movement-type-icon.transfer { background-color: #fff8e1; color: #f9a825; }
    }

    @media (max-width: 768px) {
        .manager-info {
            flex-direction: column;
            text-align: center;
        }
        
        .content-grid {
            grid-template-columns: 1fr;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .movement-breakdown {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="container-fluid">
        <!-- مسار التنقل -->
        <nav aria-label="breadcrumb" class="no-print">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:dashboard' %}">الصفحة الرئيسية</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:reports_main_dashboard' %}">التقارير</a>
                </li>
                <li class="breadcrumb-item active">تقرير مدير المخزن</li>
            </ol>
        </nav>

        <!-- معلومات المدير والمخزن -->
        <div class="manager-header">
            <div class="manager-info">
                <div class="manager-avatar">
                    <i class="fas fa-user-tie"></i>
                </div>
                <div class="manager-details">
                    <h1>
                        {% if warehouse_manager and warehouse_manager.user %}
                            {{ warehouse_manager.user.get_full_name|default:warehouse_manager.user.username }}
                        {% else %}
                            مدير المخزن
                        {% endif %}
                    </h1>
                    <div class="warehouse-name">
                        <i class="fas fa-warehouse"></i>
                        {{ selected_warehouse.warehouse_name }}
                    </div>
                    <div class="manager-role">
                        {{ selected_warehouse.location|default:"موقع غير محدد" }}
                    </div>
                </div>
            </div>

            <!-- اختيار المخزن (للمدير العام فقط) -->
            {% if warehouses|length > 1 %}
                <div class="warehouse-selector no-print">
                    <form method="GET" action="">
                        {% if date_from %}<input type="hidden" name="date_from" value="{{ date_from }}">{% endif %}
                        {% if date_to %}<input type="hidden" name="date_to" value="{{ date_to }}">{% endif %}
                        
                        <div style="display: flex; align-items: center; gap: 1rem;">
                            <label for="warehouse_id" style="color: var(--ink); font-weight: 600;">اختيار المخزن:</label>
                            <select id="warehouse_id" name="warehouse_id" class="form-control" style="width: auto;" onchange="this.form.submit()">
                                {% for warehouse in warehouses %}
                                    <option value="{{ warehouse.id }}" 
                                            {% if selected_warehouse_id == warehouse.id|stringformat:"s" %}selected{% endif %}>
                                        {{ warehouse.warehouse_name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </form>
                </div>
            {% endif %}
        </div>

        <!-- أزرار الطباعة -->
        <div class="no-print" style="margin-bottom: 2rem;">
            <button onclick="window.print()" class="btn-primary">
                <i class="fas fa-print"></i>
                طباعة التقرير
            </button>
        </div>

        <!-- فلاتر التاريخ -->
        <div class="filters-card no-print">
            <div class="filters-title">
                <i class="fas fa-calendar"></i>
                فلتر التاريخ
            </div>
            
            <form method="GET" action="">
                {% if selected_warehouse_id %}<input type="hidden" name="warehouse_id" value="{{ selected_warehouse_id }}">{% endif %}
                
                <div class="filter-row">
                    <div class="form-group">
                        <label for="date_from">من تاريخ</label>
                        <input type="date" 
                               id="date_from" 
                               name="date_from" 
                               class="form-control" 
                               value="{{ date_from }}">
                    </div>
                    
                    <div class="form-group">
                        <label for="date_to">إلى تاريخ</label>
                        <input type="date" 
                               id="date_to" 
                               name="date_to" 
                               class="form-control" 
                               value="{{ date_to }}">
                    </div>
                </div>
                
                <div class="d-flex gap-2 flex-wrap">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-search"></i>
                        تطبيق الفلتر
                    </button>
                    <a href="{% url 'inventory:warehouse_manager_report' %}{% if selected_warehouse_id %}?warehouse_id={{ selected_warehouse_id }}{% endif %}" class="btn-secondary">
                        <i class="fas fa-times"></i>
                        إزالة الفلتر
                    </a>
                </div>
            </form>
        </div>

        <!-- إحصائيات المدير -->
        <div class="stats-grid">
            <div class="stat-card movements">
                <div class="stat-content">
                    <div class="stat-icon">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="stat-info">
                        <h3>إجمالي الحركات</h3>
                        <p class="value">{{ manager_stats.total_movements|floatformat:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card today">
                <div class="stat-content">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-day"></i>
                    </div>
                    <div class="stat-info">
                        <h3>حركات اليوم</h3>
                        <p class="value">{{ manager_stats.movements_today|floatformat:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card month">
                <div class="stat-content">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="stat-info">
                        <h3>حركات الشهر</h3>
                        <p class="value">{{ manager_stats.movements_this_month|floatformat:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card items">
                <div class="stat-content">
                    <div class="stat-icon">
                        <i class="fas fa-cubes"></i>
                    </div>
                    <div class="stat-info">
                        <h3>الأصناف المُدارة</h3>
                        <p class="value">{{ manager_stats.items_managed|floatformat:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="content-grid">
            <!-- تفاصيل الحركات -->
            <div class="section-card">
                <div class="section-title">
                    <i class="fas fa-chart-pie"></i>
                    تفاصيل الحركات
                </div>
                
                <div class="movement-breakdown">
                    <div class="movement-item receipts">
                        <div class="icon">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div class="count">{{ manager_stats.receipts_count }}</div>
                        <div class="label">استلام</div>
                    </div>

                    <div class="movement-item issues">
                        <div class="icon">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div class="count">{{ manager_stats.issues_count }}</div>
                        <div class="label">صرف</div>
                    </div>

                    <div class="movement-item transfers-in">
                        <div class="icon">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                        <div class="count">{{ manager_stats.transfers_in_count }}</div>
                        <div class="label">نقل داخل</div>
                    </div>

                    <div class="movement-item transfers-out">
                        <div class="icon">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="count">{{ manager_stats.transfers_out_count }}</div>
                        <div class="label">نقل خارج</div>
                    </div>
                </div>
            </div>

            <!-- ملخص المخزون -->
            <div class="section-card">
                <div class="section-title">
                    <i class="fas fa-boxes"></i>
                    ملخص المخزون الحالي
                </div>
                
                <div class="movement-breakdown">
                    <div class="movement-item" style="background: var(--brand-gold-light); border: 1px solid var(--brand-gold);">
                        <div class="icon" style="color: var(--brand-gold);">
                            <i class="fas fa-cubes"></i>
                        </div>
                        <div class="count" style="color: var(--brand-gold);">{{ stock_summary.total_items }}</div>
                        <div class="label">إجمالي الأصناف</div>
                    </div>

                    <div class="movement-item" style="background: rgba(46, 125, 50, 0.1); border: 1px solid var(--success);">
                        <div class="icon" style="color: var(--success);">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="count" style="color: var(--success);">{{ stock_summary.total_quantity|floatformat:0 }}</div>
                        <div class="label">إجمالي الكميات</div>
                    </div>

                    <div class="movement-item" style="background: rgba(243, 156, 18, 0.1); border: 1px solid var(--warning);">
                        <div class="icon" style="color: var(--warning);">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="count" style="color: var(--warning);">{{ stock_summary.low_stock_items }}</div>
                        <div class="label">أصناف منخفضة</div>
                    </div>

                    <div class="movement-item" style="background: var(--canvas); border: 1px solid var(--line);">
                        <div class="icon" style="color: var(--slate);">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="count" style="color: var(--slate);">{{ stock_summary.locations_used }}</div>
                        <div class="label">مواقع مستخدمة</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أحدث الحركات -->
        <div class="section-card">
            <div class="section-title">
                <i class="fas fa-history"></i>
                أحدث الحركات (آخر 20 حركة)
            </div>
            
            {% if recent_movements %}
                <div class="recent-movements">
                    {% for movement in recent_movements %}
                        <div class="movement-entry">
                            <div class="movement-type-icon {% if movement.movement_type == 'RECEIPT' %}receipt{% elif movement.movement_type == 'ISSUE' %}issue{% else %}transfer{% endif %}">
                                {% if movement.movement_type == 'RECEIPT' %}
                                    <i class="fas fa-arrow-down"></i>
                                {% elif movement.movement_type == 'ISSUE' %}
                                    <i class="fas fa-arrow-up"></i>
                                {% else %}
                                    <i class="fas fa-exchange-alt"></i>
                                {% endif %}
                            </div>
                            
                            <div class="movement-details">
                                <div class="movement-item-name">{{ movement.item.item_name_ar }}</div>
                                <div class="movement-info">
                                    {{ movement.get_movement_type_display }} - 
                                    {{ movement.movement_date|date:"Y/m/d H:i" }} - 
                                    {{ movement.bin_location.bin_code|default:"غير محدد" }}
                                </div>
                            </div>
                            
                            <div class="movement-quantity">
                                {{ movement.quantity|floatformat:0 }} {{ movement.unit_of_measure }}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="no-data" style="text-align: center; padding: 2rem; color: var(--slate);">
                    <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                    <p>لا توجد حركات مسجلة</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

{% endblock %}