"""
URL configuration for KamaVerse Inventory System.
نظام إدارة المخزون - شركة القماش للاستيراد والتصدير
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from inventory.views import test_view

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('inventory.urls')),
    path('test/', test_view, name='test'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATICFILES_DIRS[0])
