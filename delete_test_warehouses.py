#!/usr/bin/env python
"""
Script to delete all test warehouses from the system
يقوم هذا السكريبت بإزالة جميع المخازن التجريبية من النظام
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

from inventory.models import Warehouse, BinLocation

def delete_test_warehouses():
    """Delete all warehouses with 'Test' in their name"""
    # Find all warehouses with "Test" in their name
    test_warehouses = Warehouse.objects.filter(warehouse_name__icontains='Test')
    
    # Count them for reporting
    count = test_warehouses.count()
    
    if count > 0:
        print(f"Found {count} test warehouses to delete:")
        for warehouse in test_warehouses:
            print(f"  - {warehouse.warehouse_name} (ID: {warehouse.id})")
        
        # First, delete all bin locations in these warehouses
        bin_locations = BinLocation.objects.filter(warehouse__in=test_warehouses)
        bin_count = bin_locations.count()
        
        if bin_count > 0:
            print(f"Deleting {bin_count} bin locations associated with test warehouses...")
            bin_locations.delete()
            print(f"✅ Successfully deleted {bin_count} bin locations")
        
        # Now delete the warehouses
        print(f"Deleting {count} test warehouses...")
        test_warehouses.delete()
        print(f"✅ Successfully deleted {count} test warehouses")
    else:
        print("No test warehouses found in the system.")
    
    return count

if __name__ == "__main__":
    print("\n🧹 Starting test warehouse cleanup process...")
    print("=" * 50)
    
    warehouse_count = delete_test_warehouses()
    
    print("\n" + "=" * 50)
    if warehouse_count > 0:
        print(f"✅ Successfully removed {warehouse_count} test warehouses from the system!")
    else:
        print("📝 No test warehouses found in the system.")
    
    print("\n🎉 Test warehouse cleanup completed!")