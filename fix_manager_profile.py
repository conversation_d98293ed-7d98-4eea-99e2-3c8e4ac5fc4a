#!/usr/bin/env python
"""
Fix Manager User Profile
This script creates/updates the manager user profile to resolve reports access issues.
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

from django.contrib.auth.models import User
from inventory.models import UserProfile, Warehouse

def fix_manager_profile():
    """Create/update manager user profile with proper permissions"""
    print("🔧 Fixing Manager User Profile...")
    print("=" * 50)
    
    try:
        # Get or create the manager user
        manager_user, created = User.objects.get_or_create(
            username='manager',
            defaults={
                'first_name': 'أحمد',
                'last_name': 'محمد',
                'email': '<EMAIL>',
                'is_active': True,
                'is_staff': True
            }
        )
        
        # Set the password
        manager_user.set_password('manager123')
        manager_user.first_name = 'أحمد'
        manager_user.last_name = 'محمد'
        manager_user.email = '<EMAIL>'
        manager_user.is_active = True
        manager_user.is_staff = True
        manager_user.save()
        
        if created:
            print(f"✅ Created manager user: {manager_user.get_full_name()}")
        else:
            print(f"✅ Updated manager user: {manager_user.get_full_name()}")
        
        # Get a default warehouse for the manager (first active warehouse)
        default_warehouse = Warehouse.objects.filter(is_active=True).first()
        
        if not default_warehouse:
            print("⚠️ No active warehouses found! Creating a default warehouse...")
            default_warehouse = Warehouse.objects.create(
                warehouse_code='DEFAULT-001',
                warehouse_name='المخزن الرئيسي',
                warehouse_name_en='Main Warehouse',
                location='الموقع الافتراضي',
                is_active=True
            )
            print(f"✅ Created default warehouse: {default_warehouse.warehouse_name}")
        
        # Create or update the UserProfile
        manager_profile, profile_created = UserProfile.objects.get_or_create(
            user=manager_user,
            defaults={
                'role': 'MANAGER',
                'assigned_warehouse': default_warehouse,  # Assign to first warehouse
                'phone_number': '01234567890',
                'department': 'إدارة المخازن',
                'is_active': True
            }
        )
        
        # Update the profile if it already exists
        if not profile_created:
            manager_profile.role = 'MANAGER'
            manager_profile.assigned_warehouse = default_warehouse
            manager_profile.phone_number = '01234567890'
            manager_profile.department = 'إدارة المخازن'
            manager_profile.is_active = True
            manager_profile.save()
            print(f"✅ Updated manager profile")
        else:
            print(f"✅ Created manager profile")
        
        print(f"\n📋 Manager Profile Details:")
        print(f"   Username: {manager_user.username}")
        print(f"   Full Name: {manager_user.get_full_name()}")
        print(f"   Role: {manager_profile.get_role_display()}")
        print(f"   Assigned Warehouse: {manager_profile.assigned_warehouse.warehouse_name}")
        print(f"   Can Access All Warehouses: {manager_profile.role == 'MANAGER'}")
        
        # Test access functions
        print(f"\n🧪 Testing Access Functions:")
        accessible_warehouses = manager_profile.get_accessible_warehouses()
        print(f"   Accessible Warehouses Count: {accessible_warehouses.count()}")
        
        # Test the specific functions used in reports views
        from inventory.views import get_user_warehouse, check_manager_access
        
        user_warehouse = get_user_warehouse(manager_user)
        manager_access = check_manager_access(manager_user)
        
        print(f"   get_user_warehouse() result: {user_warehouse}")
        print(f"   check_manager_access() result: {manager_access}")
        
        if user_warehouse and manager_access:
            print(f"✅ SUCCESS: Manager should now have access to all reports!")
        else:
            print(f"❌ ISSUE: Manager still may not have full access")
            if not user_warehouse:
                print(f"   - get_user_warehouse() returned None")
            if not manager_access:
                print(f"   - check_manager_access() returned False")
        
    except Exception as e:
        print(f"❌ Error fixing manager profile: {str(e)}")
        import traceback
        traceback.print_exc()

def list_all_users():
    """List all users and their profiles for debugging"""
    print(f"\n👥 All Users and Profiles:")
    print("=" * 60)
    
    for user in User.objects.all():
        try:
            profile = UserProfile.objects.get(user=user)
            warehouse = profile.assigned_warehouse.warehouse_name if profile.assigned_warehouse else 'جميع المخازن'
            print(f"👤 {user.get_full_name()} ({user.username})")
            print(f"   Role: {profile.get_role_display()}")
            print(f"   Warehouse: {warehouse}")
            print(f"   Active: {profile.is_active}")
        except UserProfile.DoesNotExist:
            print(f"👤 {user.get_full_name()} ({user.username})")
            print(f"   ❌ No Profile")
        print()

if __name__ == '__main__':
    fix_manager_profile()
    list_all_users()
    print(f"\n🎉 Manager profile fix completed!")
    print(f"💡 Try logging in as 'manager' with password 'manager123' now.")