{% extends "base.html" %}
{% load static %}

{% block title %}تقرير الأرصدة حسب موقع التخزين - KamaVerse{% endblock %}

{% block extra_css %}
<style>
    :root {
        --brand-red: #D62828;
        --brand-red-dark: #8B1116;
        --brand-red-light: #FCE8E8;
        --brand-gold: #C89A3C;
        --brand-gold-light: #F4D488;
        --brand-gold-dark: #8C6420;
        --ink: #1A1A1A;
        --slate: #4A4F57;
        --line: #E6E8ED;
        --canvas: #F7F8FB;
        --white: #FFFFFF;
        --success: #2E7D32;
        --warning: #F39C12;
        --error: #C21807;
    }

    .page-container {
        background: linear-gradient(180deg, #FFFFFF 0%, #F7F8FB 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .page-header {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    .page-header h1 {
        color: var(--ink);
        font-weight: 700;
        font-size: 2.5rem;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .page-header .icon {
        color: var(--brand-gold);
        font-size: 3rem;
    }

    .page-header p {
        color: var(--slate);
        font-size: 1.1rem;
        margin: 1rem 0 0 0;
    }

    .warehouse-selector {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    .selector-title {
        color: var(--ink);
        font-weight: 600;
        font-size: 1.2rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .selector-title i {
        color: var(--brand-gold);
    }

    .form-group label {
        color: var(--slate);
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        border: 1px solid var(--line);
        border-radius: 8px;
        padding: 0.75rem;
        color: var(--ink);
        background: var(--white);
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: var(--brand-gold);
        box-shadow: 0 0 0 0.2rem rgba(200, 154, 60, 0.25);
        outline: none;
    }

    .btn-primary {
        background: var(--brand-red);
        border: 1px solid var(--brand-red);
        color: var(--white);
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary:hover {
        background: var(--brand-red-dark);
        border-color: var(--brand-red-dark);
        color: var(--white);
        text-decoration: none;
        transform: translateY(-1px);
    }

    .warehouse-info-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    .warehouse-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .warehouse-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: var(--brand-gold);
        background: var(--brand-gold-light);
    }

    .warehouse-title {
        color: var(--ink);
        font-weight: 700;
        font-size: 1.8rem;
        margin: 0;
    }

    .bin-locations-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 1.5rem;
    }

    .bin-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .bin-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 30px rgba(26, 26, 26, 0.12);
    }

    .bin-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, var(--brand-gold) 0%, var(--brand-gold-dark) 100%);
    }

    .bin-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .bin-icon {
        width: 45px;
        height: 45px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        color: var(--brand-gold);
        background: var(--brand-gold-light);
    }

    .bin-info h3 {
        color: var(--ink);
        font-weight: 600;
        font-size: 1.2rem;
        margin: 0;
    }

    .bin-info p {
        color: var(--slate);
        font-size: 0.9rem;
        margin: 0.2rem 0 0 0;
    }

    .bin-stats {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .stat-mini {
        text-align: center;
        background: var(--canvas);
        border-radius: 8px;
        padding: 0.5rem;
        border: 1px solid var(--line);
    }

    .stat-mini .value {
        color: var(--brand-red);
        font-weight: 700;
        font-size: 1.1rem;
        margin: 0;
    }

    .stat-mini .label {
        color: var(--slate);
        font-size: 0.8rem;
        margin: 0.2rem 0 0 0;
    }

    .occupancy-mini {
        margin-bottom: 1rem;
    }

    .occupancy-label-mini {
        display: flex;
        justify-content: space-between;
        color: var(--slate);
        font-size: 0.8rem;
        margin-bottom: 0.3rem;
    }

    .progress-mini {
        height: 6px;
        background: var(--line);
        border-radius: 3px;
        overflow: hidden;
    }

    .progress-bar-mini {
        height: 100%;
        background: linear-gradient(135deg, var(--brand-gold) 0%, var(--brand-gold-dark) 100%);
        border-radius: 3px;
        transition: width 0.3s ease;
    }

    .items-list {
        max-height: 200px;
        overflow-y: auto;
        background: var(--canvas);
        border-radius: 8px;
        padding: 0.5rem;
        border: 1px solid var(--line);
    }

    .item-entry {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem;
        border-bottom: 1px solid var(--line);
        transition: all 0.3s ease;
    }

    .item-entry:last-child {
        border-bottom: none;
    }

    .item-entry:hover {
        background: var(--white);
        border-radius: 4px;
    }

    .item-name {
        font-weight: 600;
        color: var(--ink);
        font-size: 0.9rem;
    }

    .item-code-small {
        font-family: 'Courier New', monospace;
        color: var(--slate);
        font-size: 0.8rem;
    }

    .item-quantity {
        background: var(--brand-red-light);
        color: var(--brand-red);
        padding: 0.2rem 0.5rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.8rem;
    }

    .empty-bin {
        text-align: center;
        padding: 2rem;
        color: var(--slate);
    }

    .empty-bin i {
        font-size: 2rem;
        color: var(--brand-gold);
        margin-bottom: 0.5rem;
    }

    .breadcrumb {
        background: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }

    .breadcrumb-item {
        color: var(--slate);
    }

    .breadcrumb-item.active {
        color: var(--brand-red);
        font-weight: 600;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: var(--brand-gold);
        margin: 0 0.5rem;
    }

    .no-data {
        text-align: center;
        padding: 3rem;
        color: var(--slate);
    }

    .no-data i {
        font-size: 4rem;
        color: var(--brand-gold);
        margin-bottom: 1rem;
    }

    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2rem;
        }
        
        .page-header .icon {
            font-size: 2.5rem;
        }

        .warehouse-header {
            flex-direction: column;
            text-align: center;
        }

        .bin-locations-grid {
            grid-template-columns: 1fr;
        }

        .bin-stats {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="container-fluid">
        <!-- مسار التنقل -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:dashboard' %}">الصفحة الرئيسية</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:reports_main_dashboard' %}">التقارير</a>
                </li>
                <li class="breadcrumb-item active">الأرصدة حسب موقع التخزين</li>
            </ol>
        </nav>

        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1>
                <i class="fas fa-map-marker-alt icon"></i>
                تقرير الأرصدة حسب موقع التخزين
            </h1>
            <p>تقرير مفصل عن الأرصدة في كل موقع تخزين داخل المخازن مع معلومات الإشغال</p>
        </div>

        <!-- محدد المخزن -->
        {% if warehouses|length > 1 %}
            <div class="warehouse-selector">
                <div class="selector-title">
                    <i class="fas fa-filter"></i>
                    اختيار المخزن
                </div>
                
                <form method="GET" action="">
                    <div class="d-flex gap-3 align-items-end">
                        <div class="form-group flex-grow-1">
                            <label for="warehouse">المخزن</label>
                            <select id="warehouse" name="warehouse" class="form-control">
                                {% for warehouse in warehouses %}
                                    <option value="{{ warehouse.id }}" 
                                            {% if warehouse_filter == warehouse.id|stringformat:"s" %}selected{% endif %}>
                                        {{ warehouse.warehouse_name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-search"></i>
                            عرض
                        </button>
                    </div>
                </form>
            </div>
        {% endif %}

        {% if selected_warehouse %}
            <!-- معلومات المخزن المحدد -->
            <div class="warehouse-info-card">
                <div class="warehouse-header">
                    <div class="warehouse-icon">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <div>
                        <h2 class="warehouse-title">{{ selected_warehouse.warehouse_name }}</h2>
                        <p class="text-muted">{{ selected_warehouse.location|default:"غير محدد" }}</p>
                    </div>
                </div>
            </div>

            <!-- مواقع التخزين -->
            {% if bin_data %}
                <div class="bin-locations-grid">
                    {% for data in bin_data %}
                        <div class="bin-card">
                            <!-- رأس موقع التخزين -->
                            <div class="bin-header">
                                <div class="bin-icon">
                                    <i class="fas fa-cube"></i>
                                </div>
                                <div class="bin-info">
                                    <h3>{{ data.bin_location.bin_code }}</h3>
                                    <p>{{ data.bin_location.bin_description|default:"لا يوجد وصف" }}</p>
                                </div>
                            </div>

                            <!-- الإحصائيات المصغرة -->
                            <div class="bin-stats">
                                <div class="stat-mini">
                                    <p class="value">{{ data.total_items|floatformat:0 }}</p>
                                    <p class="label">أصناف</p>
                                </div>
                                <div class="stat-mini">
                                    <p class="value">{{ data.total_quantity|floatformat:1 }}</p>
                                    <p class="label">كمية</p>
                                </div>
                                <div class="stat-mini">
                                    <p class="value">{{ data.total_value|floatformat:0 }}</p>
                                    <p class="label">قيمة</p>
                                </div>
                            </div>

                            <!-- شريط الإشغال المصغر -->
                            <div class="occupancy-mini">
                                <div class="occupancy-label-mini">
                                    <span>نسبة الإشغال</span>
                                    <span><strong>{{ data.occupancy_percentage|floatformat:1 }}%</strong></span>
                                </div>
                                <div class="progress-mini">
                                    <div class="progress-bar-mini" style="width: {{ data.occupancy_percentage }}%"></div>
                                </div>
                            </div>

                            <!-- قائمة الأصناف -->
                            {% if data.stock_balances %}
                                <div class="items-list">
                                    {% for balance in data.stock_balances %}
                                        <div class="item-entry">
                                            <div>
                                                <div class="item-name">{{ balance.item.item_name_ar|truncatechars:25 }}</div>
                                                <div class="item-code-small">{{ balance.item.item_code }}</div>
                                            </div>
                                            <div class="item-quantity">
                                                {{ balance.current_quantity|floatformat:1 }}
                                            </div>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <div class="empty-bin">
                                    <i class="fas fa-inbox"></i>
                                    <p><strong>موقع فارغ</strong></p>
                                    <p>لا توجد أصناف في هذا الموقع</p>
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="bin-card">
                    <div class="no-data">
                        <i class="fas fa-map-marker-alt"></i>
                        <h4>لا توجد مواقع تخزين</h4>
                        <p>لا توجد مواقع تخزين مفعلة في هذا المخزن</p>
                    </div>
                </div>
            {% endif %}
        {% else %}
            <div class="warehouse-info-card">
                <div class="no-data">
                    <i class="fas fa-warehouse"></i>
                    <h4>لا يوجد مخزن محدد</h4>
                    <p>يرجى اختيار مخزن لعرض مواقع التخزين الخاصة به</p>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}