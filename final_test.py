#!/usr/bin/env python
"""
Final comprehensive test for reports functionality
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse

def final_reports_test():
    """Final comprehensive test"""
    
    # Test with Django test client
    client = Client()
    
    print("=== FINAL REPORTS TEST ===")
    
    # Test 1: Get admin user and login
    try:
        admin_user = User.objects.get(username='admin')
        client.force_login(admin_user)
        print("✅ Admin user logged in successfully")
    except Exception as e:
        print(f"❌ Failed to login admin: {str(e)}")
        return
    
    # Test 2: Test reports URL
    try:
        reports_url = reverse('inventory:reports_main_dashboard')
        print(f"✅ Reports URL: {reports_url}")
    except Exception as e:
        print(f"❌ Failed to resolve reports URL: {str(e)}")
        return
    
    # Test 3: Make request to reports page
    try:
        response = client.get(reports_url)
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            print("🎉 SUCCESS! Reports page is working!")
            
            # Check if it contains the expected content
            content = response.content.decode('utf-8')
            if 'التقارير الأساسية' in content:
                print("✅ Correct content: Arabic reports dashboard loaded")
            if 'KamaVerse' in content:
                print("✅ Brand name found in response")
            if 'reports-grid' in content:
                print("✅ Reports grid structure found")
                
            print("\n🎯 The reports system is working correctly!")
            print("   Try accessing: http://127.0.0.1:8000/reports/")
            
        elif response.status_code == 302:
            redirect_url = response.get('Location', 'Unknown')
            print(f"🚨 Still redirecting to: {redirect_url}")
            
            if '/dashboard/' in redirect_url:
                print("❌ Problem: Still redirecting to dashboard")
                print("   This suggests a user profile or permission issue")
            elif '/login/' in redirect_url:
                print("❌ Problem: Still redirecting to login") 
                print("   This suggests an authentication issue")
                
        else:
            print(f"⚠️  Unexpected response code: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Request failed: {str(e)}")
    
    print("\n" + "="*50)

if __name__ == "__main__":
    final_reports_test()