{% extends 'base.html' %}
{% load static %}

{% block title %}تقرير حركات المخزون المجمعة - KamaVerse{% endblock %}

{% block extra_css %}
<style>
    :root {
        --brand-red: #D62828;
        --brand-red-dark: #8B1116;
        --brand-red-light: #FCE8E8;
        --brand-gold: #C89A3C;
        --brand-gold-light: #F4D488;
        --brand-gold-dark: #8C6420;
        --ink: #1A1A1A;
        --slate: #4A4F57;
        --line: #E6E8ED;
        --canvas: #F7F8FB;
        --white: #FFFFFF;
        --success: #2E7D32;
        --warning: #F39C12;
        --error: #C21807;
    }

    .page-container {
        background: linear-gradient(180deg, #FFFFFF 0%, #F7F8FB 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .page-header {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    .page-header h1 {
        color: var(--ink);
        font-weight: 700;
        font-size: 2.5rem;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .page-header .icon {
        color: var(--brand-gold);
        font-size: 3rem;
    }

    .filters-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    .summary-cards {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .summary-card {
        background: var(--white);
        border: none;
        border-radius: 16px;
        padding: 1.8rem;
        box-shadow: 0 10px 25px rgba(26, 26, 26, 0.05);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
        text-align: center;
    }

    .summary-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(26, 26, 26, 0.1);
    }

    .summary-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(135deg, var(--brand-gold) 0%, var(--brand-gold-dark) 100%);
    }

    .summary-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .summary-icon {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        margin-bottom: 1.2rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    .summary-info {
        width: 100%;
    }

    .summary-info h3 {
        color: var(--slate);
        font-weight: 600;
        font-size: 1.1rem;
        margin: 0 0 0.8rem 0;
    }

    .summary-info .value {
        color: var(--ink);
        font-weight: 700;
        font-size: 2.5rem;
        margin: 0;
        line-height: 1;
    }

    .warehouses-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
        gap: 2rem;
        margin-bottom: 2.5rem;
    }

    .warehouse-card {
        background: var(--white);
        border: none;
        border-radius: 18px;
        padding: 2.5rem;
        box-shadow: 0 10px 25px rgba(26, 26, 26, 0.08);
        transition: all 0.3s ease;
        min-height: 230px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .warehouse-card:hover {
        transform: translateY(-6px);
        box-shadow: 0 15px 35px rgba(26, 26, 26, 0.12);
    }

    .warehouse-header {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .warehouse-icon {
        width: 65px;
        height: 65px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: var(--brand-gold);
        background: linear-gradient(135deg, var(--brand-gold-light), rgba(200, 154, 60, 0.2));
        box-shadow: 0 4px 15px rgba(200, 154, 60, 0.15);
    }

    .warehouse-title {
        color: var(--ink);
        font-weight: 600;
        font-size: 1.4rem;
        margin: 0;
    }

    .warehouse-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1.2rem;
        margin-bottom: 2rem;
    }

    .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.8rem 1rem;
        background: var(--canvas);
        border-radius: 10px;
    }

    .stat-label {
        color: var(--slate);
        font-size: 1rem;
        font-weight: 500;
    }

    .stat-value {
        color: var(--ink);
        font-weight: 700;
        font-size: 1.25rem;
    }

    .details-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.7rem;
        background: var(--brand-red);
        color: var(--white);
        border: none;
        padding: 0.9rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        width: 100%;
        box-shadow: 0 4px 12px rgba(214, 40, 40, 0.2);
    }

    .details-button:hover {
        background: var(--brand-red-dark);
        transform: translateY(-3px);
        text-decoration: none;
        color: var(--white);
        box-shadow: 0 6px 15px rgba(214, 40, 40, 0.3);
    }

    .receipts { --color: var(--success); }
    .issues { --color: var(--error); }
    .transfers-in { --color: var(--brand-gold); }
    .transfers-out { --color: var(--warning); }

    .movement-total {
        text-align: center;
        padding: 1rem;
        background: var(--brand-red-light);
        border-radius: 12px;
        margin-top: 1rem;
    }

    .movement-total .total-label {
        color: var(--brand-red-dark);
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .movement-total .total-value {
        color: var(--brand-red);
        font-weight: 700;
        font-size: 1.5rem;
    }

    .btn-export {
        background: var(--success);
        color: var(--white);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin-left: 1rem;
    }

    .btn-export:hover {
        background: #1B5E20;
        transform: translateY(-2px);
    }

    /* Print styles */
    @media print {
        .no-print {
            display: none !important;
        }
        
        .page-container {
            background: white;
            padding: 0;
        }
        
        .movement-card {
            break-inside: avoid;
            border: 1px solid #ddd;
            margin-bottom: 1rem;
        }
    }

    @media (max-width: 768px) {
        .warehouses-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
        
        .summary-cards {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }
        
        .summary-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }
        
        .summary-info .value {
            font-size: 2rem;
        }
        
        .warehouse-card {
            padding: 2rem;
            min-height: 200px;
        }
        
        .warehouse-icon {
            width: 55px;
            height: 55px;
            font-size: 1.5rem;
        }
        
        .warehouse-title {
            font-size: 1.3rem;
        }
    }
    
    @media (max-width: 480px) {
        .summary-cards {
            grid-template-columns: 1fr;
        }
        
        .summary-card {
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="container-fluid">
        <!-- مسار التنقل -->
        <nav aria-label="breadcrumb" class="no-print">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:dashboard' %}">الصفحة الرئيسية</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:reports_main_dashboard' %}">التقارير</a>
                </li>
                <li class="breadcrumb-item active">حركات المخزون المجمعة</li>
            </ol>
        </nav>

        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1>
                <i class="fas fa-tasks icon"></i>
                تقرير حركات المخزون المجمعة
            </h1>
            <p>تقرير شامل لحركات المخزون (استلام-صرف-نقل) للمخازن</p>
        </div>
        </div>

        <!-- أزرار الطباعة والتصدير -->
        <div class="no-print" style="margin-bottom: 2rem;">
            <button onclick="exportToPDF()" class="btn-export" style="background: var(--error);">
                <i class="fas fa-file-pdf"></i>
                تصدير PDF
            </button>
        </div>

        <!-- الفلاتر -->
        <div class="filters-card no-print">
            <div class="filters-title">
                <i class="fas fa-filter"></i>
                فلاتر التقرير
            </div>
            
            <form method="GET" action="">
                <div class="filter-row">
                    <div class="form-group">
                        <label for="warehouse">المخزن</label>
                        <select id="warehouse" name="warehouse" class="form-control">
                            <option value="">جميع المخازن</option>
                            {% for warehouse in warehouses %}
                                <option value="{{ warehouse.id }}" 
                                        {% if warehouse_filter == warehouse.id|stringformat:"s" %}selected{% endif %}>
                                    {{ warehouse.warehouse_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div class="d-flex gap-2 flex-wrap">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-search"></i>
                        تطبيق الفلاتر
                    </button>
                    <a href="{% url 'inventory:consolidated_movements_report' %}" class="btn-secondary">
                        <i class="fas fa-times"></i>
                        إزالة الفلاتر
                    </a>
                </div>
            </form>
        </div>

        <!-- الإحصائيات العامة -->
        <div class="summary-cards">
            <div class="summary-card">
                <div class="summary-content">
                    <div class="summary-icon" style="color: var(--ink); background: linear-gradient(135deg, #F7F8FB, #E6E8ED);">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="summary-info">
                        <h3>إجمالي الحركات</h3>
                        <p class="value">{{ summary_data.total_movements|floatformat:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="summary-card">
                <div class="summary-content">
                    <div class="summary-icon receipts" style="color: var(--success); background: linear-gradient(135deg, rgba(46, 125, 50, 0.1), rgba(46, 125, 50, 0.2));">
                        <i class="fas fa-arrow-down"></i>
                    </div>
                    <div class="summary-info">
                        <h3>إجمالي الاستلام</h3>
                        <p class="value" style="color: var(--success);">{{ summary_data.total_receipts|floatformat:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="summary-card">
                <div class="summary-content">
                    <div class="summary-icon issues" style="color: var(--error); background: linear-gradient(135deg, rgba(194, 24, 7, 0.1), rgba(194, 24, 7, 0.2));">
                        <i class="fas fa-arrow-up"></i>
                    </div>
                    <div class="summary-info">
                        <h3>إجمالي الصرف</h3>
                        <p class="value" style="color: var(--error);">{{ summary_data.total_issues|floatformat:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="summary-card">
                <div class="summary-content">
                    <div class="summary-icon transfers-in" style="color: var(--brand-gold); background: linear-gradient(135deg, var(--brand-gold-light), rgba(200, 154, 60, 0.3));">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                    <div class="summary-info">
                        <h3>إجمالي النقل</h3>
                        <p class="value" style="color: var(--brand-gold);">{{ summary_data.total_transfers|floatformat:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- بيانات المخازن -->
        {% if warehouses %}
            <div class="warehouses-grid">
                {% for warehouse in warehouses %}
                    <div class="warehouse-card">
                        <div class="warehouse-header">
                            <div class="warehouse-icon">
                                <i class="fas fa-warehouse"></i>
                            </div>
                            <div>
                                <h3 class="warehouse-title">{{ warehouse.warehouse_name }}</h3>
                            </div>
                        </div>

                        <div class="warehouse-stats">
                            <div class="stat-item">
                                <span class="stat-label">عدد الأصناف</span>
                                <span class="stat-value">{{ warehouse.total_items|default:"0" }}</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">عدد الحركات</span>
                                <span class="stat-value">{{ warehouse.total_movements|default:"0" }}</span>
                            </div>
                        </div>

                        <a href="{% url 'inventory:warehouse_movements_detail' warehouse.id %}" class="details-button">
                            <i class="fas fa-info-circle"></i>
                            عرض التفاصيل
                        </a>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="no-data" style="text-align: center; padding: 3rem; color: var(--slate);">
                <div>
                    <i class="fas fa-inbox" style="font-size: 3rem; color: var(--line); margin-bottom: 1rem;"></i>
                </div>
                <h3>لا توجد مخازن</h3>
                <p>لا توجد مخازن متاحة للعرض</p>
            </div>
        {% endif %}
    </div>
</div>

<script>
function exportToExcel() {
    // سيتم تطوير وظيفة التصدير لـ Excel
    alert('سيتم إضافة وظيفة التصدير لـ Excel قريباً');
}

function exportToPDF() {
    // سيتم تطوير وظيفة التصدير لـ PDF
    alert('سيتم إضافة وظيفة التصدير لـ PDF قريباً');
}
</script>
{% endblock %}