"""
برنامج للتحقق من تفعيل نسبة الإشغال ونسبة الفراغ عند إنشاء مخزن جديد
Verification script for automatic occupancy and vacancy percentages
"""

import os
import django
import uuid
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

from inventory.models import Warehouse
from django.db import transaction

def check_auto_occupancy():
    """التحقق من التعيين التلقائي لنسبة الإشغال ونسبة الفراغ عند إنشاء مخزن جديد"""
    
    print("\n========== اختبار التعيين التلقائي لنسب الإشغال ==========")
    
    # إنشاء كود فريد للمخزن الاختباري
    test_code = f"TEST-{uuid.uuid4().hex[:6].upper()}"
    
    try:
        # استخدام transaction لضمان إلغاء التغييرات في حالة الفشل
        with transaction.atomic():
            # إنشاء مخزن اختباري جديد بقيمة إشغال أولية = صفر
            test_warehouse = Warehouse.objects.create(
                warehouse_code=test_code,
                warehouse_name="مخزن اختبار نسبة الإشغال",
                warehouse_type="BRANCH",
                total_capacity=Decimal('1000.00'),
                current_occupancy=Decimal('0.00'),  # قيمة إشغال أولية = 0
                address="عنوان اختباري",
                city="القاهرة",
                governorate="القاهرة",
                warehouse_manager="مدير اختباري",
                manager_phone="01234567890",
                is_active=True
            )
            
            # قراءة المخزن مباشرة من قاعدة البيانات للتأكد من حفظ التغييرات
            refreshed_warehouse = Warehouse.objects.get(pk=test_warehouse.pk)
            
            # عرض بيانات المخزن الاختباري
            print(f"\n📊 بيانات المخزن الاختباري:")
            print(f"  • اسم المخزن: {refreshed_warehouse.warehouse_name}")
            print(f"  • كود المخزن: {refreshed_warehouse.warehouse_code}")
            print(f"  • السعة الكلية: {refreshed_warehouse.total_capacity}")
            print(f"  • الإشغال الحالي: {refreshed_warehouse.current_occupancy}")
            print(f"  • نسبة الإشغال: {refreshed_warehouse.get_occupancy_percentage():.1f}%")
            print(f"  • نسبة الفراغ: {100 - refreshed_warehouse.get_occupancy_percentage():.1f}%")
            
            # التحقق من النتائج
            occupancy_percentage = refreshed_warehouse.get_occupancy_percentage()
            
            if occupancy_percentage > 0:
                print("\n✅ تم تفعيل نسبة الإشغال ونسبة الفراغ تلقائياً!")
                print(f"   النسبة المتوقعة: 25.0%, النسبة الفعلية: {occupancy_percentage:.1f}%")
            else:
                print("\n❌ فشل تفعيل نسبة الإشغال ونسبة الفراغ")
                print(f"   القيمة: {occupancy_percentage:.1f}%")
            
            # حذف المخزن الاختباري وإلغاء التغييرات
            if refreshed_warehouse.current_occupancy > 0:
                # نجحت عملية التعيين التلقائي، نقوم بحذف المخزن الاختباري
                test_warehouse.delete()
                print("\n🧹 تم حذف المخزن الاختباري بنجاح")
            else:
                # لم تنجح عملية التعيين التلقائي، نقوم بإلغاء التغييرات
                raise Exception("لم يتم تفعيل نسبة الإشغال تلقائياً")
    
    except Exception as e:
        print(f"\n❌ حدث خطأ أثناء الاختبار: {e}")
        # تأكد من حذف المخزن الاختباري في حالة حدوث أي استثناء
        try:
            Warehouse.objects.filter(warehouse_code=test_code).delete()
        except:
            pass
    
    print("\n========== انتهى الاختبار ==========")

if __name__ == "__main__":
    check_auto_occupancy()