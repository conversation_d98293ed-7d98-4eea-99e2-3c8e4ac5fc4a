import re

# Open the file and read all content
file_path = "inventory/models.py"
with open(file_path, 'r', encoding='utf-8') as f:
    content = f.read()

# Find and remove the TransferItem model
if 'class TransferItem(models.Model):' in content:
    # Use regex to match the entire TransferItem class and its methods
    pattern = r'class TransferItem\(models\.Model\):.*?def update_stock_on_completion\(self\):'
    # We want to keep the update_stock_on_completion method, so replace with just the method name
    fixed_content = re.sub(pattern, '', content, flags=re.DOTALL)
    
    # Write the fixed content back to the file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print("Fixed duplicate model definition!")
else:
    print("No duplicate TransferItem model found!")