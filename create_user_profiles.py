#!/usr/bin/env python
"""
إنشاء ملفات المستخدمين وربطها بالمخازن
Create User Profiles and link them to warehouses
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

from django.contrib.auth.models import User
from inventory.models import UserProfile, Warehouse

def create_user_profiles():
    """إنشاء ملفات المستخدمين"""
    print("🚀 بدء إنشاء ملفات المستخدمين...")
    
    # الحصول على المخازن
    try:
        cairo_warehouse = Warehouse.objects.get(warehouse_code='CAI-003')
        daery_warehouse = Warehouse.objects.get(warehouse_code='RNG-002')
        menoufia_warehouse = Warehouse.objects.get(warehouse_code='MNF-004')
        alex_warehouse = Warehouse.objects.get(warehouse_code='ALX-001')
    except Warehouse.DoesNotExist as e:
        print(f"❌ خطأ: لم يتم العثور على المخزن: {e}")
        return
    
    # بيانات ملفات المستخدمين
    profiles_data = [
        {
            'username': 'manager',
            'role': 'MANAGER',
            'warehouse': None,  # المدير يمكنه الوصول لجميع المخازن
            'phone': '01234567890',
            'department': 'إدارة المخازن'
        },
        {
            'username': 'cairo_manager',
            'role': 'WAREHOUSE_MANAGER',
            'warehouse': cairo_warehouse,
            'phone': '01234567891',
            'department': 'مخزن القاهرة'
        },
        {
            'username': 'daery_manager',
            'role': 'WAREHOUSE_MANAGER',
            'warehouse': daery_warehouse,
            'phone': '01234567892',
            'department': 'مخزن الدائري'
        },
        {
            'username': 'menoufia_manager',
            'role': 'WAREHOUSE_MANAGER',
            'warehouse': menoufia_warehouse,
            'phone': '01234567893',
            'department': 'مخزن المنوفية'
        },
        {
            'username': 'alex_manager',
            'role': 'WAREHOUSE_MANAGER',
            'warehouse': alex_warehouse,
            'phone': '01234567894',
            'department': 'مخزن الإسكندرية'
        }
    ]
    
    created_count = 0
    
    for profile_data in profiles_data:
        try:
            user = User.objects.get(username=profile_data['username'])
            
            profile, created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'role': profile_data['role'],
                    'assigned_warehouse': profile_data['warehouse'],
                    'phone_number': profile_data['phone'],
                    'department': profile_data['department'],
                    'is_active': True,
                }
            )
            
            if created:
                created_count += 1
                warehouse_name = profile_data['warehouse'].warehouse_name if profile_data['warehouse'] else 'جميع المخازن'
                print(f"✅ تم إنشاء ملف المستخدم: {user.get_full_name()} - {profile.get_role_display()} - {warehouse_name}")
            else:
                warehouse_name = profile_data['warehouse'].warehouse_name if profile_data['warehouse'] else 'جميع المخازن'
                print(f"ℹ️ ملف المستخدم موجود بالفعل: {user.get_full_name()} - {warehouse_name}")
                
        except User.DoesNotExist:
            print(f"❌ خطأ: المستخدم غير موجود: {profile_data['username']}")
    
    print(f"\n🎉 تم إنشاء {created_count} ملف مستخدم جديد من أصل {len(profiles_data)} ملف")
    
    # عرض ملخص الصلاحيات
    print("\n📋 ملخص الصلاحيات:")
    print("=" * 60)
    for profile in UserProfile.objects.all().select_related('user', 'assigned_warehouse'):
        warehouse_access = profile.assigned_warehouse.warehouse_name if profile.assigned_warehouse else 'جميع المخازن'
        print(f"👤 {profile.user.get_full_name()}")
        print(f"   الدور: {profile.get_role_display()}")
        print(f"   المخزن المخصص: {warehouse_access}")
        print(f"   الهاتف: {profile.phone_number}")
        print(f"   القسم: {profile.department}")
        print()

def test_permissions():
    """اختبار نظام الصلاحيات"""
    print("\n🧪 اختبار نظام الصلاحيات:")
    print("=" * 40)
    
    # اختبار المدير
    manager = User.objects.get(username='manager')
    manager_profile = UserProfile.objects.get(user=manager)
    accessible_warehouses = manager_profile.get_accessible_warehouses()
    print(f"👤 {manager.get_full_name()} (مدير المخازن):")
    print(f"   يمكنه الوصول إلى {accessible_warehouses.count()} مخزن")
    
    # اختبار مسؤول مخزن
    cairo_manager = User.objects.get(username='cairo_manager')
    cairo_profile = UserProfile.objects.get(user=cairo_manager)
    cairo_warehouses = cairo_profile.get_accessible_warehouses()
    print(f"👤 {cairo_manager.get_full_name()} (مسؤول مخزن القاهرة):")
    print(f"   يمكنه الوصول إلى {cairo_warehouses.count()} مخزن: {cairo_warehouses.first().warehouse_name}")

if __name__ == '__main__':
    create_user_profiles()
    test_permissions()
