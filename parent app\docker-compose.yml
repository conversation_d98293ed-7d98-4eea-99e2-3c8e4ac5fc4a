# KamaVerse Docker Compose Configuration
# نظام إدارة متكامل لشركة القماش

version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15
    container_name: kamaverse_db
    environment:
      POSTGRES_DB: kamaverse
      POSTGRES_USER: kamaverse_user
      POSTGRES_PASSWORD: kamaverse_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - kamaverse_network

  # Redis for Caching and Channels
  redis:
    image: redis:7-alpine
    container_name: kamaverse_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - kamaverse_network

  # Django Web Application
  web:
    build: .
    container_name: kamaverse_web
    command: python manage.py runserver 0.0.0.0:8000
    volumes:
      - .:/app
      - media_files:/app/media
      - static_files:/app/staticfiles
    ports:
      - "8000:8000"
    environment:
      - DJANGO_ENVIRONMENT=development
      - DB_HOST=db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - kamaverse_network

  # Celery Worker
  celery:
    build: .
    container_name: kamaverse_celery
    command: celery -A kamaverse worker -l info
    volumes:
      - .:/app
      - media_files:/app/media
    environment:
      - DJANGO_ENVIRONMENT=development
      - DB_HOST=db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - kamaverse_network

  # Celery Beat (Scheduler)
  celery-beat:
    build: .
    container_name: kamaverse_celery_beat
    command: celery -A kamaverse beat -l info
    volumes:
      - .:/app
    environment:
      - DJANGO_ENVIRONMENT=development
      - DB_HOST=db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - kamaverse_network

  # Nginx (for production)
  nginx:
    image: nginx:alpine
    container_name: kamaverse_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - static_files:/var/www/static
      - media_files:/var/www/media
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - web
    restart: unless-stopped
    networks:
      - kamaverse_network
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  media_files:
    driver: local
  static_files:
    driver: local

networks:
  kamaverse_network:
    driver: bridge

# Development override
# Use: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
