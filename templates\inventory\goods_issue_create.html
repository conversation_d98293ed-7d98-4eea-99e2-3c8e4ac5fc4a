{% extends 'base.html' %}
{% load static %}

{% block title %}إنشاء إذن صرف جديد - Ka<PERSON>Verse{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, var(--brand-red), #FF6B6B);
        color: var(--white);
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .page-header h1 {
        font-weight: 700;
        margin: 0;
        color: var(--white);
    }

    .btn-back {
        background: rgba(255, 255, 255, 0.2);
        color: var(--white);
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-back:hover {
        background: rgba(255, 255, 255, 0.3);
        color: var(--white);
        text-decoration: none;
    }

    .form-container {
        background: var(--white);
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(26, 26, 26, 0.08);
        border: 1px solid var(--line);
    }

    .form-section {
        margin-bottom: 2rem;
        padding-bottom: 2rem;
        border-bottom: 1px solid var(--line);
    }

    .form-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

    .section-title {
        color: var(--brand-red);
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title i {
        color: var(--brand-gold);
    }

    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-label {
        font-weight: 600;
        color: var(--charcoal);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .required {
        color: var(--brand-red);
    }

    .form-control {
        padding: 0.75rem;
        border: 2px solid var(--line);
        border-radius: 8px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--brand-red);
        box-shadow: 0 0 0 3px rgba(214, 40, 40, 0.1);
    }

    .nav-tabs {
        border: none;
        margin-bottom: 2rem;
    }

    .nav-tabs .nav-link {
        background: var(--gray-100);
        color: var(--slate);
        border: 2px solid var(--line);
        border-radius: 12px 12px 0 0;
        padding: 1rem 2rem;
        font-weight: 600;
        margin-left: 0.5rem;
        transition: all 0.3s ease;
    }

    .nav-tabs .nav-link.active {
        background: var(--brand-red);
        color: var(--white);
        border-color: var(--brand-red);
    }

    .tab-content {
        display: none;
        background: var(--white);
        border: 2px solid var(--line);
        border-radius: 0 12px 12px 12px;
        padding: 2rem;
    }

    .tab-content.active {
        display: block;
    }

    .items-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 1rem;
    }

    .items-table th,
    .items-table td {
        padding: 0.75rem;
        text-align: right;
        border: 1px solid var(--line);
    }

    .items-table th {
        background: var(--gray-100);
        font-weight: 600;
        color: var(--charcoal);
    }

    .items-table input,
    .items-table select {
        width: 100%;
        padding: 0.5rem;
        border: 1px solid var(--line);
        border-radius: 4px;
    }

    .btn-add-row {
        background: var(--brand-gold);
        color: var(--white);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 1rem;
    }

    .btn-add-row:hover {
        background: var(--brand-gold-dark);
    }

    .btn-remove {
        background: var(--error);
        color: var(--white);
        border: none;
        padding: 0.5rem;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.8rem;
    }

    .excel-upload {
        border: 2px dashed var(--line);
        border-radius: 12px;
        padding: 3rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .excel-upload:hover {
        border-color: var(--brand-red);
        background: rgba(214, 40, 40, 0.05);
    }

    .excel-upload.dragover {
        border-color: var(--brand-red);
        background: rgba(214, 40, 40, 0.1);
    }

    .upload-icon {
        font-size: 3rem;
        color: var(--brand-gold);
        margin-bottom: 1rem;
    }

    .totals-section {
        background: var(--gray-50);
        border-radius: 12px;
        padding: 1.5rem;
        margin-top: 2rem;
    }

    .totals-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .totals-row:last-child {
        margin-bottom: 0;
        font-weight: 700;
        font-size: 1.1rem;
        color: var(--brand-red);
        border-top: 1px solid var(--line);
        padding-top: 0.5rem;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }

    .btn-save {
        background: var(--brand-red);
        color: var(--white);
        border: none;
        padding: 1rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 4px 15px rgba(214, 40, 40, 0.3);
    }

    .btn-save:hover {
        background: var(--brand-red-dark);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(214, 40, 40, 0.4);
    }

    .btn-cancel {
        background: var(--gray-500);
        color: var(--white);
        border: none;
        padding: 1rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-cancel:hover {
        background: var(--gray-600);
        color: var(--white);
        text-decoration: none;
    }
    
    #excelDataTable {
        display: none;
        margin-top: 2rem;
    }
    
    .alert {
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 8px;
    }
    
    .alert-warning {
        background-color: #fff3cd;
        border-color: #ffeaa7;
        color: #856404;
    }
    
    .alert-danger {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }
    
    .alert-success {
        background-color: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }
    
    .badge {
        display: inline-block;
        padding: 0.25em 0.4em;
        font-size: 75%;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;
    }
    
    .bg-success {
        background-color: #28a745 !important;
    }
    
    .bg-warning {
        background-color: #ffc107 !important;
    }
    
    .bg-danger {
        background-color: #dc3545 !important;
    }
    
    .bg-info {
        background-color: #17a2b8 !important;
    }
    
    .bg-secondary {
        background-color: #6c757d !important;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// متغيرات عامة
let itemRowCount = 1;

// دالة لإنشاء خيارات الأصناف
function getItemsOptions() {
    return `
        {% for item in items %}
        <option value="{{ item.id }}" data-code="{{ item.item_code }}" data-unit="{{ item.unit_of_measure }}">{{ item.item_name_ar }} ({{ item.item_code }})</option>
        {% endfor %}
    `;
}

// دالة لإنشاء خيارات مواقع التخزين
function getBinLocationsOptions() {
    return `
        <option value="">اختر موقع التخزين</option>
        {% for bin in bin_locations %}
        <option value="{{ bin.id }}">{{ bin.location_code }}</option>
        {% endfor %}
    `;
}

// تم حذف وظيفة switchTab لأنه لم تعد هناك تبويبات

// إضافة صف جديد للأصناف
function addItemRow() {
    const tableBody = document.getElementById('itemsTableBody');
    const rowNumber = itemRowCount++;

    const row = document.createElement('tr');
    row.innerHTML = `
        <td>
            <input type="text" name="item_code_${rowNumber}" class="form-control" placeholder="كود الصنف" readonly>
        </td>
        <td>
            <select name="item_${rowNumber}" class="form-control" required onchange="updateItemInfo(this, ${rowNumber})">
                <option value="">اختر الصنف</option>
                ${getItemsOptions()}
            </select>
        </td>
        <td>
            <input type="number" name="quantity_${rowNumber}" class="form-control" step="0.001" min="0.001" required onchange="updateTotals()">
        </td>
        <td>
            <input type="text" name="unit_${rowNumber}" class="form-control" readonly>
        </td>
        <td>
            <select name="bin_location_${rowNumber}" class="form-control">
                ${getBinLocationsOptions()}
            </select>
        </td>
        <td>
            <button type="button" class="btn-remove" onclick="removeItemRow(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

    tableBody.appendChild(row);
    updateItemCount();
}

// حذف صف
function removeItemRow(button) {
    button.closest('tr').remove();
    updateItemCount();
    updateTotals();
}

// تحديث معلومات الصنف
function updateItemInfo(select, rowNumber) {
    const selectedOption = select.options[select.selectedIndex];
    const itemCode = selectedOption.getAttribute('data-code') || '';
    const itemUnit = selectedOption.getAttribute('data-unit') || '';

    // تحديث كود الصنف والوحدة
    document.querySelector(`input[name="item_code_${rowNumber}"]`).value = itemCode;
    document.querySelector(`input[name="unit_${rowNumber}"]`).value = itemUnit;

    updateTotals();
}

// تحديث عدد الأصناف
function updateItemCount() {
    const count = document.querySelectorAll('#itemsTableBody tr').length;
    document.getElementById('itemCount').value = count;
    document.getElementById('totalItems').textContent = count;
}

// تحديث المجاميع
function updateTotals() {
    let totalQuantity = 0;

    document.querySelectorAll('input[name^="quantity_"]').forEach(input => {
        const quantity = parseFloat(input.value) || 0;
        totalQuantity += quantity;
    });

    document.getElementById('totalQuantity').textContent = totalQuantity.toFixed(3);
    updateItemCount();
}

// تم حذف وظائف معالجة Excel (handleExcelFile, displayExcelPreview, displayExcelError, getStatusClass)

document.addEventListener('DOMContentLoaded', function() {
    // إضافة صف افتراضي
    addItemRow();

    // التحقق من صحة النموذج قبل الإرسال
    document.getElementById('issueForm').addEventListener('submit', function(e) {
        console.log('=== تم الضغط على زر الحفظ ===');
        
        // التحقق من تحديد نوع الصرف
        const issueType = document.querySelector('select[name="issue_type"]').value;
        console.log('نوع الصرف:', issueType);
        if (!issueType) {
            e.preventDefault();
            alert('يجب تحديد نوع الصرف');
            return;
        }

        // معالجة الإدخال اليدوي فقط
        console.log('=== معالجة الإدخال اليدوي ===');
        const rows = document.querySelectorAll('#itemsTableBody tr');
        console.log('عدد الصفوف:', rows.length);
        
        if (rows.length === 0) {
            e.preventDefault();
            alert('يجب إضافة صنف واحد على الأقل');
            return;
        }

        let hasValidRow = false;
        rows.forEach((row, index) => {
            const itemSelect = row.querySelector('select[name^="item_"]');
            const quantityInput = row.querySelector('input[name^="quantity_"]');
            console.log(`الصف ${index + 1}: الصنف=${itemSelect ? itemSelect.value : 'غير موجود'}, الكمية=${quantityInput ? quantityInput.value : 'غير موجود'}`);

            if (itemSelect && itemSelect.value && quantityInput && quantityInput.value && parseFloat(quantityInput.value) > 0) {
                hasValidRow = true;
            }
        });

        if (!hasValidRow) {
            e.preventDefault();
            alert('يجب إدخال بيانات صحيحة لصنف واحد على الأقل');
            return;
        }

        // إظهار رسالة التحميل
        const submitButton = document.querySelector('.btn-save');
        if (submitButton) {
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
            submitButton.disabled = true;
        }
        
        console.log('=== تم التحقق بنجاح، سيتم إرسال النموذج ===');
    });
    
    // تم حذف معالجة التبويبات لأنها لم تعد موجودة
});
</script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <h1>
            <i class="fas fa-plus-circle me-2"></i>
            إنشاء إذن صرف جديد
        </h1>
        <a href="{% url 'inventory:goods_issue_dashboard' %}" class="btn-back">
            <i class="fas fa-arrow-right"></i>
            العودة
        </a>
    </div>

    <!-- نموذج الإنشاء -->
    <div class="form-container">
        <form method="post" enctype="multipart/form-data" id="issueForm">
            {% csrf_token %}
            
            <!-- معلومات الإذن -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    معلومات الإذن
                </h3>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">
                            نوع الصرف <span class="required">*</span>
                        </label>
                        <select name="issue_type" class="form-control" required>
                            <option value="">اختر نوع الصرف</option>
                            <option value="SALE">بيع</option>
                            <option value="DAMAGE">تلف</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">المخزن</label>
                        <input type="text" 
                               class="form-control" 
                               value="{{ user_warehouse.warehouse_name }}" 
                               readonly>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">التاريخ</label>
                        <input type="text" 
                               class="form-control" 
                               value="{{ 'now'|date:'Y-m-d H:i' }}" 
                               readonly>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">ملاحظات</label>
                        <textarea name="notes" 
                                  class="form-control" 
                                  rows="3" 
                                  placeholder="ملاحظات إضافية (اختياري)"></textarea>
                    </div>
                </div>
            </div>

            <!-- تبويبات الإدخال - تم إزالة التبويب -->
            <div class="form-section">
                <h3 class="section-title">
                    <i class="fas fa-list"></i>
                    أصناف الصرف
                </h3>
                
                <!-- تم إزالة التبويبات والإبقاء على الإدخال اليدوي فقط -->
                <div id="manual-content" class="tab-content active">
                    <table class="items-table" id="itemsTable">
                        <thead>
                            <tr>
                                <th>كود الصنف <span class="required">*</span></th>
                                <th>اسم الصنف <span class="required">*</span></th>
                                <th>الكمية <span class="required">*</span></th>
                                <th>الوحدة <span class="required">*</span></th>
                                <th>موقع التخزين</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="itemsTableBody">
                            <!-- سيتم إضافة الصفوف هنا بـ JavaScript -->
                        </tbody>
                    </table>
                    
                    <button type="button" class="btn-add-row" onclick="addItemRow()">
                        <i class="fas fa-plus me-1"></i>
                        إضافة صف جديد
                    </button>
                </div>
            </div>

            <!-- المجاميع -->
            <div class="totals-section">
                <div class="totals-row">
                    <span>عدد الأصناف:</span>
                    <span id="totalItems">0</span>
                </div>
                <div class="totals-row">
                    <span>إجمالي الكمية:</span>
                    <span id="totalQuantity">0</span>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="action-buttons">
                <button type="submit" class="btn-save">
                    <i class="fas fa-save me-2"></i>
                    حفظ الإذن
                </button>
                <a href="{% url 'inventory:goods_issue_dashboard' %}" class="btn-cancel">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </a>
            </div>

            <!-- حقول مخفية -->
            <input type="hidden" name="item_count" id="itemCount" value="0">
            <input type="hidden" name="data_source" id="dataSource" value="manual">
        </form>
    </div>
</div>
{% endblock %}