/**
 * KamaVerse Items Management JavaScript
 * جافاسكريبت إدارة الأصناف
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Auto-submit form on filter change
    const filterSelects = document.querySelectorAll('select[name="category"], select[name="material_type"], select[name="hazard_level"], select[name="status"]');
    filterSelects.forEach(function(select) {
        select.addEventListener('change', function() {
            showLoading();
            this.form.submit();
        });
    });

    // Search functionality
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        // Search on Enter key
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                showLoading();
                this.form.submit();
            }
        });

        // Clear search button
        const clearSearchBtn = document.createElement('button');
        clearSearchBtn.type = 'button';
        clearSearchBtn.className = 'btn btn-outline-secondary';
        clearSearchBtn.innerHTML = '<i class="fas fa-times"></i>';
        clearSearchBtn.title = 'مسح البحث';
        clearSearchBtn.style.position = 'absolute';
        clearSearchBtn.style.right = '10px';
        clearSearchBtn.style.top = '50%';
        clearSearchBtn.style.transform = 'translateY(-50%)';
        clearSearchBtn.style.zIndex = '10';
        clearSearchBtn.style.padding = '0.25rem 0.5rem';
        clearSearchBtn.style.fontSize = '0.875rem';

        if (searchInput.value.trim() !== '') {
            searchInput.parentElement.style.position = 'relative';
            searchInput.parentElement.appendChild(clearSearchBtn);
            searchInput.style.paddingRight = '45px';
        }

        clearSearchBtn.addEventListener('click', function() {
            searchInput.value = '';
            showLoading();
            searchInput.form.submit();
        });
    }

    // Table row click functionality
    const tableRows = document.querySelectorAll('.table tbody tr');
    tableRows.forEach(function(row) {
        row.addEventListener('click', function(e) {
            // Don't trigger if clicking on action buttons
            if (e.target.closest('.btn-group')) {
                return;
            }
            
            // Add selection effect
            tableRows.forEach(r => r.classList.remove('table-active'));
            this.classList.add('table-active');
            
            // You can add navigation to item detail page here
            // const itemId = this.dataset.itemId;
            // if (itemId) {
            //     window.location.href = `/items/${itemId}/`;
            // }
        });
    });

    // Confirm delete actions
    const deleteButtons = document.querySelectorAll('.btn-outline-danger');
    deleteButtons.forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            
            const itemName = this.closest('tr').querySelector('strong').textContent;
            
            if (confirm(`هل أنت متأكد من حذف الصنف "${itemName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                // الذهاب إلى صفحة تأكيد الحذف
                showLoading();
                window.location.href = this.href;
            }
        });
    });

    // Export functionality
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            const format = this.dataset.format || 'excel';
            exportItems(format);
        });
    }

    // Print functionality
    const printBtn = document.getElementById('printBtn');
    if (printBtn) {
        printBtn.addEventListener('click', function() {
            // تحديث تاريخ ووقت الطباعة
            const now = new Date();
            const printDate = now.toLocaleDateString('ar-SA');
            const printTime = now.toLocaleTimeString('ar-SA');

            const printDateElement = document.getElementById('printDate');
            const printTimeElement = document.getElementById('printTime');

            if (printDateElement) printDateElement.textContent = printDate;
            if (printTimeElement) printTimeElement.textContent = printTime;

            // تأخير قصير للسماح بتحديث DOM
            setTimeout(() => {
                window.print();
            }, 100);
        });
    }

    // Bulk selection functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            itemCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }

    itemCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateBulkActions();
            
            // Update select all checkbox
            const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === itemCheckboxes.length;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < itemCheckboxes.length;
        });
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+F for search
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            if (searchInput) {
                searchInput.focus();
                searchInput.select();
            }
        }
        
        // Ctrl+N for new item
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            const addBtn = document.querySelector('.btn-add-item');
            if (addBtn) {
                addBtn.click();
            }
        }
        
        // Escape to clear search
        if (e.key === 'Escape') {
            if (searchInput && searchInput.value.trim() !== '') {
                searchInput.value = '';
                showLoading();
                searchInput.form.submit();
            }
        }
    });

    // Smooth scrolling for pagination
    const paginationLinks = document.querySelectorAll('.pagination .page-link');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            showLoading();
            // Scroll to top of table
            document.querySelector('.items-table').scrollIntoView({ 
                behavior: 'smooth',
                block: 'start'
            });
        });
    });

});

// Helper Functions

function showLoading() {
    const loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'loadingOverlay';
    loadingOverlay.innerHTML = `
        <div class="d-flex justify-content-center align-items-center h-100">
            <div class="text-center">
                <div class="loading mb-3"></div>
                <p class="text-muted">جاري التحميل...</p>
            </div>
        </div>
    `;
    loadingOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        z-index: 9999;
        backdrop-filter: blur(2px);
    `;
    document.body.appendChild(loadingOverlay);
}

function hideLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.remove();
    }
}

function updateBulkActions() {
    const checkedItems = document.querySelectorAll('.item-checkbox:checked');
    const bulkActionsDiv = document.getElementById('bulkActions');
    
    if (bulkActionsDiv) {
        if (checkedItems.length > 0) {
            bulkActionsDiv.style.display = 'block';
            bulkActionsDiv.querySelector('.selected-count').textContent = checkedItems.length;
        } else {
            bulkActionsDiv.style.display = 'none';
        }
    }
}

function exportItems(format) {
    showLoading();

    // Get current filters
    const form = document.querySelector('.search-filters form');
    const formData = new FormData(form);

    // Build export URL with current filters
    let exportUrl;
    if (format === 'pdf') {
        exportUrl = '/items/export/pdf/';
    } else {
        exportUrl = '/items/export/excel/';
    }

    // Add filter parameters to URL
    const params = new URLSearchParams();
    for (let [key, value] of formData.entries()) {
        if (value && value.trim() !== '') {
            params.append(key, value);
        }
    }

    // Build final URL
    const finalUrl = exportUrl + (params.toString() ? '?' + params.toString() : '');

    // Navigate to export URL
    window.location.href = finalUrl;

    setTimeout(hideLoading, 1000);
}

function filterByCategory(category) {
    const categorySelect = document.querySelector('select[name="category"]');
    if (categorySelect) {
        categorySelect.value = category;
        showLoading();
        categorySelect.form.submit();
    }
}

function filterByHazardLevel(level) {
    const hazardSelect = document.querySelector('select[name="hazard_level"]');
    if (hazardSelect) {
        hazardSelect.value = level;
        showLoading();
        hazardSelect.form.submit();
    }
}

// Animation for stats cards
function animateStatsCards() {
    const statNumbers = document.querySelectorAll('.stat-number');
    
    statNumbers.forEach(stat => {
        const finalValue = parseInt(stat.textContent);
        let currentValue = 0;
        const increment = finalValue / 50;
        
        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                stat.textContent = finalValue;
                clearInterval(timer);
            } else {
                stat.textContent = Math.floor(currentValue);
            }
        }, 20);
    });
}

// Initialize animations when page loads
window.addEventListener('load', function() {
    animateStatsCards();
    hideLoading();
});

// Handle form submission loading
document.addEventListener('submit', function(e) {
    if (e.target.matches('.search-filters form')) {
        showLoading();
    }
});
