#!/usr/bin/env python
"""
KamaVerse - نظام ERP متكامل لشركة القماش
Django's command-line utility for administrative tasks.
"""
import os
import sys


def main():
    """Run administrative tasks."""
    # Set default settings module to development
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse.settings')
    os.environ.setdefault('DJANGO_ENVIRONMENT', 'development')

    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc

    # Print startup message
    if len(sys.argv) > 1 and sys.argv[1] == 'runserver':
        print("🚀 بدء تشغيل KamaVerse - نظام ERP لشركة القماش")
        print("🌐 Environment:", os.environ.get('DJANGO_ENVIRONMENT', 'development'))

    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    main()
