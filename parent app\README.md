# KamaVerse - نظام إدارة متكامل لشركة القماش

## نظرة عامة

KamaVerse هو نظام ERP متكامل مصمم خصيصاً لشركة القماش للاستيراد والتصدير. يوفر النظام إدارة شاملة لجميع عمليات الشركة من الاستيراد إلى البيع النهائي.

## الميزات الرئيسية

### 🏢 الجداول المرتبطة (Key Tables)
- **إدارة المستخدمين**: نظام صلاحيات متقدم مع مستويات مختلفة
- **إدارة الشركات**: موردين وعملاء وشركاء
- **إدارة المنتجات**: مواد كيماوية وخامات بلاستيك
- **المعاملات المالية**: تتبع شامل للمعاملات
- **إدارة المستندات**: أرشفة إلكترونية متقدمة
- **نظام الموافقات**: سير عمل للموافقات
- **إدارة الشحنات**: تتبع الشحنات الواردة والصادرة
- **حركة المخزون**: تسجيل دقيق لحركات المخزون
- **نظام الإشعارات**: تنبيهات فورية
- **سجل العمليات**: تدقيق شامل للعمليات

### 📦 الموديولات الأساسية
1. **موديول الاستيراد**: إدارة عمليات الاستيراد والموردين
2. **موديول المخزون**: إدارة المواد الخام والمنتجات
3. **موديول المالية**: إدارة الحسابات والمعاملات المالية
4. **موديول المبيعات**: إدارة المبيعات والعملاء
5. **موديول CRM**: إدارة علاقات العملاء
6. **موديول الموارد البشرية**: إدارة الموظفين والرواتب
7. **موديول اللوجستيات**: إدارة الشحن والتوصيل
8. **موديول التقارير**: تقارير وتحليلات شاملة

### 📱 التطبيقات المحمولة
- **KamaChat**: نظام محادثة داخلية للموظفين
- **Hawk**: لوحة تحكم الإدارة العليا

## التقنيات المستخدمة

- **Backend**: Django 4.2+ مع Django REST Framework
- **Database**: SQLite (تطوير) / PostgreSQL (إنتاج)
- **Frontend**: HTML5, CSS3, Bootstrap 5 RTL, JavaScript
- **Authentication**: نظام مصادقة مخصص مع صلاحيات متقدمة
- **Real-time**: Django Channels (سيتم إضافته لاحقاً)
- **Task Queue**: Celery (سيتم إضافته لاحقاً)

## متطلبات النظام

- Python 3.10+
- Django 4.2+
- SQLite (للتطوير)
- PostgreSQL (للإنتاج)

## التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd kamaverse
```

### 2. إنشاء البيئة الافتراضية
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```bash
python manage.py makemigrations
python manage.py migrate
```

### 5. إنشاء مستخدم أساسي
```bash
python manage.py createsuperuser
```

### 6. تشغيل الخادم
```bash
python manage.py runserver
```

## الاستخدام

### تسجيل الدخول
1. افتح المتصفح وانتقل إلى `http://127.0.0.1:8000`
2. سيتم توجيهك تلقائياً إلى صفحة تسجيل الدخول
3. استخدم بيانات المستخدم الأساسي للدخول

### الصفحة الرئيسية
- عرض الموديولات المتاحة حسب صلاحيات المستخدم
- إحصائيات سريعة
- النشاط الأخير والإشعارات
- إجراءات سريعة

## هيكل المشروع

```
kamaverse/
├── kamaverse/              # إعدادات المشروع الرئيسية
│   ├── settings/          # إعدادات منفصلة للبيئات المختلفة
│   │   ├── base.py       # الإعدادات الأساسية
│   │   ├── development.py # إعدادات التطوير
│   │   ├── production.py  # إعدادات الإنتاج
│   │   └── testing.py     # إعدادات الاختبار
│   ├── urls.py           # الروابط الرئيسية
│   └── wsgi.py           # إعدادات WSGI
├── core/                  # النواة الأساسية
│   ├── models/           # الجداول المرتبطة
│   ├── admin.py          # إعدادات لوحة الإدارة
│   └── signals.py        # إشارات النظام
├── modules/              # الموديولات الأساسية
│   └── authentication/   # نظام المصادقة
├── mobile_apps/          # التطبيقات المحمولة
├── templates/            # قوالب HTML
├── static/               # الملفات الثابتة
├── media/                # ملفات المستخدمين
└── logs/                 # ملفات السجلات
```

## الحالة الحالية

### ✅ مكتمل
- [x] إعداد البيئة الأساسية للمشروع
- [x] إنشاء الجداول المرتبطة (Key Tables)
- [x] نظام المصادقة الأساسي
- [x] الصفحة الرئيسية (Dashboard)
- [x] لوحة الإدارة

### 🚧 قيد التطوير
- [ ] تطوير الموديولات الأساسية
- [ ] التطبيقات المحمولة
- [ ] نظام التقارير المتقدم
- [ ] التكامل مع الأنظمة الخارجية

## المساهمة

هذا مشروع خاص لشركة القماش للاستيراد والتصدير.

## الدعم

للدعم الفني، يرجى التواصل مع فريق تقنية المعلومات.

## الترخيص

جميع الحقوق محفوظة لشركة القماش للاستيراد والتصدير © 2024

---

**KamaVerse** - نظام إدارة متكامل لشركة القماش 🚀
