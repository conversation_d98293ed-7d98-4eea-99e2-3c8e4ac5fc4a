"""
أمر إدارة لدمج الأصناف المكررة في المخازن
Management command to merge duplicate items in warehouses
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Sum, Q
from inventory.models import StockBalance, ItemMaster
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'دمج الأصناف المكررة في المخازن وجمع كمياتها'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='عرض النتائج فقط بدون تطبيق التغييرات',
        )
        parser.add_argument(
            '--warehouse',
            type=str,
            help='دمج الأصناف في مخزن محدد فقط (كود المخزن)',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        warehouse_code = options.get('warehouse')
        
        self.stdout.write(
            self.style.SUCCESS('🔄 بدء عملية دمج الأصناف المكررة...')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('⚠️  وضع المعاينة - لن يتم تطبيق أي تغييرات')
            )

        # فلترة المخازن إذا تم تحديد مخزن معين
        warehouse_filter = Q()
        if warehouse_code:
            warehouse_filter = Q(warehouse__warehouse_code=warehouse_code)

        # البحث عن الأصناف المكررة في كل مخزن
        duplicates_found = self.find_duplicate_items(warehouse_filter)
        
        if not duplicates_found:
            self.stdout.write(
                self.style.SUCCESS('✅ لا توجد أصناف مكررة للدمج')
            )
            return

        # دمج الأصناف المكررة
        merged_count = 0
        total_quantity_merged = Decimal('0')
        
        for warehouse_id, item_name, duplicates in duplicates_found:
            if not dry_run:
                quantity_merged = self.merge_duplicate_items(duplicates)
                merged_count += len(duplicates) - 1  # عدد الأصناف المدموجة
                total_quantity_merged += quantity_merged
            else:
                # في وضع المعاينة، فقط عرض ما سيتم دمجه
                total_qty = sum(d['current_quantity'] for d in duplicates)
                self.stdout.write(
                    f"📦 سيتم دمج {len(duplicates)} أصناف من '{item_name}' "
                    f"بإجمالي كمية {total_qty}"
                )

        if not dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f'✅ تم دمج {merged_count} صنف مكرر بنجاح\n'
                    f'📊 إجمالي الكمية المدموجة: {total_quantity_merged}'
                )
            )
        else:
            self.stdout.write(
                self.style.WARNING('💡 استخدم الأمر بدون --dry-run لتطبيق التغييرات')
            )

    def find_duplicate_items(self, warehouse_filter):
        """البحث عن الأصناف المكررة"""
        duplicates = []
        
        # الحصول على جميع أرصدة المخزون مع فلترة المخزن
        stock_balances = StockBalance.objects.filter(
            warehouse_filter,
            current_quantity__gt=0
        ).select_related('item', 'warehouse', 'bin_location')

        # تجميع الأصناف حسب المخزن واسم الصنف
        warehouse_items = {}
        for stock in stock_balances:
            warehouse_id = stock.warehouse.id
            item_name = stock.item.item_name_ar.strip().lower()
            
            if warehouse_id not in warehouse_items:
                warehouse_items[warehouse_id] = {}
            
            if item_name not in warehouse_items[warehouse_id]:
                warehouse_items[warehouse_id][item_name] = []
            
            warehouse_items[warehouse_id][item_name].append({
                'stock_balance': stock,
                'current_quantity': stock.current_quantity,
                'item_code': stock.item.item_code,
                'bin_location': stock.bin_location.bin_code,
            })

        # البحث عن المكررات
        for warehouse_id, items in warehouse_items.items():
            for item_name, item_list in items.items():
                if len(item_list) > 1:
                    # عرض تفاصيل الأصناف المكررة
                    self.stdout.write(
                        f"\n🔍 أصناف مكررة في المخزن {item_list[0]['stock_balance'].warehouse.warehouse_name}:"
                    )
                    self.stdout.write(f"   📝 اسم الصنف: {item_name}")
                    
                    for item in item_list:
                        self.stdout.write(
                            f"   • كود: {item['item_code']} | "
                            f"كمية: {item['current_quantity']} | "
                            f"موقع: {item['bin_location']}"
                        )
                    
                    duplicates.append((warehouse_id, item_name, item_list))

        return duplicates

    @transaction.atomic
    def merge_duplicate_items(self, duplicates):
        """دمج الأصناف المكررة"""
        if len(duplicates) < 2:
            return Decimal('0')

        # اختيار الصنف الأساسي (الأول في القائمة)
        primary_stock = duplicates[0]['stock_balance']
        total_quantity = Decimal('0')
        
        # جمع الكميات من جميع الأصناف المكررة
        for duplicate in duplicates:
            total_quantity += duplicate['current_quantity']

        # تحديث الصنف الأساسي بالكمية الإجمالية
        primary_stock.current_quantity = total_quantity
        primary_stock.available_quantity = total_quantity - primary_stock.reserved_quantity
        
        # إعادة حساب القيمة الإجمالية
        if primary_stock.average_cost:
            primary_stock.total_value = primary_stock.average_cost * total_quantity
        
        primary_stock.save()

        # حذف الأصناف المكررة الأخرى
        for i in range(1, len(duplicates)):
            duplicate_stock = duplicates[i]['stock_balance']
            self.stdout.write(
                f"🗑️  حذف الصنف المكرر: {duplicate_stock.item.item_code} "
                f"(كمية: {duplicate_stock.current_quantity})"
            )
            duplicate_stock.delete()

        self.stdout.write(
            f"✅ تم دمج {len(duplicates)} أصناف في الصنف الأساسي: "
            f"{primary_stock.item.item_code} (إجمالي الكمية: {total_quantity})"
        )

        return total_quantity
