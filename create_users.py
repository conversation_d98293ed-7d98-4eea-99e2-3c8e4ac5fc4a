#!/usr/bin/env python
"""
إنشاء المستخدمين الخمسة لنظام إذن الاستلام
Create 5 users for Goods Receipt system
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

from django.contrib.auth.models import User

def create_users():
    """إنشاء المستخدمين الخمسة"""
    print("🚀 بدء إنشاء المستخدمين...")
    
    users_data = [
        {
            'username': 'manager',
            'email': '<EMAIL>',
            'first_name': 'أحمد',
            'last_name': 'محمد',
            'password': 'manager123',
            'role': 'مدير المخازن',
            'warehouse': 'جميع المخازن'
        },
        {
            'username': 'cairo_manager',
            'email': '<EMAIL>',
            'first_name': 'محمد',
            'last_name': 'علي',
            'password': 'cairo123',
            'role': 'مسؤول مخزن القاهرة',
            'warehouse': 'مخزن القاهرة'
        },
        {
            'username': 'daery_manager',
            'email': '<EMAIL>',
            'first_name': 'علي',
            'last_name': 'حسن',
            'password': 'daery123',
            'role': 'مسؤول مخزن الدائري',
            'warehouse': 'مخزن الدائري'
        },
        {
            'username': 'menoufia_manager',
            'email': '<EMAIL>',
            'first_name': 'حسن',
            'last_name': 'أحمد',
            'password': 'menoufia123',
            'role': 'مسؤول مخزن المنوفية',
            'warehouse': 'مخزن المنوفية'
        },
        {
            'username': 'alex_manager',
            'email': '<EMAIL>',
            'first_name': 'محمود',
            'last_name': 'سالم',
            'password': 'alex123',
            'role': 'مسؤول مخزن الإسكندرية',
            'warehouse': 'مخزن الإسكندرية'
        }
    ]
    
    created_count = 0
    
    for user_data in users_data:
        user, created = User.objects.get_or_create(
            username=user_data['username'],
            defaults={
                'email': user_data['email'],
                'first_name': user_data['first_name'],
                'last_name': user_data['last_name'],
                'is_staff': True,  # يمكنهم الدخول للـ admin
                'is_active': True,
            }
        )
        
        if created:
            user.set_password(user_data['password'])
            user.save()
            created_count += 1
            print(f"✅ تم إنشاء المستخدم: {user_data['first_name']} {user_data['last_name']} ({user_data['username']}) - {user_data['role']}")
        else:
            print(f"ℹ️ المستخدم موجود بالفعل: {user_data['username']} - {user_data['role']}")
    
    print(f"\n🎉 تم إنشاء {created_count} مستخدم جديد من أصل {len(users_data)} مستخدم")
    
    # عرض معلومات تسجيل الدخول
    print("\n📋 معلومات تسجيل الدخول:")
    print("=" * 50)
    for user_data in users_data:
        print(f"👤 {user_data['role']}")
        print(f"   اسم المستخدم: {user_data['username']}")
        print(f"   كلمة المرور: {user_data['password']}")
        print(f"   المخزن المخصص: {user_data['warehouse']}")
        print()

if __name__ == '__main__':
    create_users()
