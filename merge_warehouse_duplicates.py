#!/usr/bin/env python
"""
Merge duplicate items within each warehouse

This script:
1. Finds all warehouses
2. For each warehouse, finds duplicate items
3. Merges quantities of duplicate items
4. Preserves all other attributes
5. Deletes the redundant entries after merging

No other changes are made to the system.
"""

import os
import sys
import django
from decimal import Decimal
from collections import defaultdict

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

from django.db import transaction
from inventory.models import Warehouse, StockBalance, ItemMaster, StockMovement
from django.db.models import Sum

def merge_duplicates_in_warehouse():
    """
    Merge duplicate items in each warehouse
    """
    print("\n🔍 Starting to merge duplicate items in all warehouses...\n")
    print("=" * 70)
    
    # Get all warehouses
    warehouses = Warehouse.objects.all()
    print(f"Found {warehouses.count()} warehouses in the system")
    
    total_merged = 0
    
    # Process each warehouse
    for warehouse in warehouses:
        print(f"\n📦 Processing warehouse: {warehouse.warehouse_name}")
        
        # Get all items in this warehouse
        balances = StockBalance.objects.filter(warehouse=warehouse)
        
        # Group items by their ItemMaster id
        items_by_id = defaultdict(list)
        for balance in balances:
            items_by_id[balance.item.id].append(balance)
        
        # Find duplicates (items with more than one entry)
        duplicates = {item_id: balances_list for item_id, balances_list in items_by_id.items() 
                     if len(balances_list) > 1}
        
        if not duplicates:
            print(f"  ✅ No duplicates found in warehouse {warehouse.warehouse_name}")
            continue
        
        print(f"  ⚠️ Found {len(duplicates)} items with duplicates in warehouse {warehouse.warehouse_name}")
        
        # Process each set of duplicates
        for item_id, duplicate_balances in duplicates.items():
            item = ItemMaster.objects.get(id=item_id)
            print(f"  🔄 Merging duplicates for item: {item.item_name_ar}")
            
            # Keep the first balance as primary and merge others into it
            primary_balance = duplicate_balances[0]
            
            # Calculate the total quantity
            total_quantity = sum(balance.current_quantity for balance in duplicate_balances)
            
            # Calculate weighted average cost if applicable
            if all(hasattr(balance, 'average_cost') and balance.average_cost for balance in duplicate_balances):
                weighted_sum = sum(balance.average_cost * balance.current_quantity 
                                  for balance in duplicate_balances if balance.current_quantity > 0)
                total_positive_qty = sum(balance.current_quantity 
                                        for balance in duplicate_balances if balance.current_quantity > 0)
                if total_positive_qty > 0:
                    primary_balance.average_cost = weighted_sum / total_positive_qty
            
            # Update the primary balance with the total quantity
            primary_balance.current_quantity = total_quantity
            
            # Calculate available quantity if relevant
            if hasattr(primary_balance, 'reserved_quantity') and primary_balance.reserved_quantity:
                primary_balance.available_quantity = total_quantity - primary_balance.reserved_quantity
            
            # Save the updated primary balance
            with transaction.atomic():
                primary_balance.save()
                
                # Delete the other duplicate balances
                for balance in duplicate_balances[1:]:
                    balance.delete()
            
            total_merged += len(duplicate_balances) - 1
        
    print("\n" + "=" * 70)
    if total_merged > 0:
        print(f"\n✅ Successfully merged {total_merged} duplicate items across all warehouses!")
    else:
        print("\n✅ No duplicates found across all warehouses.")
    
    print("\n🎉 Duplicate item merging process completed!")
    return total_merged

if __name__ == "__main__":
    merge_duplicates_in_warehouse()