"""
KeyCompanies - جدول الشركات
إدارة بيانات جميع الشركات (موردين وعملاء)
"""

from django.db import models
from django.core.validators import RegexValidator, EmailValidator
from django.utils.translation import gettext_lazy as _
import uuid


class KeyCompany(models.Model):
    """
    نموذج الشركات الأساسي
    يشمل الموردين والعملاء والشركاء
    """
    
    COMPANY_TYPES = [
        ('SUPPLIER', 'مورد'),
        ('CUSTOMER', 'عميل'),
        ('BOTH', 'مورد وعميل'),
        ('PARTNER', 'شريك'),
        ('LOGISTICS', 'شركة شحن'),
        ('SERVICE', 'مقدم خدمة'),
    ]
    
    COMPANY_SIZES = [
        ('SMALL', 'صغيرة'),
        ('MEDIUM', 'متوسطة'),
        ('LARGE', 'كبيرة'),
        ('ENTERPRISE', 'مؤسسة'),
    ]
    
    COUNTRIES = [
        ('EG', 'مصر'),
        ('SA', 'السعودية'),
        ('AE', 'الإمارات'),
        ('CN', 'الصين'),
        ('IN', 'الهند'),
        ('TR', 'تركيا'),
        ('DE', 'ألمانيا'),
        ('US', 'الولايات المتحدة'),
        ('OTHER', 'أخرى'),
    ]
    
    PAYMENT_TERMS = [
        ('CASH', 'نقدي'),
        ('NET_30', '30 يوم'),
        ('NET_60', '60 يوم'),
        ('NET_90', '90 يوم'),
        ('ADVANCE', 'دفع مقدم'),
        ('COD', 'الدفع عند الاستلام'),
        ('LC', 'اعتماد مستندي'),
        ('CUSTOM', 'شروط خاصة'),
    ]
    
    # Primary key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Basic Information
    company_code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name='كود الشركة',
        help_text='كود فريد للشركة في النظام'
    )
    
    company_name_ar = models.CharField(
        max_length=200,
        verbose_name='اسم الشركة بالعربية'
    )
    
    company_name_en = models.CharField(
        max_length=200,
        verbose_name='اسم الشركة بالإنجليزية',
        blank=True,
        null=True
    )
    
    company_type = models.CharField(
        max_length=20,
        choices=COMPANY_TYPES,
        verbose_name='نوع الشركة'
    )
    
    company_size = models.CharField(
        max_length=20,
        choices=COMPANY_SIZES,
        default='MEDIUM',
        verbose_name='حجم الشركة'
    )
    
    # Contact Information
    primary_email = models.EmailField(
        verbose_name='البريد الإلكتروني الرئيسي',
        validators=[EmailValidator()]
    )
    
    secondary_email = models.EmailField(
        verbose_name='البريد الإلكتروني الثانوي',
        blank=True,
        null=True
    )
    
    primary_phone = models.CharField(
        max_length=20,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$', 'رقم الهاتف غير صحيح')],
        verbose_name='الهاتف الرئيسي'
    )
    
    secondary_phone = models.CharField(
        max_length=20,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$', 'رقم الهاتف غير صحيح')],
        verbose_name='الهاتف الثانوي',
        blank=True,
        null=True
    )
    
    fax = models.CharField(
        max_length=20,
        verbose_name='الفاكس',
        blank=True,
        null=True
    )
    
    website = models.URLField(
        verbose_name='الموقع الإلكتروني',
        blank=True,
        null=True
    )
    
    # Address Information
    country = models.CharField(
        max_length=10,
        choices=COUNTRIES,
        default='EG',
        verbose_name='الدولة'
    )
    
    city = models.CharField(
        max_length=100,
        verbose_name='المدينة'
    )
    
    address_line_1 = models.CharField(
        max_length=200,
        verbose_name='العنوان الأول'
    )
    
    address_line_2 = models.CharField(
        max_length=200,
        verbose_name='العنوان الثاني',
        blank=True,
        null=True
    )
    
    postal_code = models.CharField(
        max_length=20,
        verbose_name='الرمز البريدي',
        blank=True,
        null=True
    )
    
    # Business Information
    tax_number = models.CharField(
        max_length=50,
        verbose_name='الرقم الضريبي',
        blank=True,
        null=True
    )
    
    commercial_register = models.CharField(
        max_length=50,
        verbose_name='السجل التجاري',
        blank=True,
        null=True
    )
    
    industry = models.CharField(
        max_length=100,
        verbose_name='الصناعة/المجال',
        blank=True,
        null=True
    )
    
    established_year = models.PositiveIntegerField(
        verbose_name='سنة التأسيس',
        blank=True,
        null=True
    )
    
    # Financial Information
    payment_terms = models.CharField(
        max_length=20,
        choices=PAYMENT_TERMS,
        default='NET_30',
        verbose_name='شروط الدفع'
    )
    
    credit_limit = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name='الحد الائتماني'
    )
    
    currency = models.CharField(
        max_length=3,
        default='EGP',
        verbose_name='العملة الأساسية'
    )
    
    # Rating and Status
    rating = models.PositiveIntegerField(
        default=5,
        verbose_name='التقييم (1-10)',
        help_text='تقييم الشركة من 1 إلى 10'
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشطة'
    )
    
    is_approved = models.BooleanField(
        default=False,
        verbose_name='معتمدة'
    )
    
    is_blacklisted = models.BooleanField(
        default=False,
        verbose_name='في القائمة السوداء'
    )
    
    # Notes and Comments
    notes = models.TextField(
        verbose_name='ملاحظات',
        blank=True,
        null=True
    )
    
    internal_notes = models.TextField(
        verbose_name='ملاحظات داخلية',
        blank=True,
        null=True,
        help_text='ملاحظات للاستخدام الداخلي فقط'
    )
    
    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ آخر تحديث'
    )
    
    created_by = models.ForeignKey(
        'core.KeyUser',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='created_companies',
        verbose_name='أنشئ بواسطة'
    )

    approved_by = models.ForeignKey(
        'core.KeyUser',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='approved_companies',
        verbose_name='اعتمد بواسطة'
    )
    
    class Meta:
        verbose_name = 'شركة'
        verbose_name_plural = 'الشركات'
        ordering = ['company_name_ar']
        indexes = [
            models.Index(fields=['company_code']),
            models.Index(fields=['company_type']),
            models.Index(fields=['country']),
            models.Index(fields=['is_active']),
            models.Index(fields=['is_approved']),
            models.Index(fields=['rating']),
        ]
    
    def __str__(self):
        return f"{self.company_name_ar} ({self.company_code})"
    
    def get_full_address(self):
        """إرجاع العنوان الكامل"""
        address_parts = [self.address_line_1]
        if self.address_line_2:
            address_parts.append(self.address_line_2)
        address_parts.extend([self.city, self.get_country_display()])
        if self.postal_code:
            address_parts.append(self.postal_code)
        return ', '.join(address_parts)
    
    def get_primary_contact(self):
        """إرجاع معلومات الاتصال الأساسية"""
        return {
            'email': self.primary_email,
            'phone': self.primary_phone,
            'address': self.get_full_address()
        }
    
    def is_supplier(self):
        """التحقق من كون الشركة مورد"""
        return self.company_type in ['SUPPLIER', 'BOTH']
    
    def is_customer(self):
        """التحقق من كون الشركة عميل"""
        return self.company_type in ['CUSTOMER', 'BOTH']
    
    def can_transact(self):
        """التحقق من إمكانية التعامل مع الشركة"""
        return self.is_active and self.is_approved and not self.is_blacklisted


class KeyCompanyContact(models.Model):
    """
    جهات الاتصال في الشركات
    """
    
    CONTACT_TYPES = [
        ('PRIMARY', 'جهة اتصال رئيسية'),
        ('FINANCIAL', 'جهة اتصال مالية'),
        ('TECHNICAL', 'جهة اتصال فنية'),
        ('SALES', 'جهة اتصال مبيعات'),
        ('LOGISTICS', 'جهة اتصال شحن'),
        ('MANAGEMENT', 'إدارة عليا'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    company = models.ForeignKey(
        KeyCompany,
        on_delete=models.CASCADE,
        related_name='contacts',
        verbose_name='الشركة'
    )
    
    contact_type = models.CharField(
        max_length=20,
        choices=CONTACT_TYPES,
        verbose_name='نوع جهة الاتصال'
    )
    
    name = models.CharField(
        max_length=100,
        verbose_name='الاسم'
    )
    
    position = models.CharField(
        max_length=100,
        verbose_name='المنصب',
        blank=True,
        null=True
    )
    
    email = models.EmailField(
        verbose_name='البريد الإلكتروني',
        blank=True,
        null=True
    )
    
    phone = models.CharField(
        max_length=20,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$', 'رقم الهاتف غير صحيح')],
        verbose_name='الهاتف',
        blank=True,
        null=True
    )
    
    mobile = models.CharField(
        max_length=20,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$', 'رقم الهاتف غير صحيح')],
        verbose_name='الهاتف المحمول',
        blank=True,
        null=True
    )
    
    is_primary = models.BooleanField(
        default=False,
        verbose_name='جهة اتصال رئيسية'
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )
    
    notes = models.TextField(
        verbose_name='ملاحظات',
        blank=True,
        null=True
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ آخر تحديث'
    )
    
    class Meta:
        verbose_name = 'جهة اتصال'
        verbose_name_plural = 'جهات الاتصال'
        ordering = ['name']
        indexes = [
            models.Index(fields=['company', 'contact_type']),
            models.Index(fields=['is_primary']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return f"{self.name} - {self.company.company_name_ar}"
