"""
KeyProducts - جدول المنتجات والمواد الخام
إدارة جميع المواد الكيماوية والمنتجات
"""

from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
import uuid


class KeyProduct(models.Model):
    """
    نموذج المنتجات والمواد الخام
    مخصص للمواد الكيماوية وخامات البلاستيك
    """
    
    PRODUCT_CATEGORIES = [
        ('PLASTIC_RAW', 'خامات بلاستيك'),
        ('RUBBER_RAW', 'خامات مطاط'),
        ('CHEMICALS', 'مواد كيماوية'),
        ('ADDITIVES', 'إضافات'),
        ('COLORANTS', 'ملونات'),
        ('STABILIZERS', 'مثبتات'),
        ('FILLERS', 'حشوات'),
        ('RESINS', 'راتنجات'),
        ('OILS', 'زيوت'),
        ('ACIDS', 'أحماض'),
        ('OXIDES', 'أكاسيد'),
        ('FINISHED', 'منتجات نهائية'),
    ]
    
    UNITS_OF_MEASURE = [
        ('KG', 'كيلوجرام'),
        ('TON', 'طن'),
        ('LITER', 'لتر'),
        ('GALLON', 'جالون'),
        ('BAG', 'شيكارة'),
        ('DRUM', 'برميل'),
        ('CARTON', 'كرتونة'),
        ('PIECE', 'قطعة'),
        ('METER', 'متر'),
        ('ROLL', 'لفة'),
    ]
    
    HAZARD_LEVELS = [
        ('NONE', 'غير خطر'),
        ('LOW', 'خطر منخفض'),
        ('MEDIUM', 'خطر متوسط'),
        ('HIGH', 'خطر عالي'),
        ('EXTREME', 'خطر شديد'),
    ]
    
    STORAGE_CONDITIONS = [
        ('NORMAL', 'عادية'),
        ('COOL', 'باردة'),
        ('DRY', 'جافة'),
        ('COOL_DRY', 'باردة وجافة'),
        ('REFRIGERATED', 'مبردة'),
        ('FROZEN', 'مجمدة'),
        ('CONTROLLED', 'محكومة'),
    ]
    
    # Primary key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Product Identification
    product_code = models.CharField(
        max_length=30,
        unique=True,
        verbose_name='كود المنتج',
        help_text='كود فريد للمنتج في النظام'
    )
    
    barcode = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='الباركود',
        blank=True,
        null=True
    )
    
    sku = models.CharField(
        max_length=50,
        verbose_name='رقم المنتج (SKU)',
        blank=True,
        null=True
    )
    
    # Product Names
    product_name_ar = models.CharField(
        max_length=200,
        verbose_name='اسم المنتج بالعربية'
    )
    
    product_name_en = models.CharField(
        max_length=200,
        verbose_name='اسم المنتج بالإنجليزية',
        blank=True,
        null=True
    )
    
    commercial_name = models.CharField(
        max_length=200,
        verbose_name='الاسم التجاري',
        blank=True,
        null=True
    )
    
    scientific_name = models.CharField(
        max_length=200,
        verbose_name='الاسم العلمي',
        blank=True,
        null=True
    )
    
    # Product Classification
    category = models.CharField(
        max_length=20,
        choices=PRODUCT_CATEGORIES,
        verbose_name='فئة المنتج'
    )
    
    subcategory = models.CharField(
        max_length=100,
        verbose_name='الفئة الفرعية',
        blank=True,
        null=True
    )
    
    brand = models.CharField(
        max_length=100,
        verbose_name='العلامة التجارية',
        blank=True,
        null=True
    )
    
    manufacturer = models.ForeignKey(
        'core.KeyCompany',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='manufactured_products',
        verbose_name='الشركة المصنعة'
    )
    
    # Physical Properties
    unit_of_measure = models.CharField(
        max_length=20,
        choices=UNITS_OF_MEASURE,
        default='KG',
        verbose_name='وحدة القياس'
    )
    
    weight_per_unit = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        blank=True,
        null=True,
        verbose_name='الوزن لكل وحدة'
    )
    
    density = models.DecimalField(
        max_digits=8,
        decimal_places=4,
        blank=True,
        null=True,
        verbose_name='الكثافة (جم/سم³)'
    )
    
    color = models.CharField(
        max_length=50,
        verbose_name='اللون',
        blank=True,
        null=True
    )
    
    # Chemical Properties
    chemical_formula = models.CharField(
        max_length=100,
        verbose_name='الصيغة الكيميائية',
        blank=True,
        null=True
    )
    
    cas_number = models.CharField(
        max_length=20,
        verbose_name='رقم CAS',
        blank=True,
        null=True,
        help_text='Chemical Abstracts Service Number'
    )
    
    purity_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        blank=True,
        null=True,
        verbose_name='نسبة النقاء (%)'
    )
    
    # Safety Information
    hazard_level = models.CharField(
        max_length=20,
        choices=HAZARD_LEVELS,
        default='NONE',
        verbose_name='مستوى الخطورة'
    )
    
    safety_data_sheet = models.FileField(
        upload_to='products/sds/',
        blank=True,
        null=True,
        verbose_name='ورقة بيانات الأمان'
    )
    
    hazard_symbols = models.CharField(
        max_length=200,
        verbose_name='رموز الخطر',
        blank=True,
        null=True
    )
    
    # Storage Information
    storage_conditions = models.CharField(
        max_length=20,
        choices=STORAGE_CONDITIONS,
        default='NORMAL',
        verbose_name='شروط التخزين'
    )
    
    storage_temperature_min = models.IntegerField(
        verbose_name='أقل درجة حرارة للتخزين (°C)',
        blank=True,
        null=True
    )
    
    storage_temperature_max = models.IntegerField(
        verbose_name='أعلى درجة حرارة للتخزين (°C)',
        blank=True,
        null=True
    )
    
    shelf_life_months = models.PositiveIntegerField(
        verbose_name='مدة الصلاحية (شهر)',
        blank=True,
        null=True
    )
    
    # Packaging Information
    packaging_type = models.CharField(
        max_length=100,
        verbose_name='نوع التعبئة',
        blank=True,
        null=True
    )
    
    package_size = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        verbose_name='حجم العبوة',
        blank=True,
        null=True
    )
    
    packages_per_pallet = models.PositiveIntegerField(
        verbose_name='عدد العبوات في البالتة',
        blank=True,
        null=True
    )
    
    # Financial Information
    standard_cost = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        default=0,
        verbose_name='التكلفة المعيارية'
    )
    
    last_purchase_price = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        default=0,
        verbose_name='آخر سعر شراء'
    )
    
    selling_price = models.DecimalField(
        max_digits=15,
        decimal_places=4,
        default=0,
        verbose_name='سعر البيع'
    )
    
    currency = models.CharField(
        max_length=3,
        default='EGP',
        verbose_name='العملة'
    )
    
    # Status and Control
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )
    
    is_discontinued = models.BooleanField(
        default=False,
        verbose_name='متوقف'
    )
    
    requires_approval = models.BooleanField(
        default=False,
        verbose_name='يتطلب موافقة'
    )
    
    # Inventory Control
    minimum_stock_level = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        default=0,
        verbose_name='الحد الأدنى للمخزون'
    )
    
    maximum_stock_level = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        default=0,
        verbose_name='الحد الأقصى للمخزون'
    )
    
    reorder_point = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        default=0,
        verbose_name='نقطة إعادة الطلب'
    )
    
    # Additional Information
    description = models.TextField(
        verbose_name='الوصف',
        blank=True,
        null=True
    )
    
    technical_specifications = models.TextField(
        verbose_name='المواصفات الفنية',
        blank=True,
        null=True
    )
    
    applications = models.TextField(
        verbose_name='الاستخدامات',
        blank=True,
        null=True
    )
    
    notes = models.TextField(
        verbose_name='ملاحظات',
        blank=True,
        null=True
    )
    
    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ آخر تحديث'
    )
    
    created_by = models.ForeignKey(
        'core.KeyUser',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='created_products',
        verbose_name='أنشئ بواسطة'
    )
    
    class Meta:
        verbose_name = 'منتج'
        verbose_name_plural = 'المنتجات'
        ordering = ['product_name_ar']
        indexes = [
            models.Index(fields=['product_code']),
            models.Index(fields=['category']),
            models.Index(fields=['is_active']),
            models.Index(fields=['hazard_level']),
            models.Index(fields=['manufacturer']),
            models.Index(fields=['barcode']),
        ]
    
    def __str__(self):
        return f"{self.product_name_ar} ({self.product_code})"
    
    def get_current_stock(self):
        """الحصول على المخزون الحالي"""
        # سيتم تطوير هذه الوظيفة مع موديول المخزون
        return 0
    
    def is_low_stock(self):
        """التحقق من انخفاض المخزون"""
        current_stock = self.get_current_stock()
        return current_stock <= self.minimum_stock_level
    
    def is_out_of_stock(self):
        """التحقق من نفاد المخزون"""
        return self.get_current_stock() <= 0
    
    def needs_reorder(self):
        """التحقق من الحاجة لإعادة الطلب"""
        current_stock = self.get_current_stock()
        return current_stock <= self.reorder_point
    
    def get_safety_info(self):
        """الحصول على معلومات الأمان"""
        return {
            'hazard_level': self.get_hazard_level_display(),
            'storage_conditions': self.get_storage_conditions_display(),
            'shelf_life': self.shelf_life_months,
            'has_sds': bool(self.safety_data_sheet)
        }
    
    def calculate_profit_margin(self):
        """حساب هامش الربح"""
        if self.last_purchase_price and self.selling_price:
            profit = self.selling_price - self.last_purchase_price
            return (profit / self.last_purchase_price) * 100
        return 0


class KeyProductImage(models.Model):
    """
    صور المنتجات
    """
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    product = models.ForeignKey(
        KeyProduct,
        on_delete=models.CASCADE,
        related_name='images',
        verbose_name='المنتج'
    )
    
    image = models.ImageField(
        upload_to='products/images/',
        verbose_name='الصورة'
    )
    
    caption = models.CharField(
        max_length=200,
        verbose_name='وصف الصورة',
        blank=True,
        null=True
    )
    
    is_primary = models.BooleanField(
        default=False,
        verbose_name='صورة رئيسية'
    )
    
    order = models.PositiveIntegerField(
        default=0,
        verbose_name='الترتيب'
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    
    class Meta:
        verbose_name = 'صورة منتج'
        verbose_name_plural = 'صور المنتجات'
        ordering = ['order', 'created_at']
        indexes = [
            models.Index(fields=['product', 'is_primary']),
            models.Index(fields=['order']),
        ]
    
    def __str__(self):
        return f"صورة {self.product.product_name_ar}"
