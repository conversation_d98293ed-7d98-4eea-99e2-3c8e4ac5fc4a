"""
نماذج إدارة المخزون
Inventory Management Forms
"""

from django import forms
from django.core.validators import MinValueValidator
from decimal import Decimal
from .models import Warehouse, BinLocation, ItemMaster

class WarehouseForm(forms.ModelForm):
    """نموذج إضافة/تعديل مخزن"""
    
    class Meta:
        model = Warehouse
        fields = [
            'warehouse_code', 'warehouse_name', 'warehouse_type',
            'total_capacity', 'address', 'city', 'governorate',
            'warehouse_manager', 'manager_phone', 'is_active',
            'temperature_controlled', 'humidity_controlled'
        ]
        
        widgets = {
            'warehouse_code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مثال: ALX-005',
                'maxlength': 20
            }),
            'warehouse_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم المخزن',
                'maxlength': 200
            }),
            'warehouse_type': forms.Select(attrs={
                'class': 'form-control'
            }),
            'total_capacity': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'العنوان التفصيلي',
                'rows': 3
            }),
            'city': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'المدينة',
                'maxlength': 100
            }),
            'governorate': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'المحافظة',
                'maxlength': 100
            }),
            'warehouse_manager': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم مدير المخزن',
                'maxlength': 200
            }),
            'manager_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '01234567890',
                'maxlength': 20
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'temperature_controlled': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
            'humidity_controlled': forms.CheckboxInput(attrs={
                'class': 'form-check-input'
            }),
        }
        
        labels = {
            'warehouse_code': 'كود المخزن',
            'warehouse_name': 'اسم المخزن',
            'warehouse_type': 'نوع المخزن',
            'total_capacity': 'السعة الإجمالية',
            'address': 'العنوان',
            'city': 'المدينة',
            'governorate': 'المحافظة',
            'warehouse_manager': 'مدير المخزن',
            'manager_phone': 'هاتف المدير',
            'is_active': 'نشط',
            'temperature_controlled': 'تحكم في درجة الحرارة',
            'humidity_controlled': 'تحكم في الرطوبة',
        }

    def clean_warehouse_code(self):
        """التحقق من كود المخزن"""
        warehouse_code = self.cleaned_data.get('warehouse_code')
        if warehouse_code:
            warehouse_code = warehouse_code.upper()
            # التحقق من عدم تكرار الكود
            if Warehouse.objects.filter(warehouse_code=warehouse_code).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError('كود المخزن موجود بالفعل')
        return warehouse_code

    def clean_total_capacity(self):
        """التحقق من السعة الإجمالية"""
        capacity = self.cleaned_data.get('total_capacity')
        if capacity and capacity <= 0:
            raise forms.ValidationError('السعة الإجمالية يجب أن تكون أكبر من صفر')
        return capacity


class BinLocationForm(forms.ModelForm):
    """نموذج إضافة/تعديل موقع تخزين"""

    class Meta:
        model = BinLocation
        fields = ['bin_code', 'aisle', 'rack', 'level', 'capacity', 'status']
        
        widgets = {
            'bin_code': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مثال: A-01-01',
                'maxlength': 20
            }),
            'aisle': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مثال: A, B, C',
                'maxlength': 10
            }),
            'rack': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مثال: 01, 02, 03',
                'maxlength': 10
            }),
            'level': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'مثال: 01, 02, 03',
                'maxlength': 10
            }),
            'capacity': forms.NumberInput(attrs={
                'class': 'form-control',
                'placeholder': '0.00',
                'step': '0.01',
                'min': '0'
            }),
            'status': forms.Select(attrs={
                'class': 'form-control'
            }),
        }

        labels = {
            'bin_code': 'كود الموقع',
            'aisle': 'الممر',
            'rack': 'الرف',
            'level': 'المستوى',
            'capacity': 'السعة (م³)',
            'status': 'الحالة',
        }

    def __init__(self, *args, **kwargs):
        self.warehouse = kwargs.pop('warehouse', None)
        super().__init__(*args, **kwargs)

    def clean_bin_code(self):
        """التحقق من كود الموقع"""
        bin_code = self.cleaned_data.get('bin_code')
        if bin_code and self.warehouse:
            bin_code = bin_code.upper()
            # التحقق من عدم تكرار الكود في نفس المخزن
            if BinLocation.objects.filter(
                warehouse=self.warehouse, 
                bin_code=bin_code
            ).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError('كود الموقع موجود بالفعل في هذا المخزن')
        return bin_code


class WarehouseItemForm(forms.Form):
    """نموذج إضافة صنف موجود للمخزن"""
    warehouse_id = forms.UUIDField(widget=forms.HiddenInput())
    
    selected_item = forms.ModelChoiceField(
        queryset=ItemMaster.objects.filter(is_active=True),
        widget=forms.Select(attrs={
            'class': 'form-control',
            'id': 'selected_item'
        }),
        label='اختر الصنف',
        help_text='اختر صنفاً موجوداً في النظام',
        empty_label='اختر الصنف...'
    )
    
    initial_quantity = forms.DecimalField(
        decimal_places=3,
        max_digits=10,
        min_value=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.001',
            'min': '0',
            'placeholder': '0.000'
        }),
        label='الكمية الأولية',
        help_text='الكمية التي سيتم إضافتها للمخزن',
        required=True
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # ترتيب الأصناف حسب الاسم العربي
        self.fields['selected_item'].queryset = ItemMaster.objects.filter(
            is_active=True
        ).order_by('item_name_ar')
    
    def clean_initial_quantity(self):
        """التحقق من الكمية الأولية"""
        quantity = self.cleaned_data.get('initial_quantity')
        if quantity is not None and quantity <= 0:
            raise forms.ValidationError('الكمية يجب أن تكون أكبر من صفر')
        return quantity
