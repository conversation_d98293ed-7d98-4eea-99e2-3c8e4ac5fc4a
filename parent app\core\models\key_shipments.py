"""
KeyShipments - إدارة الشحنات
تتبع جميع الشحنات الواردة والصادرة
"""

from django.db import models
import uuid


class KeyShipment(models.Model):
    """
    نموذج الشحنات الأساسي
    """
    
    SHIPMENT_TYPES = [
        ('INBOUND', 'وارد'),
        ('OUTBOUND', 'صادر'),
        ('INTERNAL', 'داخلي'),
    ]
    
    TRANSPORT_METHODS = [
        ('SEA', 'بحري'),
        ('AIR', 'جوي'),
        ('LAND', 'بري'),
        ('RAIL', 'سكك حديدية'),
        ('COURIER', 'بريد سريع'),
    ]
    
    SHIPMENT_STATUS = [
        ('PLANNED', 'مخطط'),
        ('IN_TRANSIT', 'في الطريق'),
        ('ARRIVED', 'وصل'),
        ('DELIVERED', 'تم التسليم'),
        ('DELAYED', 'متأخر'),
        ('CANCELLED', 'ملغي'),
    ]
    
    # Primary key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Shipment Information
    shipment_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='رقم الشحنة'
    )
    
    shipment_type = models.CharField(
        max_length=20,
        choices=SHIPMENT_TYPES,
        verbose_name='نوع الشحنة'
    )
    
    transport_method = models.CharField(
        max_length=20,
        choices=TRANSPORT_METHODS,
        verbose_name='وسيلة النقل'
    )
    
    # Parties
    sender = models.ForeignKey(
        'core.KeyCompany',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='sent_shipments',
        verbose_name='المرسل'
    )

    receiver = models.ForeignKey(
        'core.KeyCompany',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='received_shipments',
        verbose_name='المستقبل'
    )

    carrier = models.ForeignKey(
        'core.KeyCompany',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='carried_shipments',
        verbose_name='شركة النقل'
    )
    
    # Status and Tracking
    status = models.CharField(
        max_length=20,
        choices=SHIPMENT_STATUS,
        default='PLANNED',
        verbose_name='الحالة'
    )
    
    tracking_number = models.CharField(
        max_length=100,
        verbose_name='رقم التتبع',
        blank=True,
        null=True
    )
    
    # Dates
    planned_dispatch_date = models.DateField(
        verbose_name='تاريخ الإرسال المخطط'
    )
    
    actual_dispatch_date = models.DateField(
        verbose_name='تاريخ الإرسال الفعلي',
        blank=True,
        null=True
    )
    
    expected_arrival_date = models.DateField(
        verbose_name='تاريخ الوصول المتوقع'
    )
    
    actual_arrival_date = models.DateField(
        verbose_name='تاريخ الوصول الفعلي',
        blank=True,
        null=True
    )
    
    # Financial
    shipping_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        verbose_name='تكلفة الشحن'
    )
    
    insurance_cost = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        verbose_name='تكلفة التأمين'
    )
    
    # Additional Information
    notes = models.TextField(
        verbose_name='ملاحظات',
        blank=True,
        null=True
    )
    
    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ آخر تحديث'
    )
    
    created_by = models.ForeignKey(
        'core.KeyUser',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='created_shipments',
        verbose_name='أنشئ بواسطة'
    )
    
    class Meta:
        verbose_name = 'شحنة'
        verbose_name_plural = 'الشحنات'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['shipment_number']),
            models.Index(fields=['shipment_type']),
            models.Index(fields=['status']),
            models.Index(fields=['tracking_number']),
        ]
    
    def __str__(self):
        return f"{self.shipment_number} - {self.get_status_display()}"
