{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة صنف جديد - KamaVerse{% endblock %}

{% block extra_css %}
<link href="{% static 'css/items.css' %}" rel="stylesheet">
<style>
.form-section {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(26, 26, 26, 0.08);
    border: 1px solid var(--line);
}

.form-section h3 {
    color: var(--brand-red);
    margin-bottom: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.form-section h3 i {
    color: var(--brand-gold);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: var(--charcoal);
    margin-bottom: 0.5rem;
    display: block;
}

.form-control, .form-select {
    border: 2px solid var(--line);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--brand-gold);
    box-shadow: 0 0 0 0.2rem rgba(200, 154, 60, 0.25);
}

.required {
    color: var(--brand-red);
}

.btn-save {
    background: var(--brand-red);
    color: var(--white);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-save:hover {
    background: var(--brand-red-dark);
    transform: translateY(-1px);
}

.btn-cancel {
    background: var(--slate);
    color: var(--white);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    margin-left: 1rem;
    transition: all 0.2s ease;
}

.btn-cancel:hover {
    background: var(--charcoal);
}

.page-header {
    background: linear-gradient(135deg, var(--brand-red) 0%, var(--brand-red-dark) 100%);
    color: white !important;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.page-header h1 {
    margin: 0;
    font-weight: 700;
    color: white !important;
}

.page-header h1 i {
    color: white !important;
}

.page-header p {
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    color: white !important;
}

.help-text {
    font-size: 0.875rem;
    color: var(--slate);
    margin-top: 0.25rem;
}

.row {
    margin-left: -0.75rem;
    margin-right: -0.75rem;
}

.col-md-6, .col-md-4, .col-md-12 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <h1>
        <i class="fas fa-plus-circle me-3"></i>
        إضافة صنف جديد
    </h1>
    <p>إضافة صنف جديد إلى نظام إدارة المخزون</p>
</div>

<!-- Add Item Form -->
<form method="post" class="item-form">
    {% csrf_token %}
    
    <!-- Basic Information Section -->
    <div class="form-section">
        <h3>
            <i class="fas fa-info-circle"></i>
            المعلومات الأساسية
        </h3>
        
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="item_code" class="form-label">
                        كود الصنف <span class="required">*</span>
                    </label>
                    <input type="text" class="form-control" id="item_code" name="item_code" required>
                    <div class="help-text">كود فريد للصنف (مثال: CHM001)</div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="form-group">
                    <label for="item_name_ar" class="form-label">
                        اسم الصنف بالعربية <span class="required">*</span>
                    </label>
                    <input type="text" class="form-control" id="item_name_ar" name="item_name_ar" required>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="item_name_en" class="form-label">
                        اسم الصنف بالإنجليزية
                    </label>
                    <input type="text" class="form-control" id="item_name_en" name="item_name_en">
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="form-group">
                    <label for="chemical_formula" class="form-label">
                        الصيغة الكيميائية
                    </label>
                    <input type="text" class="form-control" id="chemical_formula" name="chemical_formula">
                    <div class="help-text">مثال: H2SO4, C2H5OH</div>
                </div>
            </div>
        </div>
        
        <div class="form-group">
            <label for="description" class="form-label">
                الوصف
            </label>
            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
        </div>
    </div>
    
    <!-- Classification Section -->
    <div class="form-section">
        <h3>
            <i class="fas fa-tags"></i>
            التصنيف
        </h3>
        
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="category" class="form-label">
                        مجموعة المواد <span class="required">*</span>
                    </label>
                    <select class="form-select" id="category" name="category" required>
                        <option value="">اختر المجموعة</option>
                        <option value="CHEMICALS">مواد كيميائية</option>
                        <option value="PLASTICS">مواد بلاستيكية</option>
                        <option value="ADDITIVES">إضافات</option>
                        <option value="PIGMENTS">ملونات</option>
                        <option value="SOLVENTS">مذيبات</option>
                    </select>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="form-group">
                    <label for="material_type" class="form-label">
                        نوع المادة <span class="required">*</span>
                    </label>
                    <select class="form-select" id="material_type" name="material_type" required>
                        <option value="">اختر النوع</option>
                        <option value="RAW">مادة خام</option>
                        <option value="INTERMEDIATE">مادة وسطية</option>
                        <option value="FINISHED">منتج نهائي</option>
                        <option value="PACKAGING">مواد تعبئة</option>
                    </select>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="form-group">
                    <label for="hazard_level" class="form-label">
                        مستوى الخطورة <span class="required">*</span>
                    </label>
                    <select class="form-select" id="hazard_level" name="hazard_level" required>
                        <option value="">اختر المستوى</option>
                        <option value="NONE">آمن</option>
                        <option value="LOW">منخفض</option>
                        <option value="MEDIUM">متوسط</option>
                        <option value="HIGH">عالي</option>
                        <option value="CRITICAL">حرج</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Units and Measurements Section -->
    <div class="form-section">
        <h3>
            <i class="fas fa-balance-scale"></i>
            الوحدات والقياسات
        </h3>
        
        <div class="row">
            <div class="col-md-4">
                <div class="form-group">
                    <label for="base_unit" class="form-label">
                        وحدة القياس الأساسية <span class="required">*</span>
                    </label>
                    <select class="form-select" id="base_unit" name="base_unit" required>
                        <option value="">اختر الوحدة</option>
                        <option value="KG">كيلوجرام</option>
                        <option value="TON">طن</option>
                        <option value="LITER">لتر</option>
                        <option value="PIECE">قطعة</option>
                        <option value="BAG">كيس</option>
                        <option value="DRUM">برميل</option>
                    </select>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="form-group">
                    <label for="min_stock_level" class="form-label">
                        الحد الأدنى للمخزون
                    </label>
                    <input type="number" class="form-control" id="min_stock_level" name="min_stock_level" min="0" step="0.01">
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="form-group">
                    <label for="initial_quantity" class="form-label">
                        الكمية المضافة
                    </label>
                    <input type="number" class="form-control" id="initial_quantity" name="initial_quantity" min="0" step="0.01" placeholder="الكمية الأولية للمخزون">
                    <div class="help-text">الكمية التي ستظهر في جدول الأصناف</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Storage Information Section -->
    <div class="form-section">
        <h3>
            <i class="fas fa-warehouse"></i>
            معلومات التخزين
        </h3>
        
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="storage_conditions" class="form-label">
                        شروط التخزين
                    </label>
                    <textarea class="form-control" id="storage_conditions" name="storage_conditions" rows="3" placeholder="مثال: يحفظ في مكان بارد وجاف، بعيداً عن أشعة الشمس المباشرة"></textarea>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="form-group">
                    <label for="shelf_life_months" class="form-label">
                        مدة الصلاحية (بالشهور)
                    </label>
                    <input type="number" class="form-control" id="shelf_life_months" name="shelf_life_months" min="1" max="120">
                    <div class="help-text">عدد الشهور من تاريخ الإنتاج</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Form Actions -->
    <div class="form-actions text-end">
        <a href="{% url 'inventory:items_list' %}" class="btn btn-cancel">
            <i class="fas fa-times me-2"></i>
            إلغاء
        </a>
        <button type="submit" class="btn btn-save">
            <i class="fas fa-save me-2"></i>
            حفظ الصنف
        </button>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-generate item code based on category
    const categorySelect = document.getElementById('category');
    const itemCodeInput = document.getElementById('item_code');
    
    categorySelect.addEventListener('change', function() {
        if (this.value && !itemCodeInput.value) {
            const prefixes = {
                'CHEMICALS': 'CHM',
                'PLASTICS': 'PLS',
                'ADDITIVES': 'ADD',
                'PIGMENTS': 'PIG',
                'SOLVENTS': 'SOL'
            };
            
            const prefix = prefixes[this.value] || 'ITM';
            const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            itemCodeInput.value = prefix + randomNum;
        }
    });
    
    // Form validation
    const form = document.querySelector('.item-form');
    form.addEventListener('submit', function(e) {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.style.borderColor = 'var(--brand-red)';
                isValid = false;
            } else {
                field.style.borderColor = 'var(--line)';
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });
});
</script>
{% endblock %}
