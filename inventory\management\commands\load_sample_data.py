"""
Django management command to load sample data for testing
أمر Django لتحميل بيانات تجريبية للاختبار
"""
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from decimal import Decimal
from datetime import date, timedelta
from inventory.models import ItemMaster, Warehouse, BinLocation, StockMovement, StockBalance, Alert


class Command(BaseCommand):
    help = 'Load sample data for testing the inventory system'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('بدء تحميل البيانات التجريبية...'))
        
        # إنشاء مستخدم تجريبي إذا لم يكن موجود
        user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'is_staff': True,
                'is_superuser': True,
            }
        )
        if created:
            user.set_password('admin123')
            user.save()
            self.stdout.write(self.style.SUCCESS('تم إنشاء مستخدم admin'))
        
        # إنشاء المخازن الأربعة
        warehouses_data = [
            {
                'warehouse_code': 'MNF001',
                'warehouse_name': 'مخزن المنوفية',
                'warehouse_type': 'MAIN',
                'address': 'المنطقة الصناعية - المنوفية',
                'city': 'شبين الكوم',
                'governorate': 'المنوفية',
                'total_capacity': Decimal('5000.00'),
                'current_occupancy': Decimal('3250.00'),
                'warehouse_manager': 'أحمد محمد علي',
                'manager_phone': '01012345678',
                'manager_email': '<EMAIL>',
                'temperature_controlled': True,
                'humidity_controlled': True,
            },
            {
                'warehouse_code': 'CAI001',
                'warehouse_name': 'مخزن القاهرة',
                'warehouse_type': 'MAIN',
                'address': 'المنطقة الصناعية - العاشر من رمضان',
                'city': 'العاشر من رمضان',
                'governorate': 'القاهرة',
                'total_capacity': Decimal('4000.00'),
                'current_occupancy': Decimal('3200.00'),
                'warehouse_manager': 'محمد أحمد حسن',
                'manager_phone': '01023456789',
                'manager_email': '<EMAIL>',
                'temperature_controlled': True,
                'humidity_controlled': False,
            },
            {
                'warehouse_code': 'RNG001',
                'warehouse_name': 'مخزن الدائري',
                'warehouse_type': 'BRANCH',
                'address': 'الطريق الدائري - القاهرة',
                'city': 'القاهرة',
                'governorate': 'القاهرة',
                'total_capacity': Decimal('2500.00'),
                'current_occupancy': Decimal('1125.00'),
                'warehouse_manager': 'علي محمد إبراهيم',
                'manager_phone': '01034567890',
                'manager_email': '<EMAIL>',
                'temperature_controlled': False,
                'humidity_controlled': False,
            },
            {
                'warehouse_code': 'ALX001',
                'warehouse_name': 'مخزن الإسكندرية',
                'warehouse_type': 'MAIN',
                'address': 'المنطقة الصناعية - برج العرب',
                'city': 'الإسكندرية',
                'governorate': 'الإسكندرية',
                'total_capacity': Decimal('3500.00'),
                'current_occupancy': Decimal('1925.00'),
                'warehouse_manager': 'حسن علي محمد',
                'manager_phone': '01045678901',
                'manager_email': '<EMAIL>',
                'temperature_controlled': True,
                'humidity_controlled': True,
            },
        ]
        
        warehouses = []
        for warehouse_data in warehouses_data:
            warehouse, created = Warehouse.objects.get_or_create(
                warehouse_code=warehouse_data['warehouse_code'],
                defaults={**warehouse_data, 'created_by': user}
            )
            warehouses.append(warehouse)
            if created:
                self.stdout.write(f'تم إنشاء مخزن: {warehouse.warehouse_name}')
        
        # إنشاء مواقع التخزين
        bin_locations = []
        for warehouse in warehouses:
            for aisle in ['A', 'B', 'C']:
                for rack in range(1, 11):  # 10 أرفف لكل ممر
                    bin_code = f"{aisle}{rack}"
                    bin_location, created = BinLocation.objects.get_or_create(
                        warehouse=warehouse,
                        bin_code=bin_code,
                        defaults={
                            'bin_name': f'موقع {bin_code}',
                            'aisle': aisle,
                            'rack': str(rack),
                            'capacity': Decimal('100.00'),
                            'current_quantity': Decimal('0.00'),
                            'status': 'AVAILABLE',
                        }
                    )
                    bin_locations.append(bin_location)
                    if created and rack <= 2:  # طباعة أول موقعين فقط
                        self.stdout.write(f'تم إنشاء موقع تخزين: {warehouse.warehouse_name} - {bin_code}')
        
        # إنشاء أصناف تجريبية
        items_data = [
            {
                'item_name_ar': 'بولي إيثيلين عالي الكثافة',
                'item_name_en': 'High Density Polyethylene (HDPE)',
                'category': 'PLASTIC_RAW',
                'material_type': 'RAW_MATERIAL',
                'unit_of_measure': 'KG',
                'minimum_stock_level': Decimal('1000.00'),
                'reorder_point': Decimal('1500.00'),
                'chemical_formula': '(C2H4)n',
                'density': Decimal('0.9400'),
                'hazard_level': 'LOW',
                'shelf_life_months': 24,
            },
            {
                'item_name_ar': 'بولي فينيل كلوريد',
                'item_name_en': 'Polyvinyl Chloride (PVC)',
                'category': 'PLASTIC_RAW',
                'material_type': 'RAW_MATERIAL',
                'unit_of_measure': 'KG',
                'minimum_stock_level': Decimal('800.00'),
                'reorder_point': Decimal('1200.00'),
                'chemical_formula': '(C2H3Cl)n',
                'density': Decimal('1.3800'),
                'hazard_level': 'MEDIUM',
                'shelf_life_months': 36,
            },
            {
                'item_name_ar': 'حمض الكبريتيك',
                'item_name_en': 'Sulfuric Acid',
                'category': 'ACIDS',
                'material_type': 'RAW_MATERIAL',
                'unit_of_measure': 'LITER',
                'minimum_stock_level': Decimal('500.00'),
                'reorder_point': Decimal('750.00'),
                'chemical_formula': 'H2SO4',
                'density': Decimal('1.8400'),
                'hazard_level': 'CRITICAL',
                'purity_percentage': Decimal('98.00'),
                'shelf_life_months': 12,
            },
            {
                'item_name_ar': 'أكسيد التيتانيوم',
                'item_name_en': 'Titanium Dioxide',
                'category': 'OXIDES',
                'material_type': 'RAW_MATERIAL',
                'unit_of_measure': 'KG',
                'minimum_stock_level': Decimal('200.00'),
                'reorder_point': Decimal('300.00'),
                'chemical_formula': 'TiO2',
                'density': Decimal('4.2300'),
                'hazard_level': 'LOW',
                'color': 'أبيض',
                'shelf_life_months': 60,
            },
            {
                'item_name_ar': 'ملون أزرق فثالوسيانين',
                'item_name_en': 'Phthalocyanine Blue',
                'category': 'COLORANTS',
                'material_type': 'AUXILIARY_MATERIAL',
                'unit_of_measure': 'KG',
                'minimum_stock_level': Decimal('50.00'),
                'reorder_point': Decimal('75.00'),
                'color': 'أزرق',
                'hazard_level': 'LOW',
                'shelf_life_months': 48,
            },
        ]
        
        items = []
        for item_data in items_data:
            # تحديد المخزن الافتراضي والموقع
            default_warehouse = warehouses[0]  # مخزن المنوفية
            default_bin = bin_locations[0]  # أول موقع متاح
            
            item, created = ItemMaster.objects.get_or_create(
                item_name_ar=item_data['item_name_ar'],
                defaults={
                    **item_data,
                    'default_warehouse': default_warehouse,
                    'default_bin_location': default_bin,
                    'created_by': user
                }
            )
            items.append(item)
            if created:
                self.stdout.write(f'تم إنشاء صنف: {item.item_name_ar} ({item.item_code})')
        
        self.stdout.write(self.style.SUCCESS('تم تحميل البيانات التجريبية بنجاح! ✅'))
        self.stdout.write(self.style.SUCCESS(f'تم إنشاء:'))
        self.stdout.write(f'- {len(warehouses)} مخازن')
        self.stdout.write(f'- {len(bin_locations)} موقع تخزين')
        self.stdout.write(f'- {len(items)} أصناف')
        self.stdout.write(self.style.SUCCESS('يمكنك الآن الدخول إلى Django Admin لمراجعة البيانات'))
