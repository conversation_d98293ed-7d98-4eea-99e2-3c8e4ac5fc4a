{% extends "base.html" %}
{% load static %}

{% block title %}التقارير الأساسية - KamaVerse{% endblock %}

{% block extra_css %}
<style>
    :root {
        --brand-red: #D62828;
        --brand-red-dark: #8B1116;
        --brand-red-light: #FCE8E8;
        --brand-gold: #C89A3C;
        --brand-gold-light: #F4D488;
        --brand-gold-dark: #8C6420;
        --ink: #1A1A1A;
        --slate: #4A4F57;
        --line: #E6E8ED;
        --canvas: #F7F8FB;
        --white: #FFFFFF;
        --success: #2E7D32;
        --warning: #F39C12;
        --error: #C21807;
    }

    .page-container {
        background: linear-gradient(180deg, #FFFFFF 0%, #F7F8FB 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .page-header {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    .page-header h1 {
        color: var(--ink);
        font-weight: 700;
        font-size: 2.5rem;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .page-header .icon {
        color: var(--brand-gold);
        font-size: 3rem;
    }

    .page-header p {
        color: var(--slate);
        font-size: 1.1rem;
        margin: 1rem 0 0 0;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(26, 26, 26, 0.12);
        border-color: var(--brand-gold);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, var(--brand-gold) 0%, var(--brand-gold-dark) 100%);
    }

    .stat-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        gap: 1rem;
    }

    .stat-icon {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: var(--brand-gold);
        background: var(--brand-gold-light);
        margin-bottom: 0.5rem;
    }

    .stat-info h3 {
        color: var(--ink);
        font-weight: 600;
        font-size: 1.1rem;
        margin: 0 0 0.5rem 0;
        text-align: center;
    }

    .stat-info .value {
        color: var(--brand-red);
        font-weight: 700;
        font-size: 2.5rem;
        margin: 0;
        text-align: center;
    }

    .reports-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .report-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
        transition: all 0.3s ease;
        text-decoration: none;
        color: inherit;
        display: flex;
        flex-direction: column;
        height: 100%;
        justify-content: space-between;
    }

    .report-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(26, 26, 26, 0.12);
        border-color: var(--brand-gold);
        text-decoration: none;
        color: inherit;
    }

    .report-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1.2rem;
    }

    .report-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: var(--brand-gold);
        background: var(--brand-gold-light);
        box-shadow: 0 4px 10px rgba(200, 154, 60, 0.2);
    }

    .report-title {
        color: var(--ink);
        font-weight: 600;
        font-size: 1.3rem;
        margin: 0;
        line-height: 1.4;
    }

    .report-content {
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    
    .report-description {
        color: var(--slate);
        font-size: 1rem;
        margin: 0;
        line-height: 1.6;
        flex: 1;
    }

    .report-button {
        background: var(--brand-red);
        color: var(--white);
        border: none;
        padding: 0.85rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        width: 100%;
        justify-content: center;
        box-shadow: 0 4px 15px rgba(214, 40, 40, 0.2);
        margin-top: 1.5rem;
    }

    .report-button:hover {
        background: var(--brand-red-dark);
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(214, 40, 40, 0.3);
    }

    .breadcrumb {
        background: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }

    .breadcrumb-item {
        color: var(--slate);
    }

    .breadcrumb-item.active {
        color: var(--brand-red);
        font-weight: 600;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: var(--brand-gold);
        margin: 0 0.5rem;
    }

    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2rem;
        }
        
        .page-header .icon {
            font-size: 2.5rem;
        }

        .stats-grid {
            grid-template-columns: 2fr 2fr;
        }

        .reports-grid {
            grid-template-columns: 1fr;
        }
        
        .report-title {
            font-size: 1.1rem;
        }
    }
    
    @media (max-width: 480px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="container-fluid">
        <!-- مسار التنقل -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:dashboard' %}">الصفحة الرئيسية</a>
                </li>
                <li class="breadcrumb-item active">التقارير الأساسية</li>
            </ol>
        </nav>

        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1>
                <i class="fas fa-chart-bar icon"></i>
                التقارير الأساسية
            </h1>
            <p>عرض تقارير شاملة عن الأرصدة وحركات المخزون في جميع المخازن</p>
        </div>

        <!-- الإحصائيات العامة -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-content">
                    <div class="stat-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="stat-info">
                        <h3>إجمالي الأصناف</h3>
                        <p class="value">{{ total_items|floatformat:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-content">
                    <div class="stat-icon">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <div class="stat-info">
                        <h3>عدد المخازن</h3>
                        <p class="value">{{ total_warehouses|floatformat:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-content">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="stat-info">
                        <h3>حركات اليوم</h3>
                        <p class="value">{{ total_movements_today|floatformat:0 }}</p>
                    </div>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-content">
                    <div class="stat-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <div class="stat-info">
                        <h3>حركات الشهر</h3>
                        <p class="value">{{ total_movements_month|floatformat:0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقات التقارير المحسنة -->
        <div class="reports-grid">
            <!-- تقرير الأصناف بالكميات -->
            <a href="{% url 'inventory:enhanced_items_quantity_report' %}" class="report-card">
                <div class="report-content">
                    <div class="report-header">
                        <div class="report-icon">
                            <i class="fas fa-cubes"></i>
                        </div>
                        <h2 class="report-title">تقرير الاصناف بالكميات</h2>
                    </div>
                    <p class="report-description">
                        عرض تقارير الأصناف بكمياتها مع تحديث يومي، يمكن طباعتها ويتم تحديثها تلقائياً عند إضافة أصناف جديدة
                    </p>
                </div>
                <div class="report-button">
                    <i class="fas fa-eye"></i>
                    عرض التقرير
                </div>
            </a>

            <!-- حركات المخزون -->
            <a href="{% url 'inventory:consolidated_movements_report' %}" class="report-card">
                <div class="report-content">
                    <div class="report-header">
                        <div class="report-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <h2 class="report-title">حركات المخزون (استلام-صرف-نقل)</h2>
                    </div>
                    <p class="report-description">
                        تقرير شامل لحركات المخزون (استلام-صرف-نقل) للمخازن مجمعة أو كل مخزن على حدة
                    </p>
                </div>
                <div class="report-button">
                    <i class="fas fa-eye"></i>
                    عرض التقرير
                </div>
            </a>

            <!-- تقرير مسؤولين المخازن -->
            <a href="{% url 'inventory:warehouse_manager_report' %}" class="report-card">
                <div class="report-content">
                    <div class="report-header">
                        <div class="report-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <h2 class="report-title">تقرير مسؤولين المخازن بحركاته وكل تفاصيله</h2>
                    </div>
                    <p class="report-description">
                        تقرير خاص لكل مخزن ومديره مع عرض جميع حركاته وإحصائياته التفصيلية
                    </p>
                </div>
                <div class="report-button">
                    <i class="fas fa-eye"></i>
                    عرض التقرير
                </div>
            </a>

            <!-- تقرير حسب ارصده المخازن -->
            <a href="{% url 'inventory:warehouse_stock_report' %}" class="report-card">
                <div class="report-content">
                    <div class="report-header">
                        <div class="report-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <h2 class="report-title">تقرير أرصدة المخازن</h2>
                    </div>
                    <p class="report-description">
                        عرض ملخص الأرصدة والقيم لكل مخزن مع معلومات تفصيلية عن حالة المخزون
                    </p>
                </div>
                <div class="report-button">
                    <i class="fas fa-eye"></i>
                    عرض التقرير
                </div>
            </a>
        </div>
    </div>
</div>
{% endblock %}