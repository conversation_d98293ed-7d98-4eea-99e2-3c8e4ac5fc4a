"""
إعدادات التطوير لمشروع KamaVerse
"""

from .base import *

# Debug settings
DEBUG = True
TEMPLATE_DEBUG = True

# Allowed hosts for development
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0']

# Development apps
INSTALLED_APPS += [
    # 'django_extensions',  # Will be added later
    # 'debug_toolbar',      # Will be added later
]

# Development middleware
MIDDLEWARE += [
    # 'debug_toolbar.middleware.DebugToolbarMiddleware',  # Will be added later
]

# Database for development (SQLite)
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
        'OPTIONS': {
            'timeout': 20,
        }
    }
}

# Debug toolbar settings
INTERNAL_IPS = [
    '127.0.0.1',
    'localhost',
]

DEBUG_TOOLBAR_CONFIG = {
    'SHOW_TOOLBAR_CALLBACK': lambda request: DEBUG,
    'HIDE_DJANGO_SQL': False,
    'SHOW_TEMPLATE_CONTEXT': True,
}

# Email backend for development
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Static files for development
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

# Media files for development
MEDIA_ROOT = BASE_DIR / 'media'

# Cache for development
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# Logging for development
LOGGING['handlers']['console']['level'] = 'DEBUG'
LOGGING['loggers']['kamaverse']['level'] = 'DEBUG'

# CORS settings for development
CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_CREDENTIALS = True

# Security settings for development (relaxed)
SECURE_SSL_REDIRECT = False
SECURE_BROWSER_XSS_FILTER = False
SECURE_CONTENT_TYPE_NOSNIFF = False

# Session settings for development
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# Development specific settings
KAMAVERSE_SETTINGS.update({
    'ENVIRONMENT': 'development',
    'DEBUG_MODE': True,
    'ENABLE_SILK_PROFILING': True,
    'MOCK_EXTERNAL_APIS': True,
})

# Channels settings for development
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer'
    }
}

# Development database logging
LOGGING['loggers']['django.db.backends'] = {
    'level': 'DEBUG',
    'handlers': ['console'],
    'propagate': False,
}

print("🚀 KamaVerse Development Environment Loaded")
print(f"📊 Database: {DATABASES['default']['ENGINE']}")
print(f"🌐 Debug Mode: {DEBUG}")
print(f"📁 Media Root: {MEDIA_ROOT}")
print(f"📁 Static Root: {STATIC_ROOT}")
