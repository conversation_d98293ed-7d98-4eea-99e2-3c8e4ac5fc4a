# PowerShell script to kill Django application processes
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Killing Django Application Processes" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host ""
Write-Host "Stopping Python processes..." -ForegroundColor Yellow

# Kill all python.exe processes
try {
    Get-Process python -ErrorAction SilentlyContinue | Stop-Process -Force
    Write-Host "✓ Python processes killed" -ForegroundColor Green
} catch {
    Write-Host "✓ No Python processes found" -ForegroundColor Green
}

# Kill all pythonw.exe processes
try {
    Get-Process pythonw -ErrorAction SilentlyContinue | Stop-Process -Force
    Write-Host "✓ Pythonw processes killed" -ForegroundColor Green
} catch {
    Write-Host "✓ No Pythonw processes found" -ForegroundColor Green
}

Write-Host ""
Write-Host "Checking for Django-specific processes..." -ForegroundColor Yellow

# Kill processes using Django ports
$ports = @(8000, 8080, 3000, 5000)
foreach ($port in $ports) {
    try {
        $connections = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue
        foreach ($conn in $connections) {
            $process = Get-Process -Id $conn.OwningProcess -ErrorAction SilentlyContinue
            if ($process) {
                Stop-Process -Id $process.Id -Force
                Write-Host "✓ Killed process $($process.Name) (PID: $($process.Id)) using port $port" -ForegroundColor Green
            }
        }
    } catch {
        # Port not in use
    }
}

Write-Host ""
Write-Host "Checking for processes in current directory..." -ForegroundColor Yellow

# Get current directory path
$currentPath = Get-Location

# Kill any processes running from this directory
try {
    $processes = Get-WmiObject Win32_Process | Where-Object { 
        $_.CommandLine -and $_.CommandLine.Contains($currentPath) 
    }
    
    foreach ($proc in $processes) {
        if ($proc.Name -eq "python.exe" -or $proc.Name -eq "pythonw.exe") {
            Stop-Process -Id $proc.ProcessId -Force
            Write-Host "✓ Killed $($proc.Name) (PID: $($proc.ProcessId)) from current directory" -ForegroundColor Green
        }
    }
} catch {
    Write-Host "✓ No processes found in current directory" -ForegroundColor Green
}

Write-Host ""
Write-Host "Final verification..." -ForegroundColor Yellow

# Check if any Python processes are still running
$remainingProcesses = Get-Process python*, pythonw* -ErrorAction SilentlyContinue
if ($remainingProcesses) {
    Write-Host "⚠ Warning: Some Python processes are still running:" -ForegroundColor Red
    $remainingProcesses | Format-Table Name, Id, StartTime -AutoSize
    Write-Host "These might be other Python applications not related to Django" -ForegroundColor Yellow
} else {
    Write-Host "✓ All Python processes cleared!" -ForegroundColor Green
}

# Check ports
Write-Host ""
Write-Host "Checking if common Django ports are free..." -ForegroundColor Yellow
foreach ($port in $ports) {
    try {
        $listener = Get-NetTCPConnection -LocalPort $port -State Listen -ErrorAction SilentlyContinue
        if ($listener) {
            Write-Host "⚠ Port $port is still in use" -ForegroundColor Red
        } else {
            Write-Host "✓ Port $port is free" -ForegroundColor Green
        }
    } catch {
        Write-Host "✓ Port $port is free" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Process cleanup completed!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Read-Host "Press Enter to exit"