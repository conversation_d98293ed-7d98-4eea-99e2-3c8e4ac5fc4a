# Generated by Django 5.2.4 on 2025-08-16 16:12

import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='KeyUser',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('arabic_name', models.CharField(help_text='الاسم الكامل للمستخدم بالعربية', max_length=100, verbose_name='الاسم بالعربية')),
                ('employee_id', models.CharField(help_text='رقم الموظف الفريد في الشركة', max_length=20, unique=True, verbose_name='رقم الموظف')),
                ('national_id', models.CharField(blank=True, max_length=14, null=True, unique=True, validators=[django.core.validators.RegexValidator('^\\d{14}$', 'رقم الهوية يجب أن يكون 14 رقم')], verbose_name='رقم الهوية القومية')),
                ('phone_number', models.CharField(blank=True, max_length=15, null=True, validators=[django.core.validators.RegexValidator('^\\+?1?\\d{9,15}$', 'رقم الهاتف غير صحيح')], verbose_name='رقم الهاتف')),
                ('user_level', models.CharField(choices=[('SUPER_ADMIN', 'مدير أعلى'), ('ADMIN', 'مدير'), ('MANAGER', 'رئيس قسم'), ('EMPLOYEE', 'موظف'), ('VIEWER', 'مطلع')], default='EMPLOYEE', max_length=20, verbose_name='مستوى المستخدم')),
                ('department', models.CharField(choices=[('IMPORT', 'قسم الاستيراد'), ('STOCK', 'قسم المخزون'), ('FINANCE', 'قسم المالية'), ('SALES', 'قسم المبيعات'), ('CRM', 'قسم علاقات العملاء'), ('HR', 'قسم الموارد البشرية'), ('LOGISTICS', 'قسم اللوجستيات'), ('REPORTING', 'قسم التقارير'), ('IT', 'قسم تقنية المعلومات'), ('MANAGEMENT', 'الإدارة العليا')], max_length=20, verbose_name='القسم')),
                ('job_title', models.CharField(blank=True, max_length=100, null=True, verbose_name='المسمى الوظيفي')),
                ('hire_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التوظيف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='آخر IP للدخول')),
                ('failed_login_attempts', models.PositiveIntegerField(default=0, verbose_name='محاولات الدخول الفاشلة')),
                ('password_changed_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ آخر تغيير كلمة المرور')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_users', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('direct_manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='subordinates', to=settings.AUTH_USER_MODEL, verbose_name='المدير المباشر')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'مستخدم',
                'verbose_name_plural': 'المستخدمون',
                'ordering': ['arabic_name'],
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='KeyApproval',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('approval_type', models.CharField(choices=[('PURCHASE', 'موافقة شراء'), ('SALE', 'موافقة بيع'), ('PAYMENT', 'موافقة دفع'), ('EXPENSE', 'موافقة مصروف'), ('LEAVE', 'موافقة إجازة'), ('OVERTIME', 'موافقة إضافي'), ('DISCOUNT', 'موافقة خصم'), ('CREDIT', 'موافقة ائتمان'), ('OTHER', 'أخرى')], max_length=20, verbose_name='نوع الموافقة')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='المبلغ')),
                ('priority', models.CharField(choices=[('LOW', 'منخفض'), ('NORMAL', 'عادي'), ('HIGH', 'عالي'), ('URGENT', 'عاجل')], default='NORMAL', max_length=10, verbose_name='الأولوية')),
                ('status', models.CharField(choices=[('PENDING', 'معلق'), ('APPROVED', 'معتمد'), ('REJECTED', 'مرفوض'), ('CANCELLED', 'ملغي')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('requested_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الموافقة')),
                ('due_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاستحقاق')),
                ('response_notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات الرد')),
                ('approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_approvals', to=settings.AUTH_USER_MODEL, verbose_name='المعتمد')),
                ('requester', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requested_approvals', to=settings.AUTH_USER_MODEL, verbose_name='الطالب')),
            ],
            options={
                'verbose_name': 'موافقة',
                'verbose_name_plural': 'الموافقات',
                'ordering': ['-requested_at'],
            },
        ),
        migrations.CreateModel(
            name='KeyAuditLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('operation_type', models.CharField(choices=[('CREATE', 'إنشاء'), ('UPDATE', 'تحديث'), ('DELETE', 'حذف'), ('LOGIN', 'تسجيل دخول'), ('LOGOUT', 'تسجيل خروج'), ('APPROVE', 'موافقة'), ('REJECT', 'رفض'), ('EXPORT', 'تصدير'), ('IMPORT', 'استيراد'), ('VIEW', 'عرض'), ('PRINT', 'طباعة'), ('OTHER', 'أخرى')], max_length=20, verbose_name='نوع العملية')),
                ('module', models.CharField(choices=[('CORE', 'النواة الأساسية'), ('IMPORT_MODULE', 'موديول الاستيراد'), ('STOCK_MODULE', 'موديول المخزون'), ('FINANCE_MODULE', 'موديول المالية'), ('SALES_MODULE', 'موديول المبيعات'), ('CRM_MODULE', 'موديول علاقات العملاء'), ('HR_MODULE', 'موديول الموارد البشرية'), ('LOGISTICS_MODULE', 'موديول اللوجستيات'), ('REPORTING_MODULE', 'موديول التقارير'), ('USERS_MODULE', 'موديول إدارة المستخدمين'), ('KAMACHAT', 'تطبيق المحادثة'), ('HAWK', 'تطبيق الإدارة العليا'), ('SYSTEM', 'النظام')], max_length=30, verbose_name='الموديول')),
                ('table_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم الجدول')),
                ('record_id', models.CharField(blank=True, max_length=100, null=True, verbose_name='معرف السجل')),
                ('ip_address', models.GenericIPAddressField(verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='معلومات المتصفح')),
                ('operation_description', models.TextField(verbose_name='وصف العملية')),
                ('old_data', models.JSONField(blank=True, default=dict, null=True, verbose_name='البيانات القديمة')),
                ('new_data', models.JSONField(blank=True, default=dict, null=True, verbose_name='البيانات الجديدة')),
                ('success', models.BooleanField(default=True, verbose_name='نجحت العملية')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='رسالة الخطأ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ العملية')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='audit_logs', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل عملية',
                'verbose_name_plural': 'سجل العمليات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='KeyCompany',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('company_code', models.CharField(help_text='كود فريد للشركة في النظام', max_length=20, unique=True, verbose_name='كود الشركة')),
                ('company_name_ar', models.CharField(max_length=200, verbose_name='اسم الشركة بالعربية')),
                ('company_name_en', models.CharField(blank=True, max_length=200, null=True, verbose_name='اسم الشركة بالإنجليزية')),
                ('company_type', models.CharField(choices=[('SUPPLIER', 'مورد'), ('CUSTOMER', 'عميل'), ('BOTH', 'مورد وعميل'), ('PARTNER', 'شريك'), ('LOGISTICS', 'شركة شحن'), ('SERVICE', 'مقدم خدمة')], max_length=20, verbose_name='نوع الشركة')),
                ('company_size', models.CharField(choices=[('SMALL', 'صغيرة'), ('MEDIUM', 'متوسطة'), ('LARGE', 'كبيرة'), ('ENTERPRISE', 'مؤسسة')], default='MEDIUM', max_length=20, verbose_name='حجم الشركة')),
                ('primary_email', models.EmailField(max_length=254, validators=[django.core.validators.EmailValidator()], verbose_name='البريد الإلكتروني الرئيسي')),
                ('secondary_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني الثانوي')),
                ('primary_phone', models.CharField(max_length=20, validators=[django.core.validators.RegexValidator('^\\+?1?\\d{9,15}$', 'رقم الهاتف غير صحيح')], verbose_name='الهاتف الرئيسي')),
                ('secondary_phone', models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator('^\\+?1?\\d{9,15}$', 'رقم الهاتف غير صحيح')], verbose_name='الهاتف الثانوي')),
                ('fax', models.CharField(blank=True, max_length=20, null=True, verbose_name='الفاكس')),
                ('website', models.URLField(blank=True, null=True, verbose_name='الموقع الإلكتروني')),
                ('country', models.CharField(choices=[('EG', 'مصر'), ('SA', 'السعودية'), ('AE', 'الإمارات'), ('CN', 'الصين'), ('IN', 'الهند'), ('TR', 'تركيا'), ('DE', 'ألمانيا'), ('US', 'الولايات المتحدة'), ('OTHER', 'أخرى')], default='EG', max_length=10, verbose_name='الدولة')),
                ('city', models.CharField(max_length=100, verbose_name='المدينة')),
                ('address_line_1', models.CharField(max_length=200, verbose_name='العنوان الأول')),
                ('address_line_2', models.CharField(blank=True, max_length=200, null=True, verbose_name='العنوان الثاني')),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True, verbose_name='الرمز البريدي')),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='الرقم الضريبي')),
                ('commercial_register', models.CharField(blank=True, max_length=50, null=True, verbose_name='السجل التجاري')),
                ('industry', models.CharField(blank=True, max_length=100, null=True, verbose_name='الصناعة/المجال')),
                ('established_year', models.PositiveIntegerField(blank=True, null=True, verbose_name='سنة التأسيس')),
                ('payment_terms', models.CharField(choices=[('CASH', 'نقدي'), ('NET_30', '30 يوم'), ('NET_60', '60 يوم'), ('NET_90', '90 يوم'), ('ADVANCE', 'دفع مقدم'), ('COD', 'الدفع عند الاستلام'), ('LC', 'اعتماد مستندي'), ('CUSTOM', 'شروط خاصة')], default='NET_30', max_length=20, verbose_name='شروط الدفع')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الحد الائتماني')),
                ('currency', models.CharField(default='EGP', max_length=3, verbose_name='العملة الأساسية')),
                ('rating', models.PositiveIntegerField(default=5, help_text='تقييم الشركة من 1 إلى 10', verbose_name='التقييم (1-10)')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشطة')),
                ('is_approved', models.BooleanField(default=False, verbose_name='معتمدة')),
                ('is_blacklisted', models.BooleanField(default=False, verbose_name='في القائمة السوداء')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('internal_notes', models.TextField(blank=True, help_text='ملاحظات للاستخدام الداخلي فقط', null=True, verbose_name='ملاحظات داخلية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_companies', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_companies', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'شركة',
                'verbose_name_plural': 'الشركات',
                'ordering': ['company_name_ar'],
            },
        ),
        migrations.CreateModel(
            name='KeyCompanyContact',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('contact_type', models.CharField(choices=[('PRIMARY', 'جهة اتصال رئيسية'), ('FINANCIAL', 'جهة اتصال مالية'), ('TECHNICAL', 'جهة اتصال فنية'), ('SALES', 'جهة اتصال مبيعات'), ('LOGISTICS', 'جهة اتصال شحن'), ('MANAGEMENT', 'إدارة عليا')], max_length=20, verbose_name='نوع جهة الاتصال')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('position', models.CharField(blank=True, max_length=100, null=True, verbose_name='المنصب')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator('^\\+?1?\\d{9,15}$', 'رقم الهاتف غير صحيح')], verbose_name='الهاتف')),
                ('mobile', models.CharField(blank=True, max_length=20, null=True, validators=[django.core.validators.RegexValidator('^\\+?1?\\d{9,15}$', 'رقم الهاتف غير صحيح')], verbose_name='الهاتف المحمول')),
                ('is_primary', models.BooleanField(default=False, verbose_name='جهة اتصال رئيسية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contacts', to='core.keycompany', verbose_name='الشركة')),
            ],
            options={
                'verbose_name': 'جهة اتصال',
                'verbose_name_plural': 'جهات الاتصال',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='KeyNotification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('notification_type', models.CharField(choices=[('INFO', 'معلومات'), ('WARNING', 'تحذير'), ('ERROR', 'خطأ'), ('SUCCESS', 'نجاح'), ('REMINDER', 'تذكير'), ('APPROVAL', 'موافقة'), ('SYSTEM', 'نظام')], max_length=20, verbose_name='نوع الإشعار')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('priority', models.CharField(choices=[('LOW', 'منخفض'), ('NORMAL', 'عادي'), ('HIGH', 'عالي'), ('URGENT', 'عاجل')], default='NORMAL', max_length=10, verbose_name='الأولوية')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('read_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ القراءة')),
                ('action_url', models.URLField(blank=True, null=True, verbose_name='رابط الإجراء')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL, verbose_name='المستقبل')),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_notifications', to=settings.AUTH_USER_MODEL, verbose_name='المرسل')),
            ],
            options={
                'verbose_name': 'إشعار',
                'verbose_name_plural': 'الإشعارات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='KeyProduct',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('product_code', models.CharField(help_text='كود فريد للمنتج في النظام', max_length=30, unique=True, verbose_name='كود المنتج')),
                ('barcode', models.CharField(blank=True, max_length=50, null=True, unique=True, verbose_name='الباركود')),
                ('sku', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم المنتج (SKU)')),
                ('product_name_ar', models.CharField(max_length=200, verbose_name='اسم المنتج بالعربية')),
                ('product_name_en', models.CharField(blank=True, max_length=200, null=True, verbose_name='اسم المنتج بالإنجليزية')),
                ('commercial_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='الاسم التجاري')),
                ('scientific_name', models.CharField(blank=True, max_length=200, null=True, verbose_name='الاسم العلمي')),
                ('category', models.CharField(choices=[('PLASTIC_RAW', 'خامات بلاستيك'), ('RUBBER_RAW', 'خامات مطاط'), ('CHEMICALS', 'مواد كيماوية'), ('ADDITIVES', 'إضافات'), ('COLORANTS', 'ملونات'), ('STABILIZERS', 'مثبتات'), ('FILLERS', 'حشوات'), ('RESINS', 'راتنجات'), ('OILS', 'زيوت'), ('ACIDS', 'أحماض'), ('OXIDES', 'أكاسيد'), ('FINISHED', 'منتجات نهائية')], max_length=20, verbose_name='فئة المنتج')),
                ('subcategory', models.CharField(blank=True, max_length=100, null=True, verbose_name='الفئة الفرعية')),
                ('brand', models.CharField(blank=True, max_length=100, null=True, verbose_name='العلامة التجارية')),
                ('unit_of_measure', models.CharField(choices=[('KG', 'كيلوجرام'), ('TON', 'طن'), ('LITER', 'لتر'), ('GALLON', 'جالون'), ('BAG', 'شيكارة'), ('DRUM', 'برميل'), ('CARTON', 'كرتونة'), ('PIECE', 'قطعة'), ('METER', 'متر'), ('ROLL', 'لفة')], default='KG', max_length=20, verbose_name='وحدة القياس')),
                ('weight_per_unit', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='الوزن لكل وحدة')),
                ('density', models.DecimalField(blank=True, decimal_places=4, max_digits=8, null=True, verbose_name='الكثافة (جم/سم³)')),
                ('color', models.CharField(blank=True, max_length=50, null=True, verbose_name='اللون')),
                ('chemical_formula', models.CharField(blank=True, max_length=100, null=True, verbose_name='الصيغة الكيميائية')),
                ('cas_number', models.CharField(blank=True, help_text='Chemical Abstracts Service Number', max_length=20, null=True, verbose_name='رقم CAS')),
                ('purity_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='نسبة النقاء (%)')),
                ('hazard_level', models.CharField(choices=[('NONE', 'غير خطر'), ('LOW', 'خطر منخفض'), ('MEDIUM', 'خطر متوسط'), ('HIGH', 'خطر عالي'), ('EXTREME', 'خطر شديد')], default='NONE', max_length=20, verbose_name='مستوى الخطورة')),
                ('safety_data_sheet', models.FileField(blank=True, null=True, upload_to='products/sds/', verbose_name='ورقة بيانات الأمان')),
                ('hazard_symbols', models.CharField(blank=True, max_length=200, null=True, verbose_name='رموز الخطر')),
                ('storage_conditions', models.CharField(choices=[('NORMAL', 'عادية'), ('COOL', 'باردة'), ('DRY', 'جافة'), ('COOL_DRY', 'باردة وجافة'), ('REFRIGERATED', 'مبردة'), ('FROZEN', 'مجمدة'), ('CONTROLLED', 'محكومة')], default='NORMAL', max_length=20, verbose_name='شروط التخزين')),
                ('storage_temperature_min', models.IntegerField(blank=True, null=True, verbose_name='أقل درجة حرارة للتخزين (°C)')),
                ('storage_temperature_max', models.IntegerField(blank=True, null=True, verbose_name='أعلى درجة حرارة للتخزين (°C)')),
                ('shelf_life_months', models.PositiveIntegerField(blank=True, null=True, verbose_name='مدة الصلاحية (شهر)')),
                ('packaging_type', models.CharField(blank=True, max_length=100, null=True, verbose_name='نوع التعبئة')),
                ('package_size', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='حجم العبوة')),
                ('packages_per_pallet', models.PositiveIntegerField(blank=True, null=True, verbose_name='عدد العبوات في البالتة')),
                ('standard_cost', models.DecimalField(decimal_places=4, default=0, max_digits=15, verbose_name='التكلفة المعيارية')),
                ('last_purchase_price', models.DecimalField(decimal_places=4, default=0, max_digits=15, verbose_name='آخر سعر شراء')),
                ('selling_price', models.DecimalField(decimal_places=4, default=0, max_digits=15, verbose_name='سعر البيع')),
                ('currency', models.CharField(default='EGP', max_length=3, verbose_name='العملة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_discontinued', models.BooleanField(default=False, verbose_name='متوقف')),
                ('requires_approval', models.BooleanField(default=False, verbose_name='يتطلب موافقة')),
                ('minimum_stock_level', models.DecimalField(decimal_places=3, default=0, max_digits=12, verbose_name='الحد الأدنى للمخزون')),
                ('maximum_stock_level', models.DecimalField(decimal_places=3, default=0, max_digits=12, verbose_name='الحد الأقصى للمخزون')),
                ('reorder_point', models.DecimalField(decimal_places=3, default=0, max_digits=12, verbose_name='نقطة إعادة الطلب')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('technical_specifications', models.TextField(blank=True, null=True, verbose_name='المواصفات الفنية')),
                ('applications', models.TextField(blank=True, null=True, verbose_name='الاستخدامات')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_products', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('manufacturer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='manufactured_products', to='core.keycompany', verbose_name='الشركة المصنعة')),
            ],
            options={
                'verbose_name': 'منتج',
                'verbose_name_plural': 'المنتجات',
                'ordering': ['product_name_ar'],
            },
        ),
        migrations.CreateModel(
            name='KeyProductImage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('image', models.ImageField(upload_to='products/images/', verbose_name='الصورة')),
                ('caption', models.CharField(blank=True, max_length=200, null=True, verbose_name='وصف الصورة')),
                ('is_primary', models.BooleanField(default=False, verbose_name='صورة رئيسية')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='core.keyproduct', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'صورة منتج',
                'verbose_name_plural': 'صور المنتجات',
                'ordering': ['order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='KeyShipment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('shipment_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الشحنة')),
                ('shipment_type', models.CharField(choices=[('INBOUND', 'وارد'), ('OUTBOUND', 'صادر'), ('INTERNAL', 'داخلي')], max_length=20, verbose_name='نوع الشحنة')),
                ('transport_method', models.CharField(choices=[('SEA', 'بحري'), ('AIR', 'جوي'), ('LAND', 'بري'), ('RAIL', 'سكك حديدية'), ('COURIER', 'بريد سريع')], max_length=20, verbose_name='وسيلة النقل')),
                ('status', models.CharField(choices=[('PLANNED', 'مخطط'), ('IN_TRANSIT', 'في الطريق'), ('ARRIVED', 'وصل'), ('DELIVERED', 'تم التسليم'), ('DELAYED', 'متأخر'), ('CANCELLED', 'ملغي')], default='PLANNED', max_length=20, verbose_name='الحالة')),
                ('tracking_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='رقم التتبع')),
                ('planned_dispatch_date', models.DateField(verbose_name='تاريخ الإرسال المخطط')),
                ('actual_dispatch_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإرسال الفعلي')),
                ('expected_arrival_date', models.DateField(verbose_name='تاريخ الوصول المتوقع')),
                ('actual_arrival_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الوصول الفعلي')),
                ('shipping_cost', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='تكلفة الشحن')),
                ('insurance_cost', models.DecimalField(decimal_places=2, default=0, max_digits=12, verbose_name='تكلفة التأمين')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('carrier', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='carried_shipments', to='core.keycompany', verbose_name='شركة النقل')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_shipments', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('receiver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='received_shipments', to='core.keycompany', verbose_name='المستقبل')),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_shipments', to='core.keycompany', verbose_name='المرسل')),
            ],
            options={
                'verbose_name': 'شحنة',
                'verbose_name_plural': 'الشحنات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='KeyTransaction',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('transaction_number', models.CharField(max_length=50, unique=True, verbose_name='رقم المعاملة')),
                ('reference_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='الرقم المرجعي')),
                ('transaction_type', models.CharField(choices=[('PURCHASE', 'شراء'), ('SALE', 'بيع'), ('PAYMENT', 'دفع'), ('RECEIPT', 'استلام'), ('EXPENSE', 'مصروف'), ('INCOME', 'إيراد'), ('TRANSFER', 'تحويل'), ('ADJUSTMENT', 'تسوية')], max_length=20, verbose_name='نوع المعاملة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='المبلغ')),
                ('currency', models.CharField(choices=[('EGP', 'جنيه مصري'), ('USD', 'دولار أمريكي'), ('EUR', 'يورو'), ('SAR', 'ريال سعودي'), ('AED', 'درهم إماراتي')], default='EGP', max_length=3, verbose_name='العملة')),
                ('exchange_rate', models.DecimalField(decimal_places=4, default=1, max_digits=10, verbose_name='سعر الصرف')),
                ('amount_in_base_currency', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='المبلغ بالعملة الأساسية')),
                ('status', models.CharField(choices=[('PENDING', 'معلق'), ('APPROVED', 'معتمد'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي'), ('REJECTED', 'مرفوض')], default='PENDING', max_length=20, verbose_name='الحالة')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('transaction_date', models.DateField(verbose_name='تاريخ المعاملة')),
                ('due_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الاستحقاق')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_transactions', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='transactions', to='core.keycompany', verbose_name='الشركة')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_transactions', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'معاملة مالية',
                'verbose_name_plural': 'المعاملات المالية',
                'ordering': ['-transaction_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='KeyInventoryMovement',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('movement_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الحركة')),
                ('movement_type', models.CharField(choices=[('IN', 'وارد'), ('OUT', 'صادر'), ('TRANSFER', 'تحويل'), ('ADJUSTMENT', 'تسوية'), ('RETURN', 'مرتجع')], max_length=20, verbose_name='نوع الحركة')),
                ('movement_reason', models.CharField(choices=[('PURCHASE', 'شراء'), ('SALE', 'بيع'), ('PRODUCTION', 'إنتاج'), ('CONSUMPTION', 'استهلاك'), ('DAMAGE', 'تلف'), ('EXPIRY', 'انتهاء صلاحية'), ('THEFT', 'سرقة'), ('CORRECTION', 'تصحيح'), ('SAMPLE', 'عينة'), ('OTHER', 'أخرى')], max_length=20, verbose_name='سبب الحركة')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=12, verbose_name='الكمية')),
                ('unit_cost', models.DecimalField(decimal_places=4, default=0, max_digits=12, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='التكلفة الإجمالية')),
                ('warehouse', models.CharField(max_length=100, verbose_name='المخزن')),
                ('location', models.CharField(blank=True, max_length=100, null=True, verbose_name='الموقع داخل المخزن')),
                ('reference_document', models.CharField(blank=True, max_length=100, null=True, verbose_name='المستند المرجعي')),
                ('movement_date', models.DateField(verbose_name='تاريخ الحركة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('responsible_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='inventory_movements', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم المسؤول')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_movements', to='core.keyproduct', verbose_name='المنتج')),
                ('related_transaction', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='inventory_movements', to='core.keytransaction', verbose_name='المعاملة المرتبطة')),
            ],
            options={
                'verbose_name': 'حركة مخزون',
                'verbose_name_plural': 'حركات المخزون',
                'ordering': ['-movement_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='KeyDocument',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('document_name', models.CharField(max_length=200, verbose_name='اسم المستند')),
                ('document_type', models.CharField(choices=[('CONTRACT', 'عقد'), ('INVOICE', 'فاتورة'), ('RECEIPT', 'إيصال'), ('CERTIFICATE', 'شهادة'), ('REPORT', 'تقرير'), ('IMAGE', 'صورة'), ('SPREADSHEET', 'جدول بيانات'), ('PDF', 'ملف PDF'), ('OTHER', 'أخرى')], max_length=20, verbose_name='نوع المستند')),
                ('file', models.FileField(upload_to='documents/%Y/%m/', verbose_name='الملف')),
                ('file_size', models.PositiveIntegerField(blank=True, null=True, verbose_name='حجم الملف (بايت)')),
                ('file_extension', models.CharField(blank=True, max_length=10, null=True, verbose_name='امتداد الملف')),
                ('confidentiality_level', models.CharField(choices=[('PUBLIC', 'عام'), ('INTERNAL', 'داخلي'), ('CONFIDENTIAL', 'سري'), ('RESTRICTED', 'محدود')], default='INTERNAL', max_length=20, verbose_name='مستوى السرية')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('tags', models.CharField(blank=True, help_text='علامات مفصولة بفواصل', max_length=200, null=True, verbose_name='العلامات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_documents', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('related_company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='documents', to='core.keycompany', verbose_name='الشركة المرتبطة')),
                ('related_transaction', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='documents', to='core.keytransaction', verbose_name='المعاملة المرتبطة')),
            ],
            options={
                'verbose_name': 'مستند',
                'verbose_name_plural': 'المستندات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='KeyUserPermission',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('module', models.CharField(choices=[('IMPORT_MODULE', 'موديول الاستيراد'), ('STOCK_MODULE', 'موديول المخزون'), ('FINANCE_MODULE', 'موديول المالية'), ('SALES_MODULE', 'موديول المبيعات'), ('CRM_MODULE', 'موديول علاقات العملاء'), ('HR_MODULE', 'موديول الموارد البشرية'), ('LOGISTICS_MODULE', 'موديول اللوجستيات'), ('REPORTING_MODULE', 'موديول التقارير'), ('USERS_MODULE', 'موديول إدارة المستخدمين'), ('KAMACHAT', 'تطبيق المحادثة'), ('HAWK', 'تطبيق الإدارة العليا')], max_length=30, verbose_name='الموديول')),
                ('permission_type', models.CharField(choices=[('VIEW', 'عرض'), ('ADD', 'إضافة'), ('EDIT', 'تعديل'), ('DELETE', 'حذف'), ('APPROVE', 'موافقة'), ('REJECT', 'رفض'), ('EXPORT', 'تصدير'), ('IMPORT', 'استيراد'), ('PRINT', 'طباعة'), ('ARCHIVE', 'أرشفة')], max_length=20, verbose_name='نوع الصلاحية')),
                ('is_granted', models.BooleanField(default=True, verbose_name='ممنوحة')),
                ('financial_limit', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='الحد المالي')),
                ('granted_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ المنح')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('granted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='granted_permissions', to=settings.AUTH_USER_MODEL, verbose_name='منح بواسطة')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='permissions', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'صلاحية مستخدم',
                'verbose_name_plural': 'صلاحيات المستخدمين',
            },
        ),
        migrations.CreateModel(
            name='KeyUserSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_key', models.CharField(max_length=40, unique=True, verbose_name='مفتاح الجلسة')),
                ('ip_address', models.GenericIPAddressField(verbose_name='عنوان IP')),
                ('user_agent', models.TextField(verbose_name='معلومات المتصفح')),
                ('login_time', models.DateTimeField(auto_now_add=True, verbose_name='وقت الدخول')),
                ('last_activity', models.DateTimeField(auto_now=True, verbose_name='آخر نشاط')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشطة')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'جلسة مستخدم',
                'verbose_name_plural': 'جلسات المستخدمين',
                'ordering': ['-login_time'],
            },
        ),
        migrations.AddIndex(
            model_name='keyuser',
            index=models.Index(fields=['employee_id'], name='core_keyuse_employe_4ffa18_idx'),
        ),
        migrations.AddIndex(
            model_name='keyuser',
            index=models.Index(fields=['department'], name='core_keyuse_departm_044bfc_idx'),
        ),
        migrations.AddIndex(
            model_name='keyuser',
            index=models.Index(fields=['user_level'], name='core_keyuse_user_le_7ccc61_idx'),
        ),
        migrations.AddIndex(
            model_name='keyuser',
            index=models.Index(fields=['is_active'], name='core_keyuse_is_acti_6f6ee7_idx'),
        ),
        migrations.AddIndex(
            model_name='keyapproval',
            index=models.Index(fields=['approval_type'], name='core_keyapp_approva_ca428c_idx'),
        ),
        migrations.AddIndex(
            model_name='keyapproval',
            index=models.Index(fields=['status'], name='core_keyapp_status_6ed3e1_idx'),
        ),
        migrations.AddIndex(
            model_name='keyapproval',
            index=models.Index(fields=['requester'], name='core_keyapp_request_67a453_idx'),
        ),
        migrations.AddIndex(
            model_name='keyapproval',
            index=models.Index(fields=['approver'], name='core_keyapp_approve_f5154d_idx'),
        ),
        migrations.AddIndex(
            model_name='keyapproval',
            index=models.Index(fields=['priority'], name='core_keyapp_priorit_932816_idx'),
        ),
        migrations.AddIndex(
            model_name='keyauditlog',
            index=models.Index(fields=['user'], name='core_keyaud_user_id_16b4c8_idx'),
        ),
        migrations.AddIndex(
            model_name='keyauditlog',
            index=models.Index(fields=['operation_type'], name='core_keyaud_operati_b23643_idx'),
        ),
        migrations.AddIndex(
            model_name='keyauditlog',
            index=models.Index(fields=['module'], name='core_keyaud_module_b24db6_idx'),
        ),
        migrations.AddIndex(
            model_name='keyauditlog',
            index=models.Index(fields=['table_name'], name='core_keyaud_table_n_1fc193_idx'),
        ),
        migrations.AddIndex(
            model_name='keyauditlog',
            index=models.Index(fields=['created_at'], name='core_keyaud_created_2dbd8f_idx'),
        ),
        migrations.AddIndex(
            model_name='keyauditlog',
            index=models.Index(fields=['ip_address'], name='core_keyaud_ip_addr_fb97d6_idx'),
        ),
        migrations.AddIndex(
            model_name='keycompany',
            index=models.Index(fields=['company_code'], name='core_keycom_company_8530ce_idx'),
        ),
        migrations.AddIndex(
            model_name='keycompany',
            index=models.Index(fields=['company_type'], name='core_keycom_company_8501ce_idx'),
        ),
        migrations.AddIndex(
            model_name='keycompany',
            index=models.Index(fields=['country'], name='core_keycom_country_6e3dd3_idx'),
        ),
        migrations.AddIndex(
            model_name='keycompany',
            index=models.Index(fields=['is_active'], name='core_keycom_is_acti_2149ae_idx'),
        ),
        migrations.AddIndex(
            model_name='keycompany',
            index=models.Index(fields=['is_approved'], name='core_keycom_is_appr_a1c964_idx'),
        ),
        migrations.AddIndex(
            model_name='keycompany',
            index=models.Index(fields=['rating'], name='core_keycom_rating_04fda8_idx'),
        ),
        migrations.AddIndex(
            model_name='keycompanycontact',
            index=models.Index(fields=['company', 'contact_type'], name='core_keycom_company_0f57c0_idx'),
        ),
        migrations.AddIndex(
            model_name='keycompanycontact',
            index=models.Index(fields=['is_primary'], name='core_keycom_is_prim_41ea67_idx'),
        ),
        migrations.AddIndex(
            model_name='keycompanycontact',
            index=models.Index(fields=['is_active'], name='core_keycom_is_acti_a9d1cb_idx'),
        ),
        migrations.AddIndex(
            model_name='keynotification',
            index=models.Index(fields=['recipient', 'is_read'], name='core_keynot_recipie_31b141_idx'),
        ),
        migrations.AddIndex(
            model_name='keynotification',
            index=models.Index(fields=['notification_type'], name='core_keynot_notific_72494e_idx'),
        ),
        migrations.AddIndex(
            model_name='keynotification',
            index=models.Index(fields=['priority'], name='core_keynot_priorit_63de27_idx'),
        ),
        migrations.AddIndex(
            model_name='keynotification',
            index=models.Index(fields=['created_at'], name='core_keynot_created_b97206_idx'),
        ),
        migrations.AddIndex(
            model_name='keyproduct',
            index=models.Index(fields=['product_code'], name='core_keypro_product_6590ea_idx'),
        ),
        migrations.AddIndex(
            model_name='keyproduct',
            index=models.Index(fields=['category'], name='core_keypro_categor_e37194_idx'),
        ),
        migrations.AddIndex(
            model_name='keyproduct',
            index=models.Index(fields=['is_active'], name='core_keypro_is_acti_0fdadc_idx'),
        ),
        migrations.AddIndex(
            model_name='keyproduct',
            index=models.Index(fields=['hazard_level'], name='core_keypro_hazard__f977be_idx'),
        ),
        migrations.AddIndex(
            model_name='keyproduct',
            index=models.Index(fields=['manufacturer'], name='core_keypro_manufac_795a87_idx'),
        ),
        migrations.AddIndex(
            model_name='keyproduct',
            index=models.Index(fields=['barcode'], name='core_keypro_barcode_4d8354_idx'),
        ),
        migrations.AddIndex(
            model_name='keyproductimage',
            index=models.Index(fields=['product', 'is_primary'], name='core_keypro_product_e21213_idx'),
        ),
        migrations.AddIndex(
            model_name='keyproductimage',
            index=models.Index(fields=['order'], name='core_keypro_order_82bd45_idx'),
        ),
        migrations.AddIndex(
            model_name='keyshipment',
            index=models.Index(fields=['shipment_number'], name='core_keyshi_shipmen_b83284_idx'),
        ),
        migrations.AddIndex(
            model_name='keyshipment',
            index=models.Index(fields=['shipment_type'], name='core_keyshi_shipmen_5c90a9_idx'),
        ),
        migrations.AddIndex(
            model_name='keyshipment',
            index=models.Index(fields=['status'], name='core_keyshi_status_0793ed_idx'),
        ),
        migrations.AddIndex(
            model_name='keyshipment',
            index=models.Index(fields=['tracking_number'], name='core_keyshi_trackin_0c13fc_idx'),
        ),
        migrations.AddIndex(
            model_name='keytransaction',
            index=models.Index(fields=['transaction_number'], name='core_keytra_transac_7d0bcd_idx'),
        ),
        migrations.AddIndex(
            model_name='keytransaction',
            index=models.Index(fields=['transaction_type'], name='core_keytra_transac_cee5cf_idx'),
        ),
        migrations.AddIndex(
            model_name='keytransaction',
            index=models.Index(fields=['status'], name='core_keytra_status_b807fb_idx'),
        ),
        migrations.AddIndex(
            model_name='keytransaction',
            index=models.Index(fields=['transaction_date'], name='core_keytra_transac_1c9162_idx'),
        ),
        migrations.AddIndex(
            model_name='keytransaction',
            index=models.Index(fields=['company'], name='core_keytra_company_9aabc6_idx'),
        ),
        migrations.AddIndex(
            model_name='keyinventorymovement',
            index=models.Index(fields=['movement_number'], name='core_keyinv_movemen_d8d44c_idx'),
        ),
        migrations.AddIndex(
            model_name='keyinventorymovement',
            index=models.Index(fields=['product'], name='core_keyinv_product_79e6c1_idx'),
        ),
        migrations.AddIndex(
            model_name='keyinventorymovement',
            index=models.Index(fields=['movement_type'], name='core_keyinv_movemen_b7dc01_idx'),
        ),
        migrations.AddIndex(
            model_name='keyinventorymovement',
            index=models.Index(fields=['movement_date'], name='core_keyinv_movemen_f50372_idx'),
        ),
        migrations.AddIndex(
            model_name='keyinventorymovement',
            index=models.Index(fields=['warehouse'], name='core_keyinv_warehou_fee38e_idx'),
        ),
        migrations.AddIndex(
            model_name='keydocument',
            index=models.Index(fields=['document_type'], name='core_keydoc_documen_1ab44e_idx'),
        ),
        migrations.AddIndex(
            model_name='keydocument',
            index=models.Index(fields=['confidentiality_level'], name='core_keydoc_confide_4f4505_idx'),
        ),
        migrations.AddIndex(
            model_name='keydocument',
            index=models.Index(fields=['created_at'], name='core_keydoc_created_fea820_idx'),
        ),
        migrations.AddIndex(
            model_name='keydocument',
            index=models.Index(fields=['related_company'], name='core_keydoc_related_293a83_idx'),
        ),
        migrations.AddIndex(
            model_name='keyuserpermission',
            index=models.Index(fields=['user', 'module'], name='core_keyuse_user_id_b05e3d_idx'),
        ),
        migrations.AddIndex(
            model_name='keyuserpermission',
            index=models.Index(fields=['permission_type'], name='core_keyuse_permiss_295554_idx'),
        ),
        migrations.AddIndex(
            model_name='keyuserpermission',
            index=models.Index(fields=['is_granted'], name='core_keyuse_is_gran_9c1824_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='keyuserpermission',
            unique_together={('user', 'module', 'permission_type')},
        ),
        migrations.AddIndex(
            model_name='keyusersession',
            index=models.Index(fields=['user', 'is_active'], name='core_keyuse_user_id_181595_idx'),
        ),
        migrations.AddIndex(
            model_name='keyusersession',
            index=models.Index(fields=['session_key'], name='core_keyuse_session_81e3f1_idx'),
        ),
        migrations.AddIndex(
            model_name='keyusersession',
            index=models.Index(fields=['last_activity'], name='core_keyuse_last_ac_f6e74d_idx'),
        ),
    ]
