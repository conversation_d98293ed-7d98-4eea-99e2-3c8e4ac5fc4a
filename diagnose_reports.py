#!/usr/bin/env python
"""
Complete Diagnostic Script for Reports Issues
"""
import os
import sys
import django

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

def diagnose_reports_issue():
    """Complete diagnosis of reports system"""
    print("🔍 COMPLETE REPORTS DIAGNOSTIC")
    print("=" * 60)
    
    # 1. Test URL resolution
    print("1️⃣ TESTING URL RESOLUTION:")
    try:
        from django.urls import reverse, resolve
        from django.test import RequestFactory
        
        # Test all reports URLs
        reports_urls = [
            ('reports_main_dashboard', '/reports/'),
            ('current_stock_balances_report', '/reports/stock-balances/'),
            ('stock_movements_report', '/reports/stock-movements/'),
            ('warehouse_stock_report', '/reports/warehouse-stock/'),
            ('bin_location_stock_report', '/reports/bin-location-stock/'),
        ]
        
        for name, expected_url in reports_urls:
            try:
                url = reverse(f'inventory:{name}')
                resolver = resolve(url)
                status = "✅ OK" if url == expected_url else f"⚠️  Expected {expected_url}, got {url}"
                print(f"  {name}: {status}")
                print(f"    URL: {url}")
                print(f"    View: {resolver.func.__name__}")
            except Exception as e:
                print(f"  ❌ {name}: ERROR - {str(e)}")
                
    except Exception as e:
        print(f"❌ URL resolution failed: {str(e)}")
    
    # 2. Test template existence and syntax
    print("\n2️⃣ TESTING TEMPLATES:")
    template_files = [
        'templates/inventory/reports/main_dashboard.html',
        'templates/inventory/reports/current_stock_balances.html',
        'templates/inventory/reports/stock_movements.html', 
        'templates/inventory/reports/warehouse_stock.html',
        'templates/inventory/reports/bin_location_stock.html'
    ]
    
    for template_path in template_files:
        if os.path.exists(template_path):
            # Check if template extends the correct base
            try:
                with open(template_path, 'r', encoding='utf-8') as f:
                    first_line = f.readline().strip()
                    if 'extends "base.html"' in first_line:
                        print(f"  ✅ {template_path.split('/')[-1]}: Correct base template")
                    elif 'extends "inventory/base.html"' in first_line:
                        print(f"  ❌ {template_path.split('/')[-1]}: WRONG base template (inventory/base.html)")
                    else:
                        print(f"  ⚠️  {template_path.split('/')[-1]}: Unexpected extends: {first_line}")
            except Exception as e:
                print(f"  ❌ {template_path.split('/')[-1]}: Read error - {str(e)}")
        else:
            print(f"  ❌ {template_path.split('/')[-1]}: NOT FOUND")
    
    # 3. Test user authentication and profiles
    print("\n3️⃣ TESTING USER AUTHENTICATION:")
    try:
        from django.contrib.auth.models import User
        from inventory.models import UserProfile
        
        test_users = ['admin', 'anas']
        for username in test_users:
            try:
                user = User.objects.get(username=username)
                try:
                    profile = UserProfile.objects.get(user=user)
                    warehouse = profile.assigned_warehouse
                    print(f"  ✅ {username}: Has profile, Warehouse: {warehouse.warehouse_name if warehouse else 'None'}")
                except UserProfile.DoesNotExist:
                    print(f"  ❌ {username}: No UserProfile - WILL CAUSE REDIRECT")
            except User.DoesNotExist:
                print(f"  ❌ {username}: User not found")
                
    except Exception as e:
        print(f"  ❌ User test failed: {str(e)}")
    
    # 4. Test view functions directly
    print("\n4️⃣ TESTING VIEW FUNCTIONS:")
    try:
        from inventory.views import reports_main_dashboard, get_user_warehouse
        from django.test import RequestFactory
        from django.contrib.auth.models import AnonymousUser, User
        from django.contrib.sessions.backends.db import SessionStore
        from django.contrib.messages.storage.fallback import FallbackStorage
        
        # Create a mock request
        factory = RequestFactory()
        request = factory.get('/reports/')
        
        # Test with admin user
        try:
            user = User.objects.get(username='admin')
            request.user = user
            
            # Add session and messages to request
            session = SessionStore()
            session.create()
            request.session = session
            
            setattr(request, '_messages', FallbackStorage(request))
            
            print(f"  Testing reports_main_dashboard with admin user...")
            
            # Test get_user_warehouse first
            warehouse = get_user_warehouse(user)
            print(f"    get_user_warehouse(admin): {warehouse}")
            
            if warehouse:
                response = reports_main_dashboard(request)
                if hasattr(response, 'status_code'):
                    print(f"  ✅ reports_main_dashboard: Returns response with status {response.status_code}")
                elif hasattr(response, 'template_name'):
                    print(f"  ✅ reports_main_dashboard: Returns TemplateResponse - {response.template_name}")
                else:
                    print(f"  ✅ reports_main_dashboard: Returns response type: {type(response)}")
            else:
                print(f"  ❌ get_user_warehouse returned None - WILL REDIRECT TO DASHBOARD")
                
        except User.DoesNotExist:
            print(f"  ❌ Admin user not found")
        except Exception as e:
            print(f"  ❌ View test failed: {str(e)}")
            
    except Exception as e:
        print(f"  ❌ View function test failed: {str(e)}")
    
    # 5. Test direct HTTP request
    print("\n5️⃣ TESTING HTTP REQUESTS:")
    try:
        from django.test import Client
        
        client = Client()
        
        # Test without login (should redirect to login)
        response = client.get('/reports/')
        print(f"  Reports without login: {response.status_code} - {response.get('Location', 'No redirect')}")
        
        # Test with admin login
        try:
            admin_user = User.objects.get(username='admin')
            client.force_login(admin_user)
            
            response = client.get('/reports/')
            print(f"  Reports with admin login: {response.status_code}")
            
            if response.status_code == 302:
                print(f"    REDIRECT TO: {response.get('Location')}")
                print(f"    ❌ STILL REDIRECTING - PROBLEM PERSISTS")
            elif response.status_code == 200:
                print(f"    ✅ SUCCESS - Template rendered correctly")
            else:
                print(f"    ⚠️  Unexpected status code: {response.status_code}")
                
        except User.DoesNotExist:
            print(f"  ❌ Admin user not found for login test")
            
    except Exception as e:
        print(f"  ❌ HTTP request test failed: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🏁 DIAGNOSTIC COMPLETE")
    
    # Provide recommendations
    print("\n💡 RECOMMENDATIONS:")
    print("1. Check template extends statements")
    print("2. Verify user profiles exist with warehouse assignments")
    print("3. Check for redirect logic in views")
    print("4. Test with different users")

if __name__ == "__main__":
    diagnose_reports_issue()