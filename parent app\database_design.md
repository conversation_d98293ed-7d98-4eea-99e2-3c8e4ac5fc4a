# تصميم قاعدة البيانات - مشر<PERSON><PERSON> KamaVerse

## نظرة عامة

قاعدة البيانات مصممة لتكون مترابطة ومتكاملة مع جميع الموديولات. تستخدم PostgreSQL لضمان الأداء العالي والموثوقية.

---

## الجداول المرتبطة (Key Tables)

### 1. KeyUsers - المستخدمين الأساسي
```sql
CREATE TABLE key_users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    user_level VARCHAR(20) NOT NULL, -- SUPER_ADMIN, ADMIN, MANAGER, EMPLOYEE, VIEWER
    department VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES key_users(id)
);
```

### 2. KeyUserPermissions - صلاحيات المستخدمين
```sql
CREATE TABLE key_user_permissions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES key_users(id) ON DELETE CASCADE,
    module_name VARCHAR(50) NOT NULL,
    permission_type VARCHAR(20) NOT NULL, -- VIEW, ADD, EDIT, DELETE, APPROVE, etc.
    is_granted BOOLEAN DEFAULT FALSE,
    financial_limit DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES key_users(id)
);
```

### 3. KeyCompanies - الشركات (موردين وعملاء)
```sql
CREATE TABLE key_companies (
    id SERIAL PRIMARY KEY,
    company_name VARCHAR(200) NOT NULL,
    company_type VARCHAR(20) NOT NULL, -- SUPPLIER, CUSTOMER, BOTH
    tax_number VARCHAR(50),
    commercial_register VARCHAR(50),
    contact_person VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    city VARCHAR(50),
    country VARCHAR(50),
    payment_terms INTEGER DEFAULT 30, -- أيام الدفع
    credit_limit DECIMAL(15,2) DEFAULT 0,
    rating INTEGER DEFAULT 0, -- تقييم من 1-5
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES key_users(id)
);
```

### 4. KeyProducts - المنتجات والمواد الخام
```sql
CREATE TABLE key_products (
    id SERIAL PRIMARY KEY,
    product_name VARCHAR(200) NOT NULL,
    internal_code VARCHAR(50) UNIQUE NOT NULL,
    international_code VARCHAR(50),
    category VARCHAR(100) NOT NULL,
    subcategory VARCHAR(100),
    unit_of_measure VARCHAR(20) NOT NULL, -- KG, TON, LITER, etc.
    hazard_level VARCHAR(20), -- LOW, MEDIUM, HIGH, CRITICAL
    storage_temperature_min DECIMAL(5,2),
    storage_temperature_max DECIMAL(5,2),
    shelf_life_months INTEGER,
    minimum_stock_level DECIMAL(10,2),
    maximum_stock_level DECIMAL(10,2),
    standard_cost DECIMAL(10,2),
    selling_price DECIMAL(10,2),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES key_users(id)
);
```

### 5. KeyTransactions - المعاملات المالية
```sql
CREATE TABLE key_transactions (
    id SERIAL PRIMARY KEY,
    transaction_type VARCHAR(50) NOT NULL, -- PURCHASE, SALE, PAYMENT, RECEIPT, etc.
    reference_number VARCHAR(100) UNIQUE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'EGP',
    exchange_rate DECIMAL(10,4) DEFAULT 1,
    transaction_date DATE NOT NULL,
    due_date DATE,
    company_id INTEGER REFERENCES key_companies(id),
    bank_account VARCHAR(100),
    payment_method VARCHAR(50), -- CASH, BANK_TRANSFER, CHECK, etc.
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, APPROVED, PAID, CANCELLED
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES key_users(id),
    approved_by INTEGER REFERENCES key_users(id),
    approved_at TIMESTAMP
);
```

### 6. KeyDocuments - المستندات
```sql
CREATE TABLE key_documents (
    id SERIAL PRIMARY KEY,
    document_name VARCHAR(200) NOT NULL,
    document_type VARCHAR(50) NOT NULL, -- INVOICE, CONTRACT, CERTIFICATE, etc.
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    mime_type VARCHAR(100),
    related_module VARCHAR(50),
    related_record_id INTEGER,
    confidentiality_level VARCHAR(20) DEFAULT 'NORMAL', -- PUBLIC, NORMAL, CONFIDENTIAL, SECRET
    is_archived BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES key_users(id)
);
```

### 7. KeyApprovals - نظام الموافقات
```sql
CREATE TABLE key_approvals (
    id SERIAL PRIMARY KEY,
    approval_type VARCHAR(50) NOT NULL, -- PURCHASE, SALE, PAYMENT, HIRING, etc.
    related_module VARCHAR(50) NOT NULL,
    related_record_id INTEGER NOT NULL,
    amount DECIMAL(15,2),
    requester_id INTEGER REFERENCES key_users(id) NOT NULL,
    approver_id INTEGER REFERENCES key_users(id),
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, APPROVED, REJECTED
    request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approval_date TIMESTAMP,
    rejection_reason TEXT,
    notes TEXT
);
```

### 8. KeyShipments - الشحنات
```sql
CREATE TABLE key_shipments (
    id SERIAL PRIMARY KEY,
    shipment_number VARCHAR(100) UNIQUE NOT NULL,
    shipment_type VARCHAR(20) NOT NULL, -- INBOUND, OUTBOUND
    company_id INTEGER REFERENCES key_companies(id),
    origin_port VARCHAR(100),
    destination_port VARCHAR(100),
    transport_method VARCHAR(50), -- SEA, AIR, LAND
    carrier_name VARCHAR(200),
    tracking_number VARCHAR(100),
    dispatch_date DATE,
    expected_arrival_date DATE,
    actual_arrival_date DATE,
    status VARCHAR(20) DEFAULT 'PREPARING', -- PREPARING, SHIPPED, IN_TRANSIT, ARRIVED, DELIVERED
    shipping_cost DECIMAL(12,2),
    insurance_cost DECIMAL(12,2),
    customs_cost DECIMAL(12,2),
    total_weight DECIMAL(10,2),
    total_volume DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES key_users(id)
);
```

### 9. KeyInventory - حركة المخزون
```sql
CREATE TABLE key_inventory (
    id SERIAL PRIMARY KEY,
    movement_type VARCHAR(20) NOT NULL, -- IN, OUT, TRANSFER, ADJUSTMENT
    product_id INTEGER REFERENCES key_products(id) NOT NULL,
    warehouse_location VARCHAR(100),
    quantity DECIMAL(10,2) NOT NULL,
    unit_cost DECIMAL(10,2),
    total_cost DECIMAL(12,2),
    batch_number VARCHAR(50),
    expiry_date DATE,
    movement_date DATE NOT NULL,
    reference_type VARCHAR(50), -- PURCHASE, SALE, TRANSFER, ADJUSTMENT
    reference_id INTEGER,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES key_users(id)
);
```

### 10. KeyNotifications - الإشعارات
```sql
CREATE TABLE key_notifications (
    id SERIAL PRIMARY KEY,
    notification_type VARCHAR(50) NOT NULL, -- ALERT, INFO, WARNING, ERROR
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    recipient_id INTEGER REFERENCES key_users(id),
    sender_id INTEGER REFERENCES key_users(id),
    related_module VARCHAR(50),
    related_record_id INTEGER,
    priority VARCHAR(20) DEFAULT 'NORMAL', -- LOW, NORMAL, HIGH, URGENT
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 11. KeyAuditLog - سجل العمليات
```sql
CREATE TABLE key_audit_log (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES key_users(id),
    module_name VARCHAR(50) NOT NULL,
    operation_type VARCHAR(50) NOT NULL, -- CREATE, UPDATE, DELETE, VIEW, APPROVE
    table_name VARCHAR(100),
    record_id INTEGER,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    operation_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## الفهارس (Indexes) للأداء

```sql
-- فهارس المستخدمين
CREATE INDEX idx_key_users_username ON key_users(username);
CREATE INDEX idx_key_users_email ON key_users(email);
CREATE INDEX idx_key_users_department ON key_users(department);

-- فهارس الصلاحيات
CREATE INDEX idx_key_user_permissions_user_module ON key_user_permissions(user_id, module_name);

-- فهارس الشركات
CREATE INDEX idx_key_companies_type ON key_companies(company_type);
CREATE INDEX idx_key_companies_name ON key_companies(company_name);

-- فهارس المنتجات
CREATE INDEX idx_key_products_code ON key_products(internal_code);
CREATE INDEX idx_key_products_category ON key_products(category);

-- فهارس المعاملات
CREATE INDEX idx_key_transactions_date ON key_transactions(transaction_date);
CREATE INDEX idx_key_transactions_company ON key_transactions(company_id);
CREATE INDEX idx_key_transactions_type ON key_transactions(transaction_type);

-- فهارس المخزون
CREATE INDEX idx_key_inventory_product_date ON key_inventory(product_id, movement_date);
CREATE INDEX idx_key_inventory_warehouse ON key_inventory(warehouse_location);

-- فهارس الإشعارات
CREATE INDEX idx_key_notifications_recipient ON key_notifications(recipient_id, is_read);

-- فهارس سجل العمليات
CREATE INDEX idx_key_audit_log_user_date ON key_audit_log(user_id, operation_timestamp);
CREATE INDEX idx_key_audit_log_module ON key_audit_log(module_name);
```

---

## العلاقات والقيود (Constraints)

```sql
-- قيود التحقق
ALTER TABLE key_users ADD CONSTRAINT chk_user_level 
    CHECK (user_level IN ('SUPER_ADMIN', 'ADMIN', 'MANAGER', 'EMPLOYEE', 'VIEWER'));

ALTER TABLE key_companies ADD CONSTRAINT chk_company_type 
    CHECK (company_type IN ('SUPPLIER', 'CUSTOMER', 'BOTH'));

ALTER TABLE key_products ADD CONSTRAINT chk_hazard_level 
    CHECK (hazard_level IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL'));

ALTER TABLE key_transactions ADD CONSTRAINT chk_transaction_status 
    CHECK (status IN ('PENDING', 'APPROVED', 'PAID', 'CANCELLED'));

-- قيود القيم الموجبة
ALTER TABLE key_products ADD CONSTRAINT chk_positive_prices 
    CHECK (standard_cost >= 0 AND selling_price >= 0);

ALTER TABLE key_transactions ADD CONSTRAINT chk_positive_amount 
    CHECK (amount > 0);
```

---

## إعدادات النسخ الاحتياطي

```sql
-- إعداد النسخ الاحتياطي التلقائي
-- يتم تنفيذه يومياً في الساعة 2:00 صباحاً
-- pg_dump kamaverse_db > backup_$(date +%Y%m%d).sql
```

---

## ملاحظات مهمة

1. **الأداء:** جميع الجداول مفهرسة بشكل مناسب
2. **الأمان:** كلمات المرور مشفرة، والبيانات الحساسة محمية
3. **التكامل:** العلاقات محددة بوضوح بين الجداول
4. **المرونة:** إمكانية إضافة حقول جديدة دون تعطيل النظام
5. **النسخ الاحتياطي:** نظام نسخ احتياطي تلقائي
6. **المراقبة:** تسجيل جميع العمليات المهمة

---

**آخر تحديث:** 2025-08-15  
**قاعدة البيانات:** PostgreSQL 14+  
**الترميز:** UTF-8 (دعم اللغة العربية)
