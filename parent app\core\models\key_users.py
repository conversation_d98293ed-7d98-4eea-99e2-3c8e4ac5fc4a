"""
KeyUsers - جدول المستخدمين الأساسي
إدارة جميع مستخدمي النظام وصلاحياتهم المفصلة
"""

from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.core.validators import RegexValidator
import uuid


class KeyUser(AbstractUser):
    """
    نموذج المستخدم الأساسي لنظام KamaVerse
    يوسع نموذج Django الافتراضي بالحقول المطلوبة
    """
    
    # User levels
    USER_LEVELS = [
        ('SUPER_ADMIN', 'مدير أعلى'),
        ('ADMIN', 'مدير'),
        ('MANAGER', 'رئيس قسم'),
        ('EMPLOYEE', 'موظف'),
        ('VIEWER', 'مطلع'),
    ]
    
    # Departments
    DEPARTMENTS = [
        ('IMPORT', 'قسم الاستيراد'),
        ('STOCK', 'قسم المخزون'),
        ('FINANCE', 'قسم المالية'),
        ('SALES', 'قسم المبيعات'),
        ('CRM', 'قسم علاقات العملاء'),
        ('HR', 'قسم الموارد البشرية'),
        ('LOGISTICS', 'قسم اللوجستيات'),
        ('REPORTING', 'قسم التقارير'),
        ('IT', 'قسم تقنية المعلومات'),
        ('MANAGEMENT', 'الإدارة العليا'),
    ]
    
    # Primary key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Personal Information
    arabic_name = models.CharField(
        max_length=100,
        verbose_name='الاسم بالعربية',
        help_text='الاسم الكامل للمستخدم بالعربية'
    )
    
    employee_id = models.CharField(
        max_length=20,
        unique=True,
        verbose_name='رقم الموظف',
        help_text='رقم الموظف الفريد في الشركة'
    )
    
    national_id = models.CharField(
        max_length=14,
        unique=True,
        validators=[RegexValidator(r'^\d{14}$', 'رقم الهوية يجب أن يكون 14 رقم')],
        verbose_name='رقم الهوية القومية',
        blank=True,
        null=True
    )
    
    phone_number = models.CharField(
        max_length=15,
        validators=[RegexValidator(r'^\+?1?\d{9,15}$', 'رقم الهاتف غير صحيح')],
        verbose_name='رقم الهاتف',
        blank=True,
        null=True
    )
    
    # Work Information
    user_level = models.CharField(
        max_length=20,
        choices=USER_LEVELS,
        default='EMPLOYEE',
        verbose_name='مستوى المستخدم'
    )
    
    department = models.CharField(
        max_length=20,
        choices=DEPARTMENTS,
        verbose_name='القسم'
    )
    
    job_title = models.CharField(
        max_length=100,
        verbose_name='المسمى الوظيفي',
        blank=True,
        null=True
    )
    
    hire_date = models.DateField(
        verbose_name='تاريخ التوظيف',
        blank=True,
        null=True
    )
    
    direct_manager = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        verbose_name='المدير المباشر',
        related_name='subordinates'
    )
    
    # System Information
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )
    
    last_login_ip = models.GenericIPAddressField(
        blank=True,
        null=True,
        verbose_name='آخر IP للدخول'
    )
    
    failed_login_attempts = models.PositiveIntegerField(
        default=0,
        verbose_name='محاولات الدخول الفاشلة'
    )
    
    password_changed_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ آخر تغيير كلمة المرور'
    )
    
    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ آخر تحديث'
    )
    
    created_by = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        verbose_name='أنشئ بواسطة',
        related_name='created_users'
    )
    
    class Meta:
        verbose_name = 'مستخدم'
        verbose_name_plural = 'المستخدمون'
        ordering = ['arabic_name']
        indexes = [
            models.Index(fields=['employee_id']),
            models.Index(fields=['department']),
            models.Index(fields=['user_level']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return f"{self.arabic_name} ({self.employee_id})"
    
    def get_full_name(self):
        """إرجاع الاسم الكامل بالعربية"""
        return self.arabic_name or self.get_full_name()
    
    def get_department_display_ar(self):
        """إرجاع اسم القسم بالعربية"""
        return dict(self.DEPARTMENTS).get(self.department, self.department)
    
    def get_user_level_display_ar(self):
        """إرجاع مستوى المستخدم بالعربية"""
        return dict(self.USER_LEVELS).get(self.user_level, self.user_level)
    
    def can_access_module(self, module_name):
        """التحقق من إمكانية الوصول لموديول معين"""
        # سيتم تطوير هذه الوظيفة مع نظام الصلاحيات
        return True
    
    def has_permission(self, permission_name):
        """التحقق من وجود صلاحية معينة"""
        # سيتم تطوير هذه الوظيفة مع نظام الصلاحيات
        return True
    
    def reset_failed_login_attempts(self):
        """إعادة تعيين محاولات الدخول الفاشلة"""
        self.failed_login_attempts = 0
        self.save(update_fields=['failed_login_attempts'])
    
    def increment_failed_login_attempts(self):
        """زيادة عدد محاولات الدخول الفاشلة"""
        self.failed_login_attempts += 1
        self.save(update_fields=['failed_login_attempts'])
    
    @property
    def is_account_locked(self):
        """التحقق من قفل الحساب"""
        from django.conf import settings
        max_attempts = getattr(settings, 'KAMAVERSE_SETTINGS', {}).get('MAX_LOGIN_ATTEMPTS', 3)
        return self.failed_login_attempts >= max_attempts


class KeyUserPermission(models.Model):
    """
    صلاحيات المستخدمين التفصيلية
    """
    
    PERMISSION_TYPES = [
        ('VIEW', 'عرض'),
        ('ADD', 'إضافة'),
        ('EDIT', 'تعديل'),
        ('DELETE', 'حذف'),
        ('APPROVE', 'موافقة'),
        ('REJECT', 'رفض'),
        ('EXPORT', 'تصدير'),
        ('IMPORT', 'استيراد'),
        ('PRINT', 'طباعة'),
        ('ARCHIVE', 'أرشفة'),
    ]
    
    MODULES = [
        ('IMPORT_MODULE', 'موديول الاستيراد'),
        ('STOCK_MODULE', 'موديول المخزون'),
        ('FINANCE_MODULE', 'موديول المالية'),
        ('SALES_MODULE', 'موديول المبيعات'),
        ('CRM_MODULE', 'موديول علاقات العملاء'),
        ('HR_MODULE', 'موديول الموارد البشرية'),
        ('LOGISTICS_MODULE', 'موديول اللوجستيات'),
        ('REPORTING_MODULE', 'موديول التقارير'),
        ('USERS_MODULE', 'موديول إدارة المستخدمين'),
        ('KAMACHAT', 'تطبيق المحادثة'),
        ('HAWK', 'تطبيق الإدارة العليا'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    user = models.ForeignKey(
        KeyUser,
        on_delete=models.CASCADE,
        related_name='permissions',
        verbose_name='المستخدم'
    )
    
    module = models.CharField(
        max_length=30,
        choices=MODULES,
        verbose_name='الموديول'
    )
    
    permission_type = models.CharField(
        max_length=20,
        choices=PERMISSION_TYPES,
        verbose_name='نوع الصلاحية'
    )
    
    is_granted = models.BooleanField(
        default=True,
        verbose_name='ممنوحة'
    )
    
    financial_limit = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name='الحد المالي'
    )
    
    granted_by = models.ForeignKey(
        KeyUser,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='granted_permissions',
        verbose_name='منح بواسطة'
    )
    
    granted_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ المنح'
    )
    
    expires_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='تاريخ الانتهاء'
    )
    
    class Meta:
        verbose_name = 'صلاحية مستخدم'
        verbose_name_plural = 'صلاحيات المستخدمين'
        unique_together = ['user', 'module', 'permission_type']
        indexes = [
            models.Index(fields=['user', 'module']),
            models.Index(fields=['permission_type']),
            models.Index(fields=['is_granted']),
        ]
    
    def __str__(self):
        return f"{self.user.arabic_name} - {self.get_module_display()} - {self.get_permission_type_display()}"


class KeyUserSession(models.Model):
    """
    جلسات المستخدمين النشطة
    """
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    user = models.ForeignKey(
        KeyUser,
        on_delete=models.CASCADE,
        related_name='sessions',
        verbose_name='المستخدم'
    )
    
    session_key = models.CharField(
        max_length=40,
        unique=True,
        verbose_name='مفتاح الجلسة'
    )
    
    ip_address = models.GenericIPAddressField(
        verbose_name='عنوان IP'
    )
    
    user_agent = models.TextField(
        verbose_name='معلومات المتصفح'
    )
    
    login_time = models.DateTimeField(
        auto_now_add=True,
        verbose_name='وقت الدخول'
    )
    
    last_activity = models.DateTimeField(
        auto_now=True,
        verbose_name='آخر نشاط'
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشطة'
    )
    
    class Meta:
        verbose_name = 'جلسة مستخدم'
        verbose_name_plural = 'جلسات المستخدمين'
        ordering = ['-login_time']
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['session_key']),
            models.Index(fields=['last_activity']),
        ]
    
    def __str__(self):
        return f"{self.user.arabic_name} - {self.login_time}"
