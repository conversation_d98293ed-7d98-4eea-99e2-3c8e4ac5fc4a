{% extends 'base.html' %}
{% load static %}

{% block title %}إذن الصرف - لوحة التحكم - KamaVerse{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/items.css' %}">
<style>
    .dashboard-header {
        display: none; /* Hide old header */
    }

    .movements-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 30px;
        margin-bottom: 40px;
    }

    .movement-card {
        background: var(--white);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--line);
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .movement-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(135deg, var(--brand-red), var(--brand-gold));
    }

    .movement-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    /* إضافة تأثيرات للأيقونات */
    .movement-card:hover .stat-icon {
        transform: scale(1.1) rotate(5deg);
        transition: all 0.3s ease;
    }

    /* تحسين الألوان */
    .gold-accent {
        color: var(--brand-gold) !important;
    }

    /* تحسين العرض للشاشات الصغيرة */
    @media (max-width: 768px) {
        .movements-grid {
            grid-template-columns: 1fr;
            gap: 20px;
        }
        
        .stat-icon {
            width: 80px;
            height: 80px;
            font-size: 32px;
        }
        
        .stat-value {
            font-size: 2rem;
        }
        
        .stat-title {
            font-size: 1.5rem;
        }
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(26, 26, 26, 0.12);
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, var(--brand-red), var(--brand-gold));
    }

    .stat-icon {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 40px;
        color: white;
        position: relative;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .stat-icon::before {
        content: '';
        position: absolute;
        inset: -3px;
        border-radius: 50%;
        background: linear-gradient(45deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1));
        z-index: -1;
    }

    .sales-card .stat-icon {
        background: linear-gradient(135deg, #28a745, #20c997);
    }

    .damage-card .stat-icon {
        background: linear-gradient(135deg, #dc3545, #fd7e14);
    }

    .stat-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--ink);
        margin-bottom: 20px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .stat-numbers {
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin-top: 25px;
        padding-top: 25px;
        border-top: 2px solid var(--line);
        gap: 20px;
    }

    .stat-item {
        text-align: center;
        flex: 1;
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 8px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .sales-card .stat-value {
        color: #28a745;
    }

    .damage-card .stat-value {
        color: #dc3545;
    }

    .stat-label {
        color: var(--slate);
        font-size: 1rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
    }

    .btn-create {
        background: var(--brand-red);
        color: var(--white);
        padding: 1rem 2rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        box-shadow: 0 4px 15px rgba(214, 40, 40, 0.3);
    }

    .btn-create:hover {
        background: var(--brand-red-dark);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(214, 40, 40, 0.4);
        color: var(--white);
        text-decoration: none;
    }

    .empty-state {
        text-align: center;
        padding: 3rem;
        color: var(--slate);
    }

    .empty-state i {
        font-size: 4rem;
        color: var(--brand-gold);
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="items-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-arrow-up me-3 gold-accent"></i>
                    إذن الصرف
                </h1>
                <p class="mb-0 opacity-75">إدارة عمليات صرف المواد من المخزون</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 align-items-center justify-content-end">
                    <span class="badge bg-success fs-6">
                        إجمالي المبيعات: {{ sales_count }}
                    </span>
                    <span class="badge bg-danger fs-6">
                        إجمالي التلف: {{ damage_count }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- رأس الصفحة القديم - مخفي -->
    <div class="dashboard-header">
        <h1>إذن الصرف</h1>
        <p>إدارة عمليات صرف المواد من المخزون</p>
    </div>

    <!-- بطاقات الإحصائيات -->
    <div class="movements-grid">
        <!-- بطاقة المبيعات -->
        <div class="movement-card sales-card" onclick="window.location.href='{% url 'inventory:goods_issue_sales_list' %}'">
            <div class="stat-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <h3 class="stat-title">إجمالي المبيعات</h3>
            <div class="stat-numbers">
                <div class="stat-item">
                    <div class="stat-value">{{ sales_count }}</div>
                    <div class="stat-label">عدد الفواتير</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ sales_quantity|floatformat:0 }}</div>
                    <div class="stat-label">إجمالي الكمية</div>
                </div>
            </div>
        </div>

        <!-- بطاقة التلف -->
        <div class="movement-card damage-card" onclick="window.location.href='{% url 'inventory:goods_issue_damage_list' %}'">
            <div class="stat-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 class="stat-title">إجمالي التلف</h3>
            <div class="stat-numbers">
                <div class="stat-item">
                    <div class="stat-value">{{ damage_count }}</div>
                    <div class="stat-label">عدد الفواتير</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ damage_quantity|floatformat:0 }}</div>
                    <div class="stat-label">إجمالي الكمية</div>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="action-buttons">
        <a href="{% url 'inventory:goods_issue_create' %}" class="btn-create">
            <i class="fas fa-plus"></i>
            إنشاء إذن صرف جديد
        </a>
    </div>

    {% if sales_count == 0 and damage_count == 0 %}
    <!-- حالة فارغة -->
    <div class="empty-state">
        <i class="fas fa-clipboard-list"></i>
        <h3>لا توجد أذون صرف حتى الآن</h3>
        <p>ابدأ بإنشاء إذن صرف جديد لتسجيل عمليات البيع أو التلف</p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأثيرات بصرية للبطاقات
    const cards = document.querySelectorAll('.movement-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
{% endblock %}
