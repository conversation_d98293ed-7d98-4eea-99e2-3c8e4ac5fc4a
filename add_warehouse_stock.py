#!/usr/bin/env python
"""
إضافة أصناف حقيقية للمخازن
Add Real Items to Warehouses
"""
import os
import sys
import django
import random
from decimal import Decimal

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

from inventory.models import ItemMaster, Warehouse, BinLocation, StockBalance

def create_sample_items():
    """إنشاء أصناف تجريبية"""
    sample_items = [
        {
            'item_name_ar': 'بولي إيثيلين عالي الكثافة',
            'item_name_en': 'High Density Polyethylene (HDPE)',
            'category': 'PLASTIC_RAW',
            'material_type': 'RAW_MATERIAL',
            'unit_of_measure': 'KG',
            'hazard_level': 'LOW',
        },
        {
            'item_name_ar': 'بولي بروبيلين',
            'item_name_en': 'Polypropylene (PP)',
            'category': 'PLASTIC_RAW',
            'material_type': 'RAW_MATERIAL',
            'unit_of_measure': 'KG',
            'hazard_level': 'LOW',
        },
        {
            'item_name_ar': 'كلوريد الصوديوم',
            'item_name_en': 'Sodium Chloride',
            'category': 'CHEMICALS',
            'material_type': 'RAW_MATERIAL',
            'unit_of_measure': 'KG',
            'hazard_level': 'LOW',
        },
        {
            'item_name_ar': 'حمض الكبريتيك',
            'item_name_en': 'Sulfuric Acid',
            'category': 'ACIDS',
            'material_type': 'RAW_MATERIAL',
            'unit_of_measure': 'LITER',
            'hazard_level': 'HIGH',
        },
        {
            'item_name_ar': 'أكسيد التيتانيوم',
            'item_name_en': 'Titanium Dioxide',
            'category': 'OXIDES',
            'material_type': 'RAW_MATERIAL',
            'unit_of_measure': 'KG',
            'hazard_level': 'MEDIUM',
        },
        {
            'item_name_ar': 'ملون أحمر',
            'item_name_en': 'Red Colorant',
            'category': 'COLORANTS',
            'material_type': 'AUXILIARY_MATERIAL',
            'unit_of_measure': 'KG',
            'hazard_level': 'MEDIUM',
        },
        {
            'item_name_ar': 'ملون أزرق',
            'item_name_en': 'Blue Colorant',
            'category': 'COLORANTS',
            'material_type': 'AUXILIARY_MATERIAL',
            'unit_of_measure': 'KG',
            'hazard_level': 'MEDIUM',
        },
        {
            'item_name_ar': 'مثبت حراري',
            'item_name_en': 'Heat Stabilizer',
            'category': 'STABILIZERS',
            'material_type': 'AUXILIARY_MATERIAL',
            'unit_of_measure': 'KG',
            'hazard_level': 'MEDIUM',
        },
        {
            'item_name_ar': 'زيت البارافين',
            'item_name_en': 'Paraffin Oil',
            'category': 'OILS',
            'material_type': 'RAW_MATERIAL',
            'unit_of_measure': 'LITER',
            'hazard_level': 'LOW',
        },
        {
            'item_name_ar': 'كربونات الكالسيوم',
            'item_name_en': 'Calcium Carbonate',
            'category': 'FILLERS',
            'material_type': 'RAW_MATERIAL',
            'unit_of_measure': 'KG',
            'hazard_level': 'LOW',
        },
    ]
    
    created_items = []
    for item_data in sample_items:
        item, created = ItemMaster.objects.get_or_create(
            item_name_ar=item_data['item_name_ar'],
            defaults=item_data
        )
        if created:
            print(f"✅ تم إنشاء الصنف: {item.item_name_ar}")
        else:
            print(f"📦 الصنف موجود: {item.item_name_ar}")
        created_items.append(item)
    
    return created_items

def create_bin_locations_for_warehouses():
    """إنشاء مواقع تخزين للمخازن"""
    warehouses = Warehouse.objects.all()
    
    for warehouse in warehouses:
        # إنشاء مواقع تخزين إذا لم تكن موجودة
        existing_bins = BinLocation.objects.filter(warehouse=warehouse).count()
        if existing_bins < 10:
            for i in range(1, 11):  # إنشاء 10 مواقع لكل مخزن
                bin_code = f"A{i:02d}"
                bin_location, created = BinLocation.objects.get_or_create(
                    warehouse=warehouse,
                    bin_code=bin_code,
                    defaults={
                        'aisle': 'A',
                        'rack': str(i),
                        'level': '1',
                        'capacity': random.randint(100, 1000),
                        'current_quantity': 0,
                        'status': 'AVAILABLE',
                        'is_active': True,
                    }
                )
                if created:
                    print(f"📍 تم إنشاء موقع تخزين: {warehouse.warehouse_name} - {bin_code}")

def add_stock_to_warehouses():
    """إضافة مخزون للمخازن"""
    items = ItemMaster.objects.filter(is_active=True)
    warehouses = Warehouse.objects.filter(is_active=True)
    
    if not items.exists():
        print("❌ لا توجد أصناف نشطة")
        return
    
    if not warehouses.exists():
        print("❌ لا توجد مخازن نشطة")
        return
    
    print(f"📦 إضافة مخزون لـ {warehouses.count()} مخازن...")
    
    for warehouse in warehouses:
        print(f"\n🏭 معالجة المخزن: {warehouse.warehouse_name}")
        
        # الحصول على مواقع التخزين
        bin_locations = BinLocation.objects.filter(warehouse=warehouse, is_active=True)
        if not bin_locations.exists():
            print(f"⚠️ لا توجد مواقع تخزين في {warehouse.warehouse_name}")
            continue
        
        # اختيار عدد عشوائي من الأصناف (3-8 أصناف لكل مخزن)
        num_items = random.randint(3, min(8, items.count()))
        selected_items = random.sample(list(items), num_items)
        
        for item in selected_items:
            # اختيار موقع تخزين عشوائي
            bin_location = random.choice(bin_locations)
            
            # التحقق من وجود رصيد مسبق
            existing_balance = StockBalance.objects.filter(
                item=item,
                warehouse=warehouse,
                bin_location=bin_location
            ).first()
            
            if existing_balance:
                print(f"   📋 الصنف {item.item_name_ar} موجود مسبقاً")
                continue
            
            # إنشاء رصيد جديد
            quantity = random.randint(10, 500)
            cost = Decimal(str(random.uniform(10.0, 1000.0)))
            
            stock_balance = StockBalance.objects.create(
                item=item,
                warehouse=warehouse,
                bin_location=bin_location,
                current_quantity=quantity,
                available_quantity=quantity,
                reserved_quantity=0,
                average_cost=cost,
                total_value=cost * quantity,
                batch_number=f"BATCH-{random.randint(1000, 9999)}",
            )
            
            # تحديث كمية الموقع
            bin_location.current_quantity += quantity
            bin_location.save()
            
            print(f"   ✅ تم إضافة {quantity} {item.get_unit_of_measure_display()} من {item.item_name_ar}")

def update_warehouse_occupancy():
    """تحديث نسب إشغال المخازن"""
    warehouses = Warehouse.objects.all()
    
    for warehouse in warehouses:
        # حساب إجمالي الكمية المستخدمة
        total_used = BinLocation.objects.filter(
            warehouse=warehouse
        ).aggregate(
            total=django.db.models.Sum('current_quantity')
        )['total'] or 0
        
        # تحديث الإشغال الحالي
        warehouse.current_occupancy = total_used
        warehouse.save()
        
        occupancy_percentage = warehouse.get_occupancy_percentage()
        print(f"📊 {warehouse.warehouse_name}: {occupancy_percentage:.1f}% إشغال")

def main():
    print("=" * 60)
    print("    إضافة أصناف حقيقية للمخازن")
    print("    Adding Real Items to Warehouses")
    print("=" * 60)
    print()
    
    try:
        # 1. إنشاء أصناف تجريبية
        print("1️⃣ إنشاء الأصناف...")
        items = create_sample_items()
        print(f"✅ تم إنشاء/التحقق من {len(items)} صنف")
        print()
        
        # 2. إنشاء مواقع تخزين
        print("2️⃣ إنشاء مواقع التخزين...")
        create_bin_locations_for_warehouses()
        print("✅ تم إنشاء مواقع التخزين")
        print()
        
        # 3. إضافة المخزون
        print("3️⃣ إضافة المخزون للمخازن...")
        add_stock_to_warehouses()
        print("✅ تم إضافة المخزون")
        print()
        
        # 4. تحديث نسب الإشغال
        print("4️⃣ تحديث نسب الإشغال...")
        update_warehouse_occupancy()
        print("✅ تم تحديث نسب الإشغال")
        print()
        
        print("🎉 تم إكمال العملية بنجاح!")
        print("يمكنك الآن زيارة صفحة المخازن لرؤية النتائج")
        
    except Exception as e:
        print(f"❌ حدث خطأ: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
