# Generated by Django 5.2.5 on 2025-08-25 13:25

import django.core.validators
import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0006_alter_alert_alert_type_stocktransfer'),
    ]

    operations = [
        migrations.CreateModel(
            name='StockTransferItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, validators=[django.core.validators.MinValueValidator(0.001)], verbose_name='الكمية المطلوبة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'صنف طلب النقل',
                'verbose_name_plural': 'أصناف طلبات النقل',
                'ordering': ['created_at'],
            },
        ),
        migrations.RemoveIndex(
            model_name='stocktransfer',
            name='inventory_s_item_id_92610e_idx',
        ),
        migrations.RemoveField(
            model_name='stocktransfer',
            name='destination_bin_location',
        ),
        migrations.RemoveField(
            model_name='stocktransfer',
            name='item',
        ),
        migrations.RemoveField(
            model_name='stocktransfer',
            name='quantity',
        ),
        migrations.RemoveField(
            model_name='stocktransfer',
            name='source_bin_location',
        ),
        migrations.AlterField(
            model_name='stocktransfer',
            name='destination_warehouse',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='incoming_transfers', to='inventory.warehouse', verbose_name='المخزن الهدف'),
        ),
        migrations.AddField(
            model_name='stocktransferitem',
            name='destination_bin_location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='incoming_transfer_items', to='inventory.binlocation', verbose_name='موقع التخزين الهدف'),
        ),
        migrations.AddField(
            model_name='stocktransferitem',
            name='item',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='transfer_items', to='inventory.itemmaster', verbose_name='الصنف'),
        ),
        migrations.AddField(
            model_name='stocktransferitem',
            name='source_bin_location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='outgoing_transfer_items', to='inventory.binlocation', verbose_name='موقع التخزين المصدر'),
        ),
        migrations.AddField(
            model_name='stocktransferitem',
            name='transfer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transfer_items', to='inventory.stocktransfer', verbose_name='طلب النقل'),
        ),
        migrations.AddIndex(
            model_name='stocktransferitem',
            index=models.Index(fields=['transfer'], name='inventory_s_transfe_33c76a_idx'),
        ),
        migrations.AddIndex(
            model_name='stocktransferitem',
            index=models.Index(fields=['item'], name='inventory_s_item_id_b8fe44_idx'),
        ),
        migrations.AddIndex(
            model_name='stocktransferitem',
            index=models.Index(fields=['created_at'], name='inventory_s_created_f960d7_idx'),
        ),
    ]
