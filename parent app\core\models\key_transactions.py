"""
KeyTransactions - المعاملات المالية
تسجيل جميع المعاملات المالية في النظام
"""

from django.db import models
from django.core.validators import MinValueValidator
import uuid


class KeyTransaction(models.Model):
    """
    نموذج المعاملات المالية الأساسي
    """
    
    TRANSACTION_TYPES = [
        ('PURCHASE', 'شراء'),
        ('SALE', 'بيع'),
        ('PAYMENT', 'دفع'),
        ('RECEIPT', 'استلام'),
        ('EXPENSE', 'مصروف'),
        ('INCOME', 'إيراد'),
        ('TRANSFER', 'تحويل'),
        ('ADJUSTMENT', 'تسوية'),
    ]
    
    TRANSACTION_STATUS = [
        ('PENDING', 'معلق'),
        ('APPROVED', 'معتمد'),
        ('COMPLETED', 'مكتمل'),
        ('CANCELLED', 'ملغي'),
        ('REJECTED', 'مرفوض'),
    ]
    
    CURRENCIES = [
        ('EGP', 'جنيه مصري'),
        ('USD', 'دولار أمريكي'),
        ('EUR', 'يورو'),
        ('SAR', 'ريال سعودي'),
        ('AED', 'درهم إماراتي'),
    ]
    
    # Primary key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Transaction Identification
    transaction_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='رقم المعاملة'
    )
    
    reference_number = models.CharField(
        max_length=100,
        verbose_name='الرقم المرجعي',
        blank=True,
        null=True
    )
    
    # Transaction Details
    transaction_type = models.CharField(
        max_length=20,
        choices=TRANSACTION_TYPES,
        verbose_name='نوع المعاملة'
    )
    
    amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='المبلغ'
    )
    
    currency = models.CharField(
        max_length=3,
        choices=CURRENCIES,
        default='EGP',
        verbose_name='العملة'
    )
    
    exchange_rate = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        default=1,
        verbose_name='سعر الصرف'
    )
    
    amount_in_base_currency = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        verbose_name='المبلغ بالعملة الأساسية'
    )
    
    # Related Entities
    company = models.ForeignKey(
        'core.KeyCompany',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='transactions',
        verbose_name='الشركة'
    )

    # Status and Approval
    status = models.CharField(
        max_length=20,
        choices=TRANSACTION_STATUS,
        default='PENDING',
        verbose_name='الحالة'
    )

    approved_by = models.ForeignKey(
        'core.KeyUser',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='approved_transactions',
        verbose_name='اعتمد بواسطة'
    )
    
    approved_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='تاريخ الاعتماد'
    )
    
    # Dates
    transaction_date = models.DateField(
        verbose_name='تاريخ المعاملة'
    )
    
    due_date = models.DateField(
        blank=True,
        null=True,
        verbose_name='تاريخ الاستحقاق'
    )
    
    # Additional Information
    description = models.TextField(
        verbose_name='الوصف',
        blank=True,
        null=True
    )
    
    notes = models.TextField(
        verbose_name='ملاحظات',
        blank=True,
        null=True
    )
    
    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ آخر تحديث'
    )
    
    created_by = models.ForeignKey(
        'core.KeyUser',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='created_transactions',
        verbose_name='أنشئ بواسطة'
    )
    
    class Meta:
        verbose_name = 'معاملة مالية'
        verbose_name_plural = 'المعاملات المالية'
        ordering = ['-transaction_date', '-created_at']
        indexes = [
            models.Index(fields=['transaction_number']),
            models.Index(fields=['transaction_type']),
            models.Index(fields=['status']),
            models.Index(fields=['transaction_date']),
            models.Index(fields=['company']),
        ]
    
    def __str__(self):
        return f"{self.transaction_number} - {self.get_transaction_type_display()}"
    
    def save(self, *args, **kwargs):
        # Calculate amount in base currency
        self.amount_in_base_currency = self.amount * self.exchange_rate
        super().save(*args, **kwargs)
