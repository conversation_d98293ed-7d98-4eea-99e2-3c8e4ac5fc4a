<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <PERSON><PERSON>erse</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --brand-red: #D62828;
            --brand-red-dark: #8B1116;
            --brand-red-light: #FCE8E8;
            --brand-gold: #C89A3C;
            --brand-gold-light: #F4D488;
            --brand-gold-dark: #8C6420;
            --ink: #1A1A1A;
            --slate: #4A4F57;
            --line: #E6E8ED;
            --canvas: #F7F8FB;
            --white: #FFFFFF;
            --accent-sand: #FFF3E0;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--brand-red) 0%, var(--brand-red-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23FFFFFF" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23FFFFFF" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="%23FFFFFF" opacity="0.15"/><circle cx="10" cy="60" r="0.5" fill="%23FFFFFF" opacity="0.15"/><circle cx="90" cy="40" r="0.5" fill="%23FFFFFF" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .login-container {
            background: var(--white);
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(26, 26, 26, 0.15);
            overflow: hidden;
            max-width: 1000px;
            width: 100%;
            border: 1px solid var(--line);
            position: relative;
            z-index: 1;
        }

        .login-form-section {
            padding: 60px 50px;
            background: var(--white);
        }

        .login-info-section {
            background: linear-gradient(135deg, var(--brand-gold-light) 0%, var(--brand-gold) 60%, var(--brand-gold-dark) 100%);
            color: var(--ink);
            padding: 60px 50px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
        }

        .login-info-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23FFFFFF" opacity="0.2"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            pointer-events: none;
        }
        
        .logo {
            max-width: 140px;
            margin-bottom: 30px;
            filter: drop-shadow(0 4px 8px rgba(26, 26, 26, 0.1));
        }

        .form-control {
            border: 2px solid var(--line);
            border-radius: 12px;
            padding: 16px 24px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: var(--white);
            color: var(--ink);
        }

        .form-control:focus {
            border-color: var(--brand-red);
            box-shadow: 0 0 0 0.2rem rgba(214, 40, 40, 0.15);
            background: var(--white);
            color: var(--ink);
        }

        .form-control::placeholder {
            color: var(--slate);
        }

        .btn-login {
            background: linear-gradient(135deg, var(--brand-red) 0%, var(--brand-red-dark) 100%);
            border: none;
            border-radius: 12px;
            padding: 16px 32px;
            font-size: 18px;
            font-weight: 600;
            color: var(--white);
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            background: linear-gradient(135deg, var(--brand-red-dark) 0%, #6B0D10 100%);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(214, 40, 40, 0.4);
            color: var(--white);
        }
        
        .form-floating label {
            color: var(--slate);
            font-weight: 500;
        }

        .form-floating .form-control:focus ~ label,
        .form-floating .form-control:not(:placeholder-shown) ~ label {
            color: var(--brand-red);
        }

        .alert {
            border: none;
            border-radius: 12px;
            margin-bottom: 24px;
            border-left: 4px solid var(--brand-gold);
        }

        .alert-danger {
            background: var(--brand-red-light);
            color: var(--brand-red-dark);
            border-left-color: var(--brand-red);
        }

        .company-info {
            position: relative;
            z-index: 1;
        }

        .company-info h2 {
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--ink);
            text-shadow: 0 2px 4px rgba(26, 26, 26, 0.1);
        }

        .company-info p {
            font-size: 18px;
            opacity: 0.9;
            line-height: 1.6;
            color: var(--ink);
        }

        .features {
            margin-top: 40px;
            position: relative;
            z-index: 1;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 18px;
            padding: 8px 0;
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            transform: translateX(5px);
        }

        .feature-item i {
            font-size: 22px;
            margin-left: 18px;
            color: var(--brand-red);
            background: var(--white);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(214, 40, 40, 0.2);
        }

        .login-header {
            text-align: center;
            margin-bottom: 50px;
        }

        .login-header h1 {
            color: var(--ink);
            font-weight: 700;
            margin-bottom: 12px;
            font-size: 2.5rem;
        }

        .login-header p {
            color: var(--slate);
            font-size: 18px;
            font-weight: 500;
        }
        
        .form-check-input:checked {
            background-color: var(--brand-red);
            border-color: var(--brand-red);
        }

        .form-check-input:focus {
            border-color: var(--brand-red);
            box-shadow: 0 0 0 0.2rem rgba(214, 40, 40, 0.15);
        }

        .form-check-label {
            color: var(--slate);
            font-weight: 500;
        }

        .text-decoration-none {
            color: var(--brand-gold-dark) !important;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .text-decoration-none:hover {
            color: var(--brand-red) !important;
        }

        @media (max-width: 768px) {
            .login-info-section {
                display: none;
            }

            .login-form-section {
                padding: 40px 30px;
            }

            .login-container {
                margin: 20px;
                border-radius: 20px;
            }

            .login-header h1 {
                font-size: 2rem;
            }
        }

        @media (max-width: 576px) {
            .login-form-section {
                padding: 30px 20px;
            }

            .btn-login {
                padding: 14px 28px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0">
                <!-- Login Form Section -->
                <div class="col-lg-6">
                    <div class="login-form-section">
                        <div class="login-header">
                            <h1>تسجيل الدخول</h1>
                            <p>مرحباً بك في نظام KamaVerse</p>
                        </div>
                        
                        <!-- Messages -->
                        {% if messages %}
                            {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {% if message.tags == 'error' %}
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                {% elif message.tags == 'success' %}
                                    <i class="fas fa-check-circle me-2"></i>
                                {% elif message.tags == 'warning' %}
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                {% else %}
                                    <i class="fas fa-info-circle me-2"></i>
                                {% endif %}
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                            {% endfor %}
                        {% endif %}
                        
                        <!-- Login Form -->
                        <form method="post" novalidate>
                            {% csrf_token %}
                            
                            <div class="form-floating mb-3">
                                {{ form.username }}
                                <label for="{{ form.username.id_for_label }}">
                                    <i class="fas fa-user me-2"></i>{{ form.username.label }}
                                </label>
                                {% if form.username.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.username.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="form-floating mb-3">
                                {{ form.password }}
                                <label for="{{ form.password.id_for_label }}">
                                    <i class="fas fa-lock me-2"></i>{{ form.password.label }}
                                </label>
                                {% if form.password.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.password.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="form-check mb-4">
                                {{ form.remember_me }}
                                <label class="form-check-label" for="{{ form.remember_me.id_for_label }}">
                                    {{ form.remember_me.label }}
                                </label>
                            </div>
                            
                            {% if form.non_field_errors %}
                                <div class="alert alert-danger">
                                    {% for error in form.non_field_errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            
                            <button type="submit" class="btn btn-login">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </form>
                        
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                هل تواجه مشكلة في الدخول؟ 
                                <a href="#" class="text-decoration-none">اتصل بالدعم الفني</a>
                            </small>
                        </div>
                    </div>
                </div>
                
                <!-- Company Info Section -->
                <div class="col-lg-6">
                    <div class="login-info-section">
                        <div class="company-info">
                            <img src="/static/images/logo.png" alt="Logo" class="logo">
                            <h2>شركة القماش للاستيراد والتصدير</h2>
                            <p>نظام إدارة متكامل لجميع عمليات الشركة من الاستيراد إلى البيع</p>
                        </div>
                        
                        <div class="features">
                            <div class="feature-item">
                                <i class="fas fa-ship"></i>
                                <span>إدارة عمليات الاستيراد</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-warehouse"></i>
                                <span>إدارة المخزون والمواد الخام</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-chart-line"></i>
                                <span>إدارة المبيعات والعملاء</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-coins"></i>
                                <span>إدارة الحسابات المالية</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-users"></i>
                                <span>إدارة علاقات العملاء</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-chart-bar"></i>
                                <span>التقارير والتحليلات</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Focus on username field when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const usernameField = document.getElementById('{{ form.username.id_for_label }}');
            if (usernameField) {
                usernameField.focus();
            }
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
