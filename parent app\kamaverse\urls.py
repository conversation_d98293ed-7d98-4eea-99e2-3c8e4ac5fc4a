"""
URL configuration for Ka<PERSON>V<PERSON> project.
إعدادات الروابط لنظام KamaVerse
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.shortcuts import redirect

def redirect_to_dashboard(request):
    """توجيه الصفحة الرئيسية إلى لوحة التحكم"""
    return redirect('authentication:dashboard')

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),

    # Root redirect
    path('', redirect_to_dashboard, name='home'),

    # Authentication
    path('auth/', include('modules.authentication.urls')),

    # Modules (will be added later)
    # path('import/', include('modules.import_module.urls')),
    # path('stock/', include('modules.stock_module.urls')),
    # path('finance/', include('modules.finance_module.urls')),
    # path('sales/', include('modules.sales_module.urls')),
    # path('crm/', include('modules.crm_module.urls')),
    # path('hr/', include('modules.hr_module.urls')),
    # path('logistics/', include('modules.logistics_module.urls')),
    # path('reports/', include('modules.reporting_module.urls')),
    # path('users/', include('modules.users_module.urls')),

    # Mobile Apps (will be added later)
    # path('kamachat/', include('mobile_apps.kamachat.urls')),
    # path('hawk/', include('mobile_apps.hawk.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
