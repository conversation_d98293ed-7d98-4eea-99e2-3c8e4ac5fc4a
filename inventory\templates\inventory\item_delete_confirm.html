{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
:root {
    --danger: #dc3545;
    --danger-light: #f8d7da;
    --danger-dark: #721c24;
    --warning: #ffc107;
    --warning-light: #fff3cd;
    --success: #28a745;
    --info: #17a2b8;
    --slate: #6c757d;
    --charcoal: #343a40;
    --canvas: #f8f9fa;
    --white: #ffffff;
    --line: #dee2e6;
}

.delete-container {
    max-width: 600px;
    margin: 2rem auto;
    background: var(--white);
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.delete-header {
    background: var(--danger);
    color: var(--white);
    padding: 2rem;
    text-align: center;
}

.delete-header h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.delete-header .icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.delete-content {
    padding: 2rem;
}

.item-info {
    background: var(--canvas);
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    border-left: 4px solid var(--danger);
}

.item-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--charcoal);
    margin-bottom: 0.5rem;
}

.item-code {
    color: var(--slate);
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.warning-box {
    background: var(--danger-light);
    border: 1px solid var(--danger);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.warning-title {
    color: var(--danger-dark);
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.warning-text {
    color: var(--danger-dark);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.related-data-list {
    list-style: none;
    padding: 0;
    margin: 1rem 0;
}

.related-data-list li {
    background: var(--white);
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    border-radius: 6px;
    border-left: 3px solid var(--danger);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding-top: 1rem;
    border-top: 1px solid var(--line);
}

.btn {
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
    font-size: 1rem;
}

.btn-cancel {
    background: var(--slate);
    color: var(--white);
}

.btn-cancel:hover {
    background: var(--charcoal);
    color: var(--white);
    transform: translateY(-1px);
}

.btn-delete {
    background: var(--danger);
    color: var(--white);
}

.btn-delete:hover {
    background: var(--danger-dark);
    transform: translateY(-1px);
}

.btn-delete:disabled {
    background: var(--slate);
    cursor: not-allowed;
    transform: none;
}

.no-related-data {
    background: var(--success);
    color: var(--white);
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    margin-bottom: 2rem;
}

@media (max-width: 768px) {
    .delete-container {
        margin: 1rem;
        border-radius: 8px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn {
        text-align: center;
        justify-content: center;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="delete-container">
    <div class="delete-header">
        <div class="icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <h1>تأكيد حذف الصنف</h1>
    </div>
    
    <div class="delete-content">
        <div class="item-info">
            <div class="item-name">{{ item.item_name_ar }}</div>
            <div class="item-code">{{ item.item_code }}</div>
            {% if item.item_name_en %}
                <div class="item-code">{{ item.item_name_en }}</div>
            {% endif %}
        </div>
        
        {% if related_data %}
            <div class="warning-box">
                <div class="warning-title">
                    <i class="fas fa-info-circle"></i>
                    معلومات مهمة
                </div>
                <div class="warning-text">
                    هذا الصنف مرتبط ببيانات أخرى في النظام. سيتم حذف الصنف مع جميع البيانات المرتبطة به:
                </div>
                <ul class="related-data-list">
                    {% for data in related_data %}
                        <li>
                            <i class="fas fa-link"></i>
                            {{ data }}
                        </li>
                    {% endfor %}
                </ul>
            </div>
            
            <!-- Stock Balance Information -->
            {% if stock_balances %}
            <div class="warning-box">
                <div class="warning-title">
                    <i class="fas fa-warehouse"></i>
                    أرصدة المخزون المرتبطة
                </div>
                <div class="warning-text">
                    سيتم حذف الأرصدة التالية من المخازن:
                </div>
                <ul class="related-data-list">
                    {% for balance in stock_balances %}
                    <li>
                        <i class="fas fa-box"></i>
                        {{ balance.warehouse.warehouse_name }} - {{ balance.bin_location.bin_code }}: 
                        {{ balance.current_quantity }} {{ item.get_unit_of_measure_display }}
                        {% if balance.batch_number %}
                            (دفعة: {{ balance.batch_number }})
                        {% endif %}
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
        {% else %}
            <div class="no-related-data">
                <i class="fas fa-check-circle"></i>
                يمكن حذف هذا الصنف بأمان - لا توجد بيانات مرتبطة
            </div>
        {% endif %}
        
        <div class="warning-box">
            <div class="warning-title">
                <i class="fas fa-exclamation-triangle"></i>
                تحذير مهم
            </div>
            <div class="warning-text">
                سيتم حذف الصنف نهائياً من النظام ولا يمكن التراجع عن هذا الإجراء. 
                تأكد من أنك تريد المتابعة.
            </div>
        </div>
        
        <div class="action-buttons">
            <a href="{% url 'inventory:item_detail' item.pk %}" class="btn btn-cancel">
                <i class="fas fa-arrow-right"></i>
                إلغاء
            </a>
            
            <form method="post" style="display: inline;">
                {% csrf_token %}
                <button type="submit" class="btn btn-delete" onclick="return confirm('هل أنت متأكد من حذف هذا الصنف نهائياً؟\n\nسيتم حذف الصنف مع جميع البيانات المرتبطة به من المخازن.')">
                    <i class="fas fa-trash"></i>
                    حذف نهائياً
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}