{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }} - KamaVerse{% endblock %}

{% block extra_css %}
<link href="{% static 'css/items.css' %}" rel="stylesheet">
<style>
.form-section {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(26, 26, 26, 0.08);
    border: 1px solid var(--line);
}

.form-section h3 {
    color: var(--brand-red);
    margin-bottom: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.form-section h3 i {
    color: var(--brand-gold);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: var(--charcoal);
    margin-bottom: 0.5rem;
    display: block;
}

.form-control, .form-select {
    border: 2px solid var(--line);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--brand-gold);
    box-shadow: 0 0 0 0.2rem rgba(200, 154, 60, 0.25);
}

.required {
    color: var(--brand-red);
}

.btn-save {
    background: var(--brand-red);
    color: var(--white);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-save:hover {
    background: var(--brand-red-dark);
    transform: translateY(-1px);
}

.btn-cancel {
    background: var(--line);
    color: var(--charcoal);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
    text-decoration: none;
    margin-left: 1rem;
}

.btn-cancel:hover {
    background: #e0e0e0;
    color: var(--charcoal);
    text-decoration: none;
}

.help-text {
    font-size: 0.875rem;
    color: #666;
    margin-top: 0.25rem;
}

.warehouse-info {
    background: linear-gradient(135deg, var(--brand-red) 0%, var(--brand-red-dark) 100%);
    color: var(--white);
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.warehouse-info h2 {
    margin: 0;
    font-weight: 700;
}

.warehouse-details {
    margin-top: 1rem;
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.warehouse-detail {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.warehouse-detail i {
    opacity: 0.8;
}

.form-actions {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--line);
}

.item-details-section {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 12px;
    padding: 1.5rem;
    display: none;
}

.item-details-section.show {
    display: block;
}

.item-details-section h4 {
    color: var(--brand-red);
    margin-bottom: 1rem;
    font-weight: 600;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #dee2e6;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: var(--charcoal);
}

.detail-value {
    color: #666;
}

.warehouse-quantity-section {
    background: #e3f2fd;
    border: 2px solid #2196f3;
    border-radius: 12px;
    padding: 1.5rem;
}

.warehouse-quantity-section h4 {
    color: var(--brand-red);
    margin-bottom: 1rem;
    font-weight: 600;
}

.quantity-info {
    background: #fff3cd;
    border: 1px solid #ffc107;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
}

.quantity-info p {
    margin: 0;
    color: #856404;
    font-weight: 500;
}
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="items-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-plus-circle me-3 gold-accent"></i>
                    إضافة صنف للمخزن
                </h1>
                <p class="mb-0 opacity-75">اختيار صنف موجود وإضافة كمية له في المخزن المحدد</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'inventory:warehouse_detail' warehouse.pk %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للمخزن
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Warehouse Information -->
    <div class="warehouse-info">
        <h2>
            <i class="fas fa-warehouse me-2"></i>
            {{ warehouse.warehouse_name }}
        </h2>
        <div class="warehouse-details">
            <div class="warehouse-detail">
                <i class="fas fa-barcode"></i>
                <span>{{ warehouse.warehouse_code }}</span>
            </div>
            <div class="warehouse-detail">
                <i class="fas fa-map-marker-alt"></i>
                <span>{{ warehouse.address }}</span>
            </div>
            <div class="warehouse-detail">
                <i class="fas fa-user-tie"></i>
                <span>{{ warehouse.warehouse_manager|default:"غير محدد" }}</span>
            </div>
        </div>
    </div>

    <!-- Add Item Form -->
    <form method="post" class="item-form">
        {% csrf_token %}
        {{ form.warehouse_id }}
        
        <!-- Item Selection Section -->
        <div class="form-section">
            <h3>
                <i class="fas fa-search"></i>
                اختيار الصنف
            </h3>
            
            <div class="form-group">
                <label for="{{ form.selected_item.id_for_label }}" class="form-label">
                    {{ form.selected_item.label }} <span class="required">*</span>
                </label>
                {{ form.selected_item }}
                {% if form.selected_item.errors %}
                    {% for error in form.selected_item.errors %}
                        <div class="text-danger small">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <div class="help-text">{{ form.selected_item.help_text }}</div>
            </div>
        </div>

        <!-- Item Details Section (Auto-filled) -->
        <div class="form-section item-details-section" id="item-details-section">
            <h4>
                <i class="fas fa-info-circle me-2"></i>
                تفاصيل الصنف المختار
            </h4>
            
            <div class="detail-row">
                <span class="detail-label">كود الصنف:</span>
                <span class="detail-value" id="detail-item-code">-</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">اسم الصنف بالعربية:</span>
                <span class="detail-value" id="detail-item-name-ar">-</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">اسم الصنف بالإنجليزية:</span>
                <span class="detail-value" id="detail-item-name-en">-</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">الصيغة الكيميائية:</span>
                <span class="detail-value" id="detail-chemical-formula">-</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">مجموعة المواد:</span>
                <span class="detail-value" id="detail-category">-</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">نوع المادة:</span>
                <span class="detail-value" id="detail-material-type">-</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">مستوى الخطورة:</span>
                <span class="detail-value" id="detail-hazard-level">-</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">وحدة القياس:</span>
                <span class="detail-value" id="detail-unit-of-measure">-</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">أدنى مستوى مخزون:</span>
                <span class="detail-value" id="detail-minimum-stock">-</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">نقطة إعادة الطلب:</span>
                <span class="detail-value" id="detail-reorder-point">-</span>
            </div>
            
            <div class="detail-row" id="shelf-life-row" style="display: none;">
                <span class="detail-label">مدة الصلاحية:</span>
                <span class="detail-value" id="detail-shelf-life">-</span>
            </div>
            
            <div class="detail-row" id="notes-row" style="display: none;">
                <span class="detail-label">ملاحظات:</span>
                <span class="detail-value" id="detail-notes">-</span>
            </div>
        </div>

        <!-- Warehouse Quantity Section -->
        <div class="form-section warehouse-quantity-section">
            <h4>
                <i class="fas fa-warehouse me-2"></i>
                الكمية في المخزن
            </h4>
            
            <div class="form-group">
                <label for="{{ form.initial_quantity.id_for_label }}" class="form-label">
                    {{ form.initial_quantity.label }} <span class="required">*</span>
                </label>
                {{ form.initial_quantity }}
                {% if form.initial_quantity.errors %}
                    {% for error in form.initial_quantity.errors %}
                        <div class="text-danger small">{{ error }}</div>
                    {% endfor %}
                {% endif %}
                <div class="help-text">{{ form.initial_quantity.help_text }}</div>
            </div>

            <div class="quantity-info">
                <p>
                    <i class="fas fa-info-circle me-2"></i>
                    ستتم إضافة الكمية المحددة للصنف في هذا المخزن. 
                    إذا كان الصنف موجود بالفعل، ستضاف الكمية الجديدة للكمية الحالية.
                </p>
            </div>
        </div>
        
        <!-- Form Actions -->
        <div class="form-actions">
            <a href="{% url 'inventory:warehouse_detail' warehouse.pk %}" class="btn-cancel">
                <i class="fas fa-times me-2"></i>
                إلغاء
            </a>
            <button type="submit" class="btn-save">
                <i class="fas fa-plus me-2"></i>
                إضافة الصنف للمخزن
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
// بيانات الأصناف من الخادم
const itemsData = {{ items_data_json|safe }};

document.addEventListener('DOMContentLoaded', function() {
    const itemSelect = document.getElementById('{{ form.selected_item.id_for_label }}');
    const itemDetailsSection = document.getElementById('item-details-section');
    
    // عند تغيير اختيار الصنف
    itemSelect.addEventListener('change', function() {
        const selectedItemId = this.value;
        
        if (selectedItemId) {
            // البحث عن بيانات الصنف المختار
            const selectedItem = itemsData.find(item => item.id === selectedItemId);
            
            if (selectedItem) {
                // ملء بيانات الصنف
                fillItemDetails(selectedItem);
                // إظهار قسم تفاصيل الصنف
                itemDetailsSection.classList.add('show');
                // الانتقال إلى قسم الكمية
                document.querySelector('.warehouse-quantity-section').scrollIntoView({ 
                    behavior: 'smooth', 
                    block: 'start' 
                });
            }
        } else {
            // إخفاء قسم تفاصيل الصنف
            itemDetailsSection.classList.remove('show');
        }
    });
    
    // وظيفة ملء تفاصيل الصنف
    function fillItemDetails(item) {
        document.getElementById('detail-item-code').textContent = item.item_code || '-';
        document.getElementById('detail-item-name-ar').textContent = item.item_name_ar || '-';
        document.getElementById('detail-item-name-en').textContent = item.item_name_en || '-';
        document.getElementById('detail-chemical-formula').textContent = item.chemical_formula || '-';
        document.getElementById('detail-category').textContent = item.category || '-';
        document.getElementById('detail-material-type').textContent = item.material_type || '-';
        document.getElementById('detail-hazard-level').textContent = item.hazard_level || '-';
        document.getElementById('detail-unit-of-measure').textContent = item.unit_of_measure || '-';
        document.getElementById('detail-minimum-stock').textContent = item.minimum_stock_level || '-';
        document.getElementById('detail-reorder-point').textContent = item.reorder_point || '-';
        
        // إظهار/إخفاء مدة الصلاحية
        const shelfLifeRow = document.getElementById('shelf-life-row');
        const shelfLifeElement = document.getElementById('detail-shelf-life');
        if (item.shelf_life_months && item.shelf_life_months > 0) {
            shelfLifeElement.textContent = item.shelf_life_months + ' شهر';
            shelfLifeRow.style.display = 'flex';
        } else {
            shelfLifeRow.style.display = 'none';
        }
        
        // إظهار/إخفاء الملاحظات
        const notesRow = document.getElementById('notes-row');
        const notesElement = document.getElementById('detail-notes');
        if (item.notes && item.notes.trim()) {
            notesElement.textContent = item.notes;
            notesRow.style.display = 'flex';
        } else {
            notesRow.style.display = 'none';
        }
    }
    
    // التحقق من النموذج قبل الإرسال
    const form = document.querySelector('.item-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const selectedItem = itemSelect.value;
            const quantity = document.getElementById('{{ form.initial_quantity.id_for_label }}').value;
            
            if (!selectedItem) {
                e.preventDefault();
                alert('يرجى اختيار صنف');
                itemSelect.focus();
                return;
            }
            
            if (!quantity || parseFloat(quantity) <= 0) {
                e.preventDefault();
                alert('يرجى إدخال كمية صحيحة أكبر من صفر');
                document.getElementById('{{ form.initial_quantity.id_for_label }}').focus();
                return;
            }
            
            // إظهار رسالة تحميل
            const submitBtn = document.querySelector('.btn-save');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإضافة...';
            submitBtn.disabled = true;
            
            // إعادة تفعيل الزر في حالة الخطأ
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 10000);
        });
    }
    
    // تحسين تجربة المستخدم - التركيز على حقل الاختيار
    if (itemSelect) {
        itemSelect.focus();
    }
});
</script>
{% endblock %}