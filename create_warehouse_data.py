#!/usr/bin/env python
"""
إنشاء بيانات تجريبية للمخازن ومواقع التخزين
Create sample data for warehouses and bin locations
"""

import os
import sys
import django
from decimal import Decimal

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

from inventory.models import Warehouse, BinLocation

def create_warehouses():
    """إنشاء المخازن الأربعة الأساسية"""
    
    warehouses_data = [
        {
            'warehouse_code': 'ALX-001',
            'warehouse_name': 'مخزن الإسكندرية',
            'warehouse_type': 'MAIN',
            'total_capacity': Decimal('4000.00'),
            'current_occupancy': Decimal('2800.00'),
            'address': 'المنطقة الصناعية، برج العرب الجديدة',
            'city': 'الإسكندرية',
            'governorate': 'الإسكندرية',
            'warehouse_manager': 'أحمد محمد علي',
            'manager_phone': '01234567890',
            'is_active': True,
            'temperature_controlled': True,
            'humidity_controlled': True,
        },
        {
            'warehouse_code': 'RNG-002',
            'warehouse_name': 'مخزن الدائري',
            'warehouse_type': 'MAIN',
            'total_capacity': Decimal('3500.00'),
            'current_occupancy': Decimal('2100.00'),
            'address': 'الطريق الدائري الأوسطي، المنطقة الصناعية',
            'city': 'القاهرة',
            'governorate': 'القاهرة',
            'warehouse_manager': 'فاطمة أحمد حسن',
            'manager_phone': '01234567891',
            'is_active': True,
            'temperature_controlled': True,
            'humidity_controlled': False,
        },
        {
            'warehouse_code': 'CAI-003',
            'warehouse_name': 'مخزن القاهرة',
            'warehouse_type': 'MAIN',
            'total_capacity': Decimal('3000.00'),
            'current_occupancy': Decimal('1950.00'),
            'address': 'المنطقة الصناعية الأولى، حلوان',
            'city': 'القاهرة',
            'governorate': 'القاهرة',
            'warehouse_manager': 'محمد حسن علي',
            'manager_phone': '01234567892',
            'is_active': True,
            'temperature_controlled': True,
            'humidity_controlled': True,
        },
        {
            'warehouse_code': 'MNF-004',
            'warehouse_name': 'مخزن المنوفية',
            'warehouse_type': 'MAIN',
            'total_capacity': Decimal('2500.00'),
            'current_occupancy': Decimal('1200.00'),
            'address': 'المنطقة الصناعية، مدينة السادات',
            'city': 'السادات',
            'governorate': 'المنوفية',
            'warehouse_manager': 'سارة محمود أحمد',
            'manager_phone': '01234567893',
            'is_active': True,
            'temperature_controlled': False,
            'humidity_controlled': False,
        }
    ]
    
    created_warehouses = []
    
    for warehouse_data in warehouses_data:
        warehouse, created = Warehouse.objects.get_or_create(
            warehouse_code=warehouse_data['warehouse_code'],
            defaults=warehouse_data
        )
        
        if created:
            print(f"✅ تم إنشاء المخزن: {warehouse.warehouse_name}")
        else:
            print(f"📦 المخزن موجود بالفعل: {warehouse.warehouse_name}")
            
        created_warehouses.append(warehouse)
    
    return created_warehouses

def create_bin_locations(warehouses):
    """إنشاء مواقع التخزين لكل مخزن"""
    
    bin_locations_data = {
        'ALX-001': [  # مخزن الإسكندرية
            {'bin_code': 'ALX-A-01', 'capacity': 150, 'current_quantity': 120, 'status': 'AVAILABLE'},
            {'bin_code': 'ALX-A-02', 'capacity': 150, 'current_quantity': 150, 'status': 'AVAILABLE'},
            {'bin_code': 'ALX-A-03', 'capacity': 150, 'current_quantity': 80, 'status': 'AVAILABLE'},
            {'bin_code': 'ALX-B-01', 'capacity': 200, 'current_quantity': 180, 'status': 'AVAILABLE'},
            {'bin_code': 'ALX-B-02', 'capacity': 200, 'current_quantity': 100, 'status': 'AVAILABLE'},
            {'bin_code': 'ALX-B-03', 'capacity': 200, 'current_quantity': 0, 'status': 'AVAILABLE'},
            {'bin_code': 'ALX-C-01', 'capacity': 120, 'current_quantity': 90, 'status': 'AVAILABLE'},
            {'bin_code': 'ALX-C-02', 'capacity': 120, 'current_quantity': 0, 'status': 'BLOCKED'},
        ],
        'RNG-002': [  # مخزن الدائري
            {'bin_code': 'RNG-A-01', 'capacity': 180, 'current_quantity': 150, 'status': 'AVAILABLE'},
            {'bin_code': 'RNG-A-02', 'capacity': 180, 'current_quantity': 180, 'status': 'AVAILABLE'},
            {'bin_code': 'RNG-B-01', 'capacity': 160, 'current_quantity': 120, 'status': 'AVAILABLE'},
            {'bin_code': 'RNG-B-02', 'capacity': 160, 'current_quantity': 80, 'status': 'AVAILABLE'},
            {'bin_code': 'RNG-B-03', 'capacity': 160, 'current_quantity': 0, 'status': 'AVAILABLE'},
            {'bin_code': 'RNG-C-01', 'capacity': 140, 'current_quantity': 140, 'status': 'AVAILABLE'},
            {'bin_code': 'RNG-C-02', 'capacity': 140, 'current_quantity': 70, 'status': 'AVAILABLE'},
        ],
        'CAI-003': [  # مخزن القاهرة
            {'bin_code': 'CAI-A-01', 'capacity': 130, 'current_quantity': 100, 'status': 'AVAILABLE'},
            {'bin_code': 'CAI-A-02', 'capacity': 130, 'current_quantity': 130, 'status': 'AVAILABLE'},
            {'bin_code': 'CAI-A-03', 'capacity': 130, 'current_quantity': 65, 'status': 'AVAILABLE'},
            {'bin_code': 'CAI-B-01', 'capacity': 170, 'current_quantity': 150, 'status': 'AVAILABLE'},
            {'bin_code': 'CAI-B-02', 'capacity': 170, 'current_quantity': 85, 'status': 'AVAILABLE'},
            {'bin_code': 'CAI-C-01', 'capacity': 110, 'current_quantity': 110, 'status': 'AVAILABLE'},
            {'bin_code': 'CAI-C-02', 'capacity': 110, 'current_quantity': 0, 'status': 'AVAILABLE'},
            {'bin_code': 'CAI-C-03', 'capacity': 110, 'current_quantity': 50, 'status': 'BLOCKED'},
        ],
        'MNF-004': [  # مخزن المنوفية
            {'bin_code': 'MNF-A-01', 'capacity': 120, 'current_quantity': 90, 'status': 'AVAILABLE'},
            {'bin_code': 'MNF-A-02', 'capacity': 120, 'current_quantity': 60, 'status': 'AVAILABLE'},
            {'bin_code': 'MNF-A-03', 'capacity': 120, 'current_quantity': 0, 'status': 'AVAILABLE'},
            {'bin_code': 'MNF-B-01', 'capacity': 100, 'current_quantity': 100, 'status': 'AVAILABLE'},
            {'bin_code': 'MNF-B-02', 'capacity': 100, 'current_quantity': 50, 'status': 'AVAILABLE'},
            {'bin_code': 'MNF-C-01', 'capacity': 80, 'current_quantity': 80, 'status': 'AVAILABLE'},
            {'bin_code': 'MNF-C-02', 'capacity': 80, 'current_quantity': 40, 'status': 'AVAILABLE'},
            {'bin_code': 'MNF-C-03', 'capacity': 80, 'current_quantity': 0, 'status': 'BLOCKED'},
        ]
    }
    
    for warehouse in warehouses:
        if warehouse.warehouse_code in bin_locations_data:
            locations_data = bin_locations_data[warehouse.warehouse_code]
            
            for location_data in locations_data:
                # استخراج معلومات الموقع من الكود
                bin_code = location_data['bin_code']
                parts = bin_code.split('-')
                aisle = parts[0] if len(parts) > 0 else 'A'
                rack = parts[1] if len(parts) > 1 else '01'
                level = parts[2] if len(parts) > 2 else '01'

                bin_location, created = BinLocation.objects.get_or_create(
                    warehouse=warehouse,
                    bin_code=location_data['bin_code'],
                    defaults={
                        'bin_name': f'موقع {location_data["bin_code"]}',
                        'aisle': aisle,
                        'rack': rack,
                        'level': level,
                        'capacity': Decimal(str(location_data['capacity'])),
                        'current_quantity': Decimal(str(location_data['current_quantity'])),
                        'status': location_data['status'],
                        'notes': f'موقع تخزين {location_data["bin_code"]} في {warehouse.warehouse_name}',
                        'is_active': True,
                    }
                )
                
                if created:
                    print(f"  ✅ تم إنشاء موقع التخزين: {bin_location.bin_code}")
                else:
                    print(f"  📍 موقع التخزين موجود: {bin_location.bin_code}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء إنشاء البيانات التجريبية للمخازن...")
    print("=" * 50)
    
    # إنشاء المخازن
    print("\n📦 إنشاء المخازن...")
    warehouses = create_warehouses()
    
    # إنشاء مواقع التخزين
    print("\n📍 إنشاء مواقع التخزين...")
    create_bin_locations(warehouses)
    
    print("\n" + "=" * 50)
    print("✅ تم إنشاء جميع البيانات التجريبية بنجاح!")
    
    # عرض الإحصائيات النهائية
    print(f"\n📊 الإحصائيات النهائية:")
    print(f"   - إجمالي المخازن: {Warehouse.objects.count()}")
    print(f"   - إجمالي مواقع التخزين: {BinLocation.objects.count()}")
    
    print("\n🎉 يمكنك الآن زيارة صفحة إدارة المخازن!")
    print("   http://127.0.0.1:8000/warehouses/")

if __name__ == '__main__':
    main()
