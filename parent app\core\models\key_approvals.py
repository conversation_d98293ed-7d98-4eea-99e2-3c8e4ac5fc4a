"""
KeyApprovals - نظام الموافقات
إدارة جميع الموافقات المطلوبة في النظام
"""

from django.db import models
import uuid


class KeyApproval(models.Model):
    """
    نموذج الموافقات الأساسي
    """
    
    APPROVAL_TYPES = [
        ('PURCHASE', 'موافقة شراء'),
        ('SALE', 'موافقة بيع'),
        ('PAYMENT', 'موافقة دفع'),
        ('EXPENSE', 'موافقة مصروف'),
        ('LEAVE', 'موافقة إجازة'),
        ('OVERTIME', 'موافقة إضافي'),
        ('DISCOUNT', 'موافقة خصم'),
        ('CREDIT', 'موافقة ائتمان'),
        ('OTHER', 'أخرى'),
    ]
    
    APPROVAL_STATUS = [
        ('PENDING', 'معلق'),
        ('APPROVED', 'معتمد'),
        ('REJECTED', 'مرفوض'),
        ('CANCELLED', 'ملغي'),
    ]
    
    PRIORITY_LEVELS = [
        ('LOW', 'منخفض'),
        ('NORMAL', 'عادي'),
        ('HIGH', 'عالي'),
        ('URGENT', 'عاجل'),
    ]
    
    # Primary key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Approval Information
    approval_type = models.CharField(
        max_length=20,
        choices=APPROVAL_TYPES,
        verbose_name='نوع الموافقة'
    )
    
    title = models.CharField(
        max_length=200,
        verbose_name='العنوان'
    )
    
    description = models.TextField(
        verbose_name='الوصف'
    )
    
    amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name='المبلغ'
    )
    
    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_LEVELS,
        default='NORMAL',
        verbose_name='الأولوية'
    )
    
    # Users
    requester = models.ForeignKey(
        'core.KeyUser',
        on_delete=models.CASCADE,
        related_name='requested_approvals',
        verbose_name='الطالب'
    )

    approver = models.ForeignKey(
        'core.KeyUser',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='assigned_approvals',
        verbose_name='المعتمد'
    )
    
    # Status
    status = models.CharField(
        max_length=20,
        choices=APPROVAL_STATUS,
        default='PENDING',
        verbose_name='الحالة'
    )
    
    # Dates
    requested_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الطلب'
    )
    
    approved_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='تاريخ الموافقة'
    )
    
    due_date = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='تاريخ الاستحقاق'
    )
    
    # Response
    response_notes = models.TextField(
        verbose_name='ملاحظات الرد',
        blank=True,
        null=True
    )
    
    class Meta:
        verbose_name = 'موافقة'
        verbose_name_plural = 'الموافقات'
        ordering = ['-requested_at']
        indexes = [
            models.Index(fields=['approval_type']),
            models.Index(fields=['status']),
            models.Index(fields=['requester']),
            models.Index(fields=['approver']),
            models.Index(fields=['priority']),
        ]
    
    def __str__(self):
        return f"{self.title} - {self.get_status_display()}"
