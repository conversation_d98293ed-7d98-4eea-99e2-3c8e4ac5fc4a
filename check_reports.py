#!/usr/bin/env python
"""
Focused test for reports issue
"""
import os
import django
import sys

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

def check_reports_status():
    print("🔍 FOCUSED REPORTS CHECK")
    print("=" * 30)
    
    # Check 1: Current user profiles  
    from django.contrib.auth.models import User
    from inventory.models import UserProfile
    from inventory.views import get_user_warehouse
    
    print("👥 Users & Profiles:")
    users = ['admin', 'anas']
    for username in users:
        try:
            user = User.objects.get(username=username)
            warehouse = get_user_warehouse(user)
            print(f"  {username}: {warehouse}")
        except User.DoesNotExist:
            print(f"  {username}: NOT FOUND")
        except Exception as e:
            print(f"  {username}: ERROR - {str(e)}")
    
    # Check 2: Test actual HTTP request
    print("\n🌐 HTTP Test:")
    from django.test import Client
    
    client = Client()
    
    # Test with admin
    try:
        admin = User.objects.get(username='admin')
        client.force_login(admin)
        
        print("  Testing /reports/ with admin...")
        response = client.get('/reports/')
        print(f"    Status: {response.status_code}")
        
        if response.status_code == 302:
            location = response.get('Location', 'No location header')
            print(f"    🚨 REDIRECT TO: {location}")
            
            # Check if it's a dashboard redirect
            if '/dashboard/' in location or location.endswith('/'):
                print("    ❌ REDIRECTED TO DASHBOARD - USER PROFILE ISSUE")
            elif '/login/' in location:
                print("    ❌ REDIRECTED TO LOGIN - AUTH ISSUE")
            else:
                print(f"    ❌ REDIRECTED TO: {location}")
                
        elif response.status_code == 200:
            print("    ✅ SUCCESS - Reports page loaded")
            # Check content
            content = response.content.decode('utf-8')
            if 'التقارير الأساسية' in content:
                print("    ✅ Correct content loaded")
            else:
                print("    ⚠️  Wrong content - not reports page")
        else:
            print(f"    ⚠️  Unexpected status: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ Test failed: {str(e)}")
    
    # Check 3: URLs
    print("\n🔗 URL Check:")
    try:
        from django.urls import reverse
        url = reverse('inventory:reports_main_dashboard')
        print(f"  Reports URL: {url}")
    except Exception as e:
        print(f"  ❌ URL Error: {str(e)}")
    
    # Check 4: Template
    print("\n📄 Template Check:")
    template_path = "templates/inventory/reports/main_dashboard.html"
    if os.path.exists(template_path):
        print("  ✅ Template exists")
        with open(template_path, 'r') as f:
            first_line = f.readline().strip()
            if 'extends "base.html"' in first_line:
                print("  ✅ Correct extends")
            else:
                print(f"  ❌ Wrong extends: {first_line}")
    else:
        print("  ❌ Template missing")

if __name__ == "__main__":
    check_reports_status()