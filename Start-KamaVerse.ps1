# KamaVerse PowerShell Launcher
# نظام إدارة المخزون - شركة القماش

Write-Host "========================================" -ForegroundColor Green
Write-Host "    KamaVerse Inventory System" -ForegroundColor Green
Write-Host "    نظام إدارة المخزون - شركة القماش" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Clear virtual environment
$env:VIRTUAL_ENV = $null
$env:CONDA_DEFAULT_ENV = $null

# Step 1: Check Python
Write-Host "[1/6] Checking Python installation..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ Python not found. Please install Python from https://python.org" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} catch {
    Write-Host "❌ Python not found. Please install Python from https://python.org" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 2: Install Django
Write-Host ""
Write-Host "[2/6] Installing Django..." -ForegroundColor Yellow
try {
    pip install django --quiet --disable-pip-version-check
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Django installed successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Failed to install Django" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} catch {
    Write-Host "❌ Failed to install Django" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 3: Check Django installation
Write-Host ""
Write-Host "[3/6] Verifying Django..." -ForegroundColor Yellow
try {
    $djangoVersion = python -c "import django; print(f'Django {django.get_version()}')" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ $djangoVersion" -ForegroundColor Green
    } else {
        Write-Host "❌ Django verification failed" -ForegroundColor Red
        Write-Host "Error: $djangoVersion" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} catch {
    Write-Host "❌ Django verification failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 4: Run migrations
Write-Host ""
Write-Host "[4/6] Setting up database..." -ForegroundColor Yellow
try {
    python manage.py migrate --verbosity=1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Database setup completed" -ForegroundColor Green
    } else {
        Write-Host "❌ Database setup failed" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} catch {
    Write-Host "❌ Database setup failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Step 5: Create superuser
Write-Host ""
Write-Host "[5/6] Creating admin user..." -ForegroundColor Yellow
try {
    $createUserScript = @"
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('✅ Admin user created')
else:
    print('✅ Admin user already exists')
"@
    
    $result = python -c $createUserScript 2>&1
    Write-Host $result -ForegroundColor Green
} catch {
    Write-Host "⚠️ Could not create admin user" -ForegroundColor Yellow
}

# Step 6: Start server
Write-Host ""
Write-Host "[6/6] Starting Django server..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🚀 KamaVerse is starting..." -ForegroundColor Cyan
Write-Host "🌐 Server URL: http://127.0.0.1:8000" -ForegroundColor Cyan
Write-Host "👤 Admin Panel: http://127.0.0.1:8000/admin" -ForegroundColor Cyan
Write-Host "🔑 Login: admin / admin123" -ForegroundColor Cyan
Write-Host "🛑 Press Ctrl+C to stop the server" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Open browser
Start-Process "http://127.0.0.1:8000"

# Start Django server
try {
    python manage.py runserver 127.0.0.1:8000
} catch {
    Write-Host "❌ Server failed to start" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "Server stopped." -ForegroundColor Yellow
Read-Host "Press Enter to exit"
