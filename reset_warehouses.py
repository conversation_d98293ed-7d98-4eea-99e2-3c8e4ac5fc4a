#!/usr/bin/env python
"""
إعادة تعيين بيانات المخازن
Reset Warehouses Data - Keep only 4 warehouses
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

from inventory.models import Warehouse, BinLocation, StockBalance, StockMovement

def reset_warehouses():
    """حذف جميع البيانات القديمة وإعادة إنشاء المخازن الأربعة"""
    
    print("🗑️ حذف البيانات القديمة...")
    
    # حذف جميع البيانات المرتبطة بالمخازن
    StockMovement.objects.all().delete()
    StockBalance.objects.all().delete()
    BinLocation.objects.all().delete()
    Warehouse.objects.all().delete()
    
    print("✅ تم حذف جميع البيانات القديمة")
    
    print("\n🏗️ إنشاء المخازن الجديدة...")
    
    # استيراد وتشغيل ملف إنشاء البيانات
    with open('create_warehouse_data.py', 'r', encoding='utf-8') as f:
        exec(f.read())

def main():
    """الدالة الرئيسية"""
    print("🔄 إعادة تعيين بيانات المخازن...")
    print("=" * 50)
    
    # تأكيد من المستخدم
    confirm = input("هل أنت متأكد من حذف جميع بيانات المخازن وإعادة إنشائها؟ (y/N): ")
    
    if confirm.lower() in ['y', 'yes', 'نعم']:
        reset_warehouses()
        print("\n🎉 تم إعادة تعيين بيانات المخازن بنجاح!")
        print("المخازن الجديدة:")
        print("- مخزن الإسكندرية (ALX-001)")
        print("- مخزن الدائري (RNG-002)")
        print("- مخزن القاهرة (CAI-003)")
        print("- مخزن المنوفية (MNF-004)")
    else:
        print("❌ تم إلغاء العملية")

if __name__ == '__main__':
    main()
