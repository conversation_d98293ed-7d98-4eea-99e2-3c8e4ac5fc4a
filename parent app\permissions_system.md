# نظام الصلاحيات المتقدم - مشروع KamaVerse

## نظرة عامة

نظام الصلاحيات في KamaVerse مصمم ليكون مرناً ومعقداً بما يكفي لتلبية احتياجات شركة القماش المتنوعة. يدعم النظام مستويات متعددة من الصلاحيات والتحكم الدقيق في الوصول.

---

## مستويات المستخدمين

### 1. Super Admin (المدير الأعلى)
- **الوصف:** رئيس مجلس الإدارة
- **الصلاحيات:** وصول كامل لجميع الموديولات والبيانات
- **المميزات الخاصة:**
  - الوصول لتطبيق Hawk
  - إنشاء وحذف المستخدمين
  - تعديل جميع الصلاحيات
  - الموافقة على جميع المعاملات
  - عرض جميع التقارير المالية

### 2. Admin (المدير)
- **الوصف:** مديري الأقسام الرئيسية
- **الصلاحيات:** وصول كامل للموديولات المخصصة لهم
- **المميزات الخاصة:**
  - إنشاء مستخدمين في قسمهم
  - الموافقة على المعاملات ضمن حدود مالية
  - عرض تقارير القسم
  - إدارة فريق العمل

### 3. Manager (رئيس القسم)
- **الوصف:** رؤساء الأقسام الفرعية
- **الصلاحيات:** وصول محدود للموديولات
- **المميزات الخاصة:**
  - تعديل بيانات القسم
  - الموافقة على العمليات البسيطة
  - عرض تقارير محدودة
  - إدارة المهام

### 4. Employee (الموظف)
- **الوصف:** الموظفين العاديين
- **الصلاحيات:** وصول محدود حسب الوظيفة
- **المميزات الخاصة:**
  - إدخال البيانات
  - عرض البيانات المخصصة لهم
  - طلب الموافقات
  - استخدام Kamachat

### 5. Viewer (المطلع)
- **الوصف:** مستخدمين للعرض فقط
- **الصلاحيات:** عرض البيانات فقط
- **المميزات الخاصة:**
  - عرض التقارير المحددة
  - لا يمكن التعديل أو الحذف

---

## أنواع الصلاحيات

### الصلاحيات الأساسية (CRUD)
```python
BASIC_PERMISSIONS = {
    'VIEW': 'عرض',
    'ADD': 'إضافة', 
    'EDIT': 'تعديل',
    'DELETE': 'حذف'
}
```

### الصلاحيات المتقدمة
```python
ADVANCED_PERMISSIONS = {
    'APPROVE': 'موافقة',
    'REJECT': 'رفض',
    'EXPORT': 'تصدير',
    'IMPORT': 'استيراد',
    'PRINT': 'طباعة',
    'ARCHIVE': 'أرشفة'
}
```

### الصلاحيات المالية
```python
FINANCIAL_PERMISSIONS = {
    'VIEW_PRICES': 'عرض الأسعار',
    'EDIT_PRICES': 'تعديل الأسعار',
    'APPROVE_PURCHASE': 'الموافقة على الشراء',
    'APPROVE_SALE': 'الموافقة على البيع',
    'VIEW_PROFIT': 'عرض الأرباح',
    'FINANCIAL_REPORTS': 'التقارير المالية'
}
```

---

## مصفوفة الصلاحيات حسب الموديول

### موديول الاستيراد (Import Module)
| المستوى | عرض | إضافة | تعديل | حذف | موافقة | تصدير |
|---------|------|-------|-------|------|--------|--------|
| Super Admin | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Admin | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Manager | ✅ | ✅ | ✅ | ❌ | محدود | ✅ |
| Employee | ✅ | ✅ | محدود | ❌ | ❌ | ❌ |
| Viewer | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |

### موديول المخزون (Stock Module)
| المستوى | عرض | إضافة | تعديل | حذف | تنبيهات | تقارير |
|---------|------|-------|-------|------|---------|---------|
| Super Admin | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Admin | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Manager | ✅ | ✅ | ✅ | ❌ | ✅ | محدود |
| Employee | ✅ | ✅ | محدود | ❌ | ✅ | ❌ |
| Viewer | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |

### موديول المالية (Finance Module)
| المستوى | عرض | إضافة | تعديل | حذف | موافقة | أسعار |
|---------|------|-------|-------|------|--------|--------|
| Super Admin | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Admin | ✅ | ✅ | ✅ | محدود | محدود | ✅ |
| Manager | محدود | ✅ | محدود | ❌ | ❌ | محدود |
| Employee | محدود | ✅ | ❌ | ❌ | ❌ | ❌ |
| Viewer | محدود | ❌ | ❌ | ❌ | ❌ | ❌ |

---

## الحدود المالية للموافقات

### حدود الموافقة على الشراء
```python
PURCHASE_APPROVAL_LIMITS = {
    'SUPER_ADMIN': float('inf'),  # بلا حدود
    'ADMIN': 1000000,            # مليون جنيه
    'MANAGER': 100000,           # 100 ألف جنيه
    'EMPLOYEE': 10000,           # 10 آلاف جنيه
    'VIEWER': 0                  # لا يمكن الموافقة
}
```

### حدود الموافقة على البيع
```python
SALES_APPROVAL_LIMITS = {
    'SUPER_ADMIN': float('inf'),
    'ADMIN': 2000000,
    'MANAGER': 200000,
    'EMPLOYEE': 20000,
    'VIEWER': 0
}
```

---

## نظام الموافقات المتدرج

### موافقات الشراء
1. **أقل من 10,000 جنيه:** موافقة الموظف
2. **10,000 - 100,000 جنيه:** موافقة المدير
3. **100,000 - 1,000,000 جنيه:** موافقة الإدارة
4. **أكثر من 1,000,000 جنيه:** موافقة رئيس مجلس الإدارة

### موافقات التوظيف
1. **موظف عادي:** موافقة مدير HR
2. **رئيس قسم:** موافقة الإدارة العامة
3. **مدير:** موافقة رئيس مجلس الإدارة

---

## الصلاحيات الخاصة بالتطبيقات المحمولة

### Kamachat
- **جميع المستخدمين:** الوصول الأساسي للمحادثة
- **المديرين:** إنشاء مجموعات العمل
- **الإدارة العليا:** مراقبة المحادثات

### Hawk (الإدارة العليا فقط)
- **Super Admin:** وصول كامل
- **Admin:** وصول محدود للتقارير
- **باقي المستويات:** لا يوجد وصول

---

## تطبيق الصلاحيات في الكود

### Decorators
```python
@require_permission('import_module.view')
@require_permission('import_module.add')
@require_financial_limit(100000)
```

### Mixins
```python
class ImportPermissionMixin:
    permission_required = 'import_module.view'
    financial_limit = 50000
```

### Template Tags
```html
{% if user|has_permission:'stock_module.edit' %}
    <button>تعديل</button>
{% endif %}
```

---

## الأمان والمراقبة

### تسجيل العمليات
- جميع العمليات الحساسة تُسجل في KeyAuditLog
- تتبع محاولات الوصول غير المصرح بها
- إشعارات فورية للإدارة عند المخالفات

### التحقق المتعدد
- كلمة مرور قوية
- تسجيل دخول ثنائي العامل (اختياري)
- انتهاء صلاحية الجلسات

### المراقبة المستمرة
- مراقبة أنشطة المستخدمين
- تقارير دورية عن الصلاحيات
- تنبيهات عند تجاوز الحدود

---

## إعدادات الصلاحيات

### ملف الإعدادات
```python
# settings/permissions.py
PERMISSION_SETTINGS = {
    'SESSION_TIMEOUT': 8 * 60 * 60,  # 8 ساعات
    'MAX_LOGIN_ATTEMPTS': 3,
    'PASSWORD_EXPIRY_DAYS': 90,
    'AUDIT_LOG_RETENTION_DAYS': 365
}
```

### قاعدة البيانات
- جدول KeyUserPermissions للصلاحيات التفصيلية
- جدول KeyUserGroups للمجموعات
- جدول KeyPermissionLimits للحدود المالية

---

**آخر تحديث:** 2025-08-15  
**المسؤول:** فريق الأمان - KamaVerse
