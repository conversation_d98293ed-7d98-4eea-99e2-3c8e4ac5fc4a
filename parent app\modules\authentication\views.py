"""
Authentication Views for KamaVerse
عروض المصادقة لنظام KamaVerse
"""

from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.views.decorators.csrf import csrf_protect
from django.views.decorators.cache import never_cache
from django.http import JsonResponse
from django.utils import timezone
from django.conf import settings
from core.models import KeyUser, KeyAuditLog
from .forms import LoginForm
import json


@csrf_protect
@never_cache
def login_view(request):
    """
    صفحة تسجيل الدخول
    """
    if request.user.is_authenticated:
        return redirect('authentication:dashboard')
    
    if request.method == 'POST':
        form = LoginForm(request.POST)
        if form.is_valid():
            username = form.cleaned_data['username']
            password = form.cleaned_data['password']
            
            try:
                # البحث عن المستخدم
                user = KeyUser.objects.get(username=username)
                
                # التحقق من قفل الحساب
                if user.is_account_locked:
                    messages.error(request, 'تم قفل حسابك بسبب محاولات دخول خاطئة متكررة. يرجى الاتصال بالإدارة.')
                    return render(request, 'authentication/login.html', {'form': form})
                
                # التحقق من كلمة المرور
                user_auth = authenticate(request, username=username, password=password)
                
                if user_auth is not None:
                    if user_auth.is_active:
                        login(request, user_auth)
                        
                        # إعادة تعيين محاولات الدخول الفاشلة
                        user.reset_failed_login_attempts()
                        
                        # تسجيل نجاح الدخول
                        KeyAuditLog.log_operation(
                            user=user,
                            operation_type='LOGIN',
                            module='SYSTEM',
                            description=f'تسجيل دخول ناجح للمستخدم {user.arabic_name}',
                            ip_address=get_client_ip(request),
                            user_agent=request.META.get('HTTP_USER_AGENT', ''),
                            success=True
                        )
                        
                        messages.success(request, f'مرحباً {user.arabic_name}')
                        
                        # التوجيه حسب نوع المستخدم
                        next_url = request.GET.get('next', 'authentication:dashboard')
                        return redirect(next_url)
                    else:
                        messages.error(request, 'حسابك غير نشط. يرجى الاتصال بالإدارة.')
                else:
                    # زيادة عدد محاولات الدخول الفاشلة
                    user.increment_failed_login_attempts()
                    
                    # تسجيل فشل الدخول
                    KeyAuditLog.log_operation(
                        user=None,
                        operation_type='LOGIN',
                        module='SYSTEM',
                        description=f'محاولة دخول فاشلة للمستخدم {username}',
                        ip_address=get_client_ip(request),
                        user_agent=request.META.get('HTTP_USER_AGENT', ''),
                        success=False,
                        error_message='كلمة مرور خاطئة'
                    )
                    
                    remaining_attempts = settings.KAMAVERSE_SETTINGS.get('MAX_LOGIN_ATTEMPTS', 3) - user.failed_login_attempts
                    if remaining_attempts > 0:
                        messages.error(request, f'كلمة المرور خاطئة. المحاولات المتبقية: {remaining_attempts}')
                    else:
                        messages.error(request, 'تم قفل حسابك بسبب محاولات دخول خاطئة متكررة.')
                        
            except KeyUser.DoesNotExist:
                # تسجيل محاولة دخول بمستخدم غير موجود
                KeyAuditLog.log_operation(
                    user=None,
                    operation_type='LOGIN',
                    module='SYSTEM',
                    description=f'محاولة دخول بمستخدم غير موجود: {username}',
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    success=False,
                    error_message='مستخدم غير موجود'
                )
                messages.error(request, 'اسم المستخدم أو كلمة المرور خاطئة.')
        else:
            messages.error(request, 'يرجى تصحيح الأخطاء أدناه.')
    else:
        form = LoginForm()
    
    return render(request, 'authentication/login.html', {'form': form})


@login_required
def logout_view(request):
    """
    تسجيل الخروج
    """
    user = request.user
    
    # تسجيل الخروج في سجل العمليات
    KeyAuditLog.log_operation(
        user=user,
        operation_type='LOGOUT',
        module='SYSTEM',
        description=f'تسجيل خروج المستخدم {user.arabic_name}',
        ip_address=get_client_ip(request),
        user_agent=request.META.get('HTTP_USER_AGENT', ''),
        success=True
    )
    
    logout(request)
    messages.success(request, 'تم تسجيل الخروج بنجاح.')
    return redirect('authentication:login')


@login_required
def dashboard_view(request):
    """
    الصفحة الرئيسية (Dashboard)
    """
    user = request.user
    
    # الحصول على الموديولات المتاحة للمستخدم
    available_modules = get_user_modules(user)
    
    # إحصائيات سريعة
    context = {
        'user': user,
        'available_modules': available_modules,
        'company_name': settings.KAMAVERSE_SETTINGS.get('COMPANY_NAME', 'شركة القماش'),
        'system_name': settings.KAMAVERSE_SETTINGS.get('SYSTEM_NAME', 'KamaVerse'),
        'current_time': timezone.now(),
    }
    
    return render(request, 'authentication/dashboard.html', context)


@login_required
def user_profile_view(request):
    """
    صفحة الملف الشخصي للمستخدم
    """
    user = request.user
    
    context = {
        'user': user,
        'recent_logins': user.audit_logs.filter(
            operation_type='LOGIN'
        ).order_by('-created_at')[:10],
    }
    
    return render(request, 'authentication/profile.html', context)


def get_user_modules(user):
    """
    الحصول على الموديولات المتاحة للمستخدم حسب صلاحياته
    """
    modules = []
    
    # تحديد الموديولات حسب مستوى المستخدم
    if user.user_level in ['SUPER_ADMIN', 'ADMIN']:
        modules = [
            {
                'name': 'import_module',
                'title': 'الاستيراد',
                'icon': 'fas fa-ship',
                'url': '/import/',
                'description': 'إدارة عمليات الاستيراد والموردين'
            },
            {
                'name': 'stock_module',
                'title': 'المخزون',
                'icon': 'fas fa-warehouse',
                'url': '/stock/',
                'description': 'إدارة المخزون والمواد الخام'
            },
            {
                'name': 'finance_module',
                'title': 'المالية',
                'icon': 'fas fa-coins',
                'url': '/finance/',
                'description': 'إدارة الحسابات والمعاملات المالية'
            },
            {
                'name': 'sales_module',
                'title': 'المبيعات',
                'icon': 'fas fa-chart-line',
                'url': '/sales/',
                'description': 'إدارة المبيعات والعملاء'
            },
            {
                'name': 'crm_module',
                'title': 'علاقات العملاء',
                'icon': 'fas fa-users',
                'url': '/crm/',
                'description': 'إدارة علاقات العملاء'
            },
            {
                'name': 'hr_module',
                'title': 'الموارد البشرية',
                'icon': 'fas fa-user-tie',
                'url': '/hr/',
                'description': 'إدارة الموظفين والرواتب'
            },
            {
                'name': 'logistics_module',
                'title': 'اللوجستيات',
                'icon': 'fas fa-truck',
                'url': '/logistics/',
                'description': 'إدارة الشحن والتوصيل'
            },
            {
                'name': 'reporting_module',
                'title': 'التقارير',
                'icon': 'fas fa-chart-bar',
                'url': '/reports/',
                'description': 'التقارير والتحليلات'
            },
            {
                'name': 'users_module',
                'title': 'إدارة المستخدمين',
                'icon': 'fas fa-user-cog',
                'url': '/users/',
                'description': 'إدارة المستخدمين والصلاحيات'
            }
        ]
    elif user.user_level == 'MANAGER':
        # الموديولات المتاحة للمديرين حسب القسم
        if user.department == 'IMPORT':
            modules.append({
                'name': 'import_module',
                'title': 'الاستيراد',
                'icon': 'fas fa-ship',
                'url': '/import/',
                'description': 'إدارة عمليات الاستيراد والموردين'
            })
        # إضافة باقي الأقسام...
    
    # إضافة التطبيقات المحمولة للجميع
    modules.extend([
        {
            'name': 'kamachat',
            'title': 'KamaChat',
            'icon': 'fas fa-comments',
            'url': '/kamachat/',
            'description': 'نظام المحادثة الداخلية'
        }
    ])
    
    # إضافة Hawk للإدارة العليا فقط
    if user.user_level == 'SUPER_ADMIN':
        modules.append({
            'name': 'hawk',
            'title': 'Hawk',
            'icon': 'fas fa-eye',
            'url': '/hawk/',
            'description': 'لوحة تحكم الإدارة العليا'
        })
    
    return modules


def get_client_ip(request):
    """الحصول على عنوان IP للعميل"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
    return ip
