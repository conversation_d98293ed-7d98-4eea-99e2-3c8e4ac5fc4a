:root {
    --brand-red: #D62828;
    --brand-red-dark: #8B1116;
    --brand-red-light: #FCE8E8;

    --brand-gold: #C89A3C;
    --brand-gold-light: #F4D488;
    --brand-gold-dark: #8C6420;

    --ink: #1A1A1A;
    --slate: #4A4F57;
    --line: #E6E8ED;
    --canvas: #F7F8FB;
    --white: #FFFFFF;

    --accent-sand: #FFF3E0;
    --success: #2E7D32;
    --warning: #F39C12;
    --error: #C21807;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON>wal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(180deg, var(--white) 0%, var(--canvas) 100%);
    min-height: 100vh;
    direction: rtl;
    color: var(--ink);
}

.dashboard-wrapper {
    display: flex;
    min-height: 100vh;
    padding: 20px;
    gap: 20px;
}

/* Sidebar */
.sidebar {
    width: 280px;
    background: var(--white);
    border-radius: 20px;
    padding: 30px 20px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 10px 30px rgba(26, 26, 26, 0.08);
    border: 1px solid var(--line);
}

.sidebar-header .logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 20px;
    font-weight: bold;
    color: var(--ink);
    margin-bottom: 30px;
}

.sidebar-header .logo .logo-img {
    width: 96px;
    height: 96px;
    object-fit: contain;
}

.sidebar-header .logo span {
    color: var(--ink);
    font-family: 'Tajawal', sans-serif;
    font-weight: 700;
}

.user-profile {
    text-align: center;
    margin-bottom: 40px;
}

.user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 15px;
    overflow: hidden;
    border: 3px solid var(--brand-gold);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-info h4 {
    font-size: 22px;
    font-weight: 600;
    color: var(--ink);
    margin-bottom: 5px;
}

.user-info p {
    color: var(--slate);
    font-size: 18px;
}

.sidebar-nav ul {
    list-style: none;
    flex: 1;
}

.sidebar-nav li {
    margin-bottom: 8px;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    color: var(--slate);
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-size: 18px;
    font-weight: 500;
}

.sidebar-nav li.active a,
.sidebar-nav a:hover {
    background: var(--brand-red);
    color: var(--white);
}

.sidebar-nav li.active a:hover {
    background: var(--brand-red-dark);
}

.sidebar-nav i {
    width: 24px;
    text-align: center;
    color: var(--brand-gold);
    font-size: 18px;
}

.sidebar-nav li.active i,
.sidebar-nav a:hover i {
    color: var(--white);
}

.sidebar-footer {
    margin-top: auto;
    padding-top: 20px;
    border-top: 1px solid var(--line);
}

.logout-btn {
    display: flex;
    align-items: center;
    gap: 14px;
    padding: 16px 20px;
    color: var(--slate);
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    font-size: 18px;
    font-weight: 500;
}

.logout-btn:hover {
    background: var(--brand-red-light);
    color: var(--brand-red-dark);
}

.logout-btn i {
    color: var(--brand-gold);
    font-size: 18px;
}

/* Main Content */
.main-content {
    flex: 1;
}

.content-card {
    background: var(--white);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(26, 26, 26, 0.08);
    border: 1px solid var(--line);
    min-height: calc(100vh - 40px);
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.dashboard-header h1 {
    font-size: 40px;
    font-weight: 700;
    color: var(--ink);
    font-family: 'Tajawal', sans-serif;
}

.notification-badge {
    width: 40px;
    height: 40px;
    background: var(--brand-red);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    position: relative;
    transition: background 0.3s ease;
}

.notification-badge:hover {
    background: var(--brand-red-dark);
}

.notification-badge::after {
    content: '';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background: var(--error);
    border-radius: 50%;
    border: 2px solid var(--white);
}

/* Stats Row */
.stats-row {
    display: flex;
    gap: 30px;
    margin-bottom: 40px;
}

.stat-card {
    background: var(--white);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(26, 26, 26, 0.06);
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
    border: 1px solid var(--line);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(26, 26, 26, 0.12);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 20px;
}

.stat-icon.blue {
    background: linear-gradient(135deg, var(--brand-red) 0%, var(--brand-red-dark) 100%);
}

.stat-icon.red {
    background: linear-gradient(135deg, var(--brand-gold) 0%, var(--brand-gold-dark) 100%);
}

.stat-content {
    flex: 1;
}

.stat-label {
    font-size: 18px;
    color: var(--slate);
    margin-bottom: 4px;
    font-weight: 500;
    font-family: 'Tajawal', sans-serif;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: var(--ink);
    margin-bottom: 4px;
    font-family: 'Tajawal', sans-serif;
}

.stat-change {
    font-size: 16px;
    color: var(--slate);
    font-family: 'Tajawal', sans-serif;
}

/* Content Row */
.content-row {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
}

.activity-section {
    flex: 1;
}

.activity-section h3 {
    font-size: 26px;
    font-weight: 600;
    color: var(--ink);
    margin-bottom: 8px;
    font-family: 'Tajawal', sans-serif;
}

.activity-subtitle {
    font-size: 18px;
    color: var(--slate);
    margin-bottom: 30px;
    font-family: 'Tajawal', sans-serif;
}

.chart-container {
    position: relative;
    height: 200px;
    background: var(--canvas);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--line);
}

.chart-value {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    font-weight: 700;
    color: var(--ink);
    pointer-events: none;
}

.top-performers {
    width: 300px;
}

.top-performers h3 {
    font-size: 26px;
    font-weight: 600;
    color: var(--ink);
    margin-bottom: 30px;
    font-family: 'Tajawal', sans-serif;
}

.performer-list {
    margin-bottom: 20px;
}

.performer-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 0;
    border-bottom: 1px solid var(--line);
    transition: background 0.3s ease;
}

.performer-item:hover {
    background: var(--accent-sand);
    border-radius: 8px;
    padding: 12px 8px;
}

.performer-item:last-child {
    border-bottom: none;
}

.performer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid var(--brand-gold-light);
}

.performer-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.performer-info {
    flex: 1;
}

.performer-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--ink);
    margin-bottom: 2px;
    font-family: 'Tajawal', sans-serif;
}

.performer-handle {
    font-size: 16px;
    color: var(--slate);
    font-family: 'Tajawal', sans-serif;
}

.performer-score {
    font-size: 18px;
    font-weight: 600;
    color: var(--brand-gold-dark);
    font-family: 'Tajawal', sans-serif;
}

.view-more {
    color: var(--slate);
    text-decoration: none;
    font-size: 18px;
    font-weight: 500;
    transition: color 0.3s ease;
    font-family: 'Tajawal', sans-serif;
}

.view-more:hover {
    color: var(--brand-red);
}

/* Channels Section */
.channels-section h3 {
    font-size: 26px;
    font-weight: 600;
    color: var(--ink);
    margin-bottom: 8px;
    font-family: 'Tajawal', sans-serif;
}

.channels-subtitle {
    font-size: 18px;
    color: var(--slate);
    margin-bottom: 30px;
    font-family: 'Tajawal', sans-serif;
}

.channels-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.channel-card {
    background: var(--white);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(26, 26, 26, 0.06);
    border: 1px solid var(--line);
    text-align: center;
    transition: all 0.3s ease;
}

.channel-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(26, 26, 26, 0.12);
}

.channel-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    color: var(--white);
    font-size: 20px;
}

.channel-card.dribbble .channel-icon {
    background: linear-gradient(135deg, var(--brand-red) 0%, var(--brand-red-dark) 100%);
}

.channel-card.behance .channel-icon {
    background: linear-gradient(135deg, var(--brand-gold) 0%, var(--brand-gold-dark) 100%);
}

.channel-card.instagram .channel-icon {
    background: linear-gradient(135deg, var(--warning) 0%, #E67E22 100%);
}

.channel-card.pinterest .channel-icon {
    background: linear-gradient(135deg, var(--error) 0%, var(--brand-red-dark) 100%);
}

.channel-name {
    font-size: 20px;
    font-weight: 600;
    color: var(--ink);
    margin-bottom: 4px;
    font-family: 'Tajawal', sans-serif;
}

.channel-handle {
    font-size: 16px;
    color: var(--slate);
    margin-bottom: 12px;
    font-family: 'Tajawal', sans-serif;
}

.channel-change {
    font-size: 24px;
    font-weight: 700;
    color: var(--brand-gold-dark);
    font-family: 'Tajawal', sans-serif;
}



/* Responsive */
@media (max-width: 1200px) {
    .dashboard-wrapper {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        flex-direction: row;
        padding: 20px;
    }

    .user-profile {
        margin-bottom: 0;
        margin-left: 20px;
    }

    .sidebar-nav {
        flex: 1;
    }

    .sidebar-nav ul {
        display: flex;
        gap: 10px;
    }

    .sidebar-footer {
        margin-top: 0;
        margin-right: 20px;
        padding-top: 0;
        border-top: none;
    }

    .sidebar-footer .logout-btn {
        background: var(--brand-red-light);
        color: var(--brand-red-dark);
        border-radius: 12px;
    }

    .content-row {
        flex-direction: column;
    }

    .top-performers {
        width: 100%;
    }

    .channels-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .stats-row {
        flex-direction: column;
    }

    .channels-grid {
        grid-template-columns: 1fr;
    }
}
