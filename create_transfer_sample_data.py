#!/usr/bin/env python
"""
إنشاء بيانات تجريبية لنقل المخزون
Create sample data for stock transfer testing
"""
import os
import sys
import django
from decimal import Decimal
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

from django.contrib.auth.models import User
from django.utils import timezone
from inventory.models import (
    ItemMaster, Warehouse, BinLocation, StockBalance, 
    StockTransfer, Alert, UserProfile
)

def create_sample_transfers():
    """إنشاء طلبات نقل تجريبية"""
    print("🚀 بدء إنشاء بيانات تجريبية لنقل المخزون...")
    
    try:
        # الحصول على المستخدمين والمخازن
        manager = User.objects.get(username='manager')
        cairo_manager = User.objects.get(username='cairo_manager')
        daery_manager = User.objects.get(username='daery_manager')
        menoufia_manager = User.objects.get(username='menoufia_manager')
        alex_manager = User.objects.get(username='alex_manager')
        
        cairo_warehouse = Warehouse.objects.get(warehouse_code='CAI-003')
        daery_warehouse = Warehouse.objects.get(warehouse_code='RNG-002')
        menoufia_warehouse = Warehouse.objects.get(warehouse_code='MNF-004')
        alex_warehouse = Warehouse.objects.get(warehouse_code='ALX-001')
        
        # الحصول على بعض الأصناف
        items = ItemMaster.objects.filter(is_active=True)[:5]
        
        if not items.exists():
            print("❌ لا توجد أصناف في النظام. يرجى إنشاء أصناف أولاً")
            return
        
        print(f"✅ تم العثور على {items.count()} صنف")
        
        # إنشاء طلبات نقل متنوعة
        transfers_data = [
            {
                'source_warehouse': menoufia_warehouse,
                'destination_warehouse': cairo_warehouse,
                'item': items[0],
                'quantity': Decimal('50.000'),
                'reason': 'نقص في مخزون القاهرة',
                'notes': 'طلب عاجل لتلبية طلبات العملاء',
                'requested_by': menoufia_manager,
                'status': 'PENDING',
                'days_ago': 1
            },
            {
                'source_warehouse': alex_warehouse,
                'destination_warehouse': daery_warehouse,
                'item': items[1],
                'quantity': Decimal('25.500'),
                'reason': 'إعادة توزيع المخزون',
                'notes': 'توزيع متوازن للمخزون',
                'requested_by': alex_manager,
                'status': 'APPROVED',
                'days_ago': 2
            },
            {
                'source_warehouse': cairo_warehouse,
                'destination_warehouse': alex_warehouse,
                'item': items[2],
                'quantity': Decimal('100.000'),
                'reason': 'طلب من فرع الإسكندرية',
                'notes': 'لتلبية طلبات العملاء في الإسكندرية',
                'requested_by': cairo_manager,
                'status': 'COMPLETED',
                'days_ago': 5
            },
            {
                'source_warehouse': daery_warehouse,
                'destination_warehouse': menoufia_warehouse,
                'item': items[3],
                'quantity': Decimal('75.250'),
                'reason': 'تجديد مخزون المنوفية',
                'notes': 'نقل دوري للمخزون',
                'requested_by': daery_manager,
                'status': 'REJECTED',
                'days_ago': 3
            },
            {
                'source_warehouse': menoufia_warehouse,
                'destination_warehouse': alex_warehouse,
                'item': items[4],
                'quantity': Decimal('30.000'),
                'reason': 'طلب عاجل',
                'notes': 'عميل مهم يحتاج الكمية بسرعة',
                'requested_by': menoufia_manager,
                'status': 'PENDING',
                'days_ago': 0
            }
        ]
        
        created_count = 0
        
        for transfer_data in transfers_data:
            # حساب التاريخ
            transfer_date = timezone.now() - timedelta(days=transfer_data['days_ago'])
            
            # إنشاء طلب النقل
            transfer = StockTransfer.objects.create(
                source_warehouse=transfer_data['source_warehouse'],
                destination_warehouse=transfer_data['destination_warehouse'],
                item=transfer_data['item'],
                quantity=transfer_data['quantity'],
                reason=transfer_data['reason'],
                notes=transfer_data['notes'],
                requested_by=transfer_data['requested_by'],
                status=transfer_data['status']
            )
            
            # تحديث التاريخ
            transfer.transfer_date = transfer_date
            transfer.created_at = transfer_date
            
            # إضافة تفاصيل الاعتماد حسب الحالة
            if transfer_data['status'] in ['APPROVED', 'COMPLETED', 'REJECTED']:
                transfer.approved_by = manager
                transfer.approved_at = transfer_date + timedelta(hours=2)
                
                if transfer_data['status'] == 'APPROVED':
                    transfer.approval_notes = 'تم الاعتماد - طلب مبرر'
                elif transfer_data['status'] == 'COMPLETED':
                    transfer.approval_notes = 'تم الاعتماد والإكمال بنجاح'
                    transfer.completed_at = transfer_date + timedelta(hours=4)
                elif transfer_data['status'] == 'REJECTED':
                    transfer.approval_notes = 'مرفوض - الكمية غير متوفرة حالياً'
            
            transfer.save()
            created_count += 1
            
            print(f"✅ تم إنشاء طلب النقل: {transfer.transfer_number}")
            print(f"   من: {transfer.source_warehouse.warehouse_name}")
            print(f"   إلى: {transfer.destination_warehouse.warehouse_name}")
            print(f"   الصنف: {transfer.item.item_name_ar}")
            print(f"   الكمية: {transfer.quantity}")
            print(f"   الحالة: {transfer.get_status_display()}")
            print()
        
        print(f"🎉 تم إنشاء {created_count} طلب نقل تجريبي بنجاح!")
        
        # إنشاء بعض التنبيهات للطلبات المعلقة
        pending_transfers = StockTransfer.objects.filter(status='PENDING')
        for transfer in pending_transfers:
            Alert.create_alert(
                alert_type='TRANSFER_REQUESTED',
                title=f'طلب نقل مخزون جديد {transfer.transfer_number}',
                message=f'طلب نقل {transfer.quantity} {transfer.item.unit_of_measure} من {transfer.item.item_name_ar} من {transfer.source_warehouse.warehouse_name} إلى {transfer.destination_warehouse.warehouse_name}. السبب: {transfer.reason}',
                priority='HIGH',
                item=transfer.item,
                warehouse=transfer.source_warehouse
            )
        
        print(f"✅ تم إنشاء {pending_transfers.count()} تنبيه للطلبات المعلقة")
        
    except Exception as e:
        print(f"❌ حدث خطأ: {str(e)}")
        import traceback
        traceback.print_exc()

def show_transfer_summary():
    """عرض ملخص طلبات النقل"""
    print("\n📊 ملخص طلبات نقل المخزون:")
    print("=" * 50)
    
    total_transfers = StockTransfer.objects.count()
    pending_transfers = StockTransfer.objects.filter(status='PENDING').count()
    approved_transfers = StockTransfer.objects.filter(status='APPROVED').count()
    completed_transfers = StockTransfer.objects.filter(status='COMPLETED').count()
    rejected_transfers = StockTransfer.objects.filter(status='REJECTED').count()
    
    print(f"📦 إجمالي طلبات النقل: {total_transfers}")
    print(f"⏳ في الانتظار: {pending_transfers}")
    print(f"✅ معتمدة: {approved_transfers}")
    print(f"🎉 مكتملة: {completed_transfers}")
    print(f"❌ مرفوضة: {rejected_transfers}")
    
    print("\n📋 تفاصيل الطلبات:")
    print("-" * 50)
    
    for transfer in StockTransfer.objects.all().order_by('-created_at'):
        print(f"🔸 {transfer.transfer_number}")
        print(f"   {transfer.source_warehouse.warehouse_name} → {transfer.destination_warehouse.warehouse_name}")
        print(f"   {transfer.item.item_name_ar} - {transfer.quantity} {transfer.item.unit_of_measure}")
        print(f"   الحالة: {transfer.get_status_display()}")
        print(f"   طلب بواسطة: {transfer.requested_by.get_full_name()}")
        print()

if __name__ == '__main__':
    create_sample_transfers()
    show_transfer_summary()
