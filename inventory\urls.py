"""
URLs for Inventory Module
روابط موديول المخزون
"""
from django.urls import path
from . import views
from . import user_views

app_name = 'inventory'

urlpatterns = [
    # Dashboard - الصفحة الرئيسية
    path('dashboard/', views.dashboard, name='dashboard'),
    path('', views.dashboard, name='dashboard_root'),  # للوصول من الجذر

    # Items Management - إدارة الأصناف
    path('items/', views.items_list, name='items_list'),
    path('items/add/', views.item_add, name='item_add'),
    path('items/<uuid:pk>/', views.item_detail, name='item_detail'),
    path('items/<uuid:pk>/edit/', views.item_edit, name='item_edit'),
    path('items/<uuid:pk>/delete/', views.item_delete, name='item_delete'),
    path('items/export/excel/', views.items_export_excel, name='items_export_excel'),
    
    # Warehouses Management - إدارة المخازن
    path('warehouses/', views.warehouses_list, name='warehouses_list'),
    path('warehouses/create/', views.warehouse_create, name='warehouse_create'),
    path('warehouses/<uuid:pk>/', views.warehouse_detail, name='warehouse_detail'),
    path('warehouses/<uuid:warehouse_id>/edit/', views.warehouse_edit, name='warehouse_edit'),
    path('warehouses/<uuid:warehouse_id>/add-items/', views.warehouse_add_items, name='warehouse_add_items'),
    path('warehouses/<uuid:warehouse_id>/locations/', views.bin_locations_list, name='bin_locations_list'),
    path('warehouses/<uuid:warehouse_id>/locations/create/', views.bin_location_create, name='bin_location_create'),
    
    # User Management - إدارة المستخدمين
    path('users/', user_views.users_list, name='users_list'),
    path('users/<uuid:user_id>/', user_views.user_detail, name='user_detail'),
    path('users/<uuid:user_id>/permissions/', user_views.update_user_permissions, name='update_user_permissions'),
    path('users/add/', user_views.add_user, name='add_user'),
    path('users/<uuid:user_id>/delete/', user_views.delete_user, name='delete_user'),
    
    # Stock Movements - حركات المخزون
    path('movements/', views.movements_list, name='movements_list'),
    path('movements/receipt/', views.goods_receipt_create, name='goods_receipt_create'),
    path('movements/receipt/<uuid:pk>/', views.goods_receipt_detail, name='goods_receipt_detail'),
    path('movements/receipt/<uuid:pk>/print/', views.goods_receipt_print, name='goods_receipt_print'),
    path('movements/receipt/<uuid:pk>/pdf/', views.goods_receipt_pdf, name='goods_receipt_pdf'),
    path('excel/template/', views.download_excel_template, name='download_excel_template'),
    path('excel/preview/', views.process_excel_preview, name='process_excel_preview'),
    path('excel/issue/preview/', views.process_issue_excel_preview, name='process_issue_excel_preview'),

    # Goods Issue - إذن الصرف
    path('movements/issue/', views.goods_issue_dashboard, name='goods_issue_dashboard'),
    path('movements/issue/create/', views.goods_issue_create, name='goods_issue_create'),
    path('movements/issue/<uuid:pk>/', views.goods_issue_detail, name='goods_issue_detail'),
    path('movements/issue/<uuid:pk>/print/', views.goods_issue_print, name='goods_issue_print'),
    path('movements/issue/<uuid:pk>/pdf/', views.goods_issue_pdf, name='goods_issue_pdf'),
    path('movements/issue/sales/', views.goods_issue_sales_list, name='goods_issue_sales_list'),
    path('movements/issue/damage/', views.goods_issue_damage_list, name='goods_issue_damage_list'),

    # Stock Transfer - نقل المخزون
    path('movements/transfer/', views.stock_transfer_list, name='stock_transfer_list'),
    path('movements/transfer/create/', views.stock_transfer_create, name='stock_transfer_create'),
    path('movements/transfer/<uuid:pk>/', views.stock_transfer_detail, name='stock_transfer_detail'),
    path('movements/transfer/<uuid:pk>/approve/', views.stock_transfer_approve, name='stock_transfer_approve'),
    path('movements/transfer/<uuid:pk>/reject/', views.stock_transfer_reject, name='stock_transfer_reject'),

    # Reports - التقارير
    path('reports/', views.reports_main_dashboard, name='reports_main_dashboard'),
    path('reports/stock-balances/', views.current_stock_balances_report, name='current_stock_balances_report'),
    path('reports/stock-movements/', views.stock_movements_report, name='stock_movements_report'),
    path('reports/warehouse-stock/', views.warehouse_stock_report, name='warehouse_stock_report'),
    path('reports/bin-location-stock/', views.bin_location_stock_report, name='bin_location_stock_report'),
    
    # Enhanced Reports - التقارير المحسنة
    path('reports/enhanced-items/', views.enhanced_items_quantity_report, name='enhanced_items_quantity_report'),
    path('reports/consolidated-movements/', views.consolidated_movements_report, name='consolidated_movements_report'),
    path('reports/warehouse-movements/<uuid:warehouse_id>/', views.warehouse_movements_detail, name='warehouse_movements_detail'),
    path('reports/warehouse-manager/', views.warehouse_manager_report, name='warehouse_manager_report'),
    
    # Export Functions - وظائف التصدير
    path('reports/export/items-excel/', views.export_items_quantity_excel, name='export_items_quantity_excel'),
    path('reports/export/movements-excel/', views.export_movements_excel, name='export_movements_excel'),
    path('reports/export/manager-pdf/', views.export_manager_report_pdf, name='export_manager_report_pdf'),
    
    path('reports/daily/', views.daily_receipts_report, name='daily_receipts_report'),
    path('reports/monthly/', views.monthly_receipts_report, name='monthly_receipts_report'),
    path('reports/custom/', views.custom_receipts_report, name='custom_receipts_report'),
    # path('reports/stock-balance/', views.stock_balance_report, name='stock_balance_report'),
    # path('reports/movements/', views.movements_report, name='movements_report'),
    # path('reports/low-stock/', views.low_stock_report, name='low_stock_report'),
    # path('reports/expired/', views.expired_items_report, name='expired_items_report'),
    
    # Alerts - التنبيهات
    path('alerts/', views.alerts_list, name='alerts_list'),
    path('alerts/<uuid:pk>/mark-read/', views.mark_alert_read, name='mark_alert_read'),

    # Authentication - المصادقة
    path('login/', views.custom_login_view, name='login'),
    path('logout/', views.custom_logout_view, name='logout'),

    # Design Pages - صفحات التصميم
    path('design/login/', views.login_design_view, name='login_design'),
]