{% extends 'base.html' %}
{% load static %}

{% block title %}إدارة المستخدمين - Ka<PERSON>Verse{% endblock %}

{% block extra_css %}
<!-- Tooltip dependencies -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<style>
    .page-header {
        background: linear-gradient(135deg, var(--brand-red), #FF6B6B);
        color: var(--white);
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 10px 25px rgba(214, 40, 40, 0.2);
    }

    .page-header h1 {
        font-weight: 700;
        margin: 0;
        color: var(--white);
    }

    .summary-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .summary-card {
        background-color: var(--white);
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        border: 1px solid var(--line);
        text-align: center;
        transition: all 0.3s ease;
    }

    .summary-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .summary-card .value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--brand-red);
        margin: 0.5rem 0;
    }

    .summary-card .label {
        color: var(--slate);
        font-size: 1rem;
    }

    .users-section {
        background-color: var(--white);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        border: 1px solid var(--line);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--brand-red);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-left: 0.5rem;
        color: var(--brand-gold);
    }

    .users-table {
        width: 100%;
        border-collapse: collapse;
    }

    .users-table th,
    .users-table td {
        padding: 1rem;
        border: 1px solid var(--line);
    }

    .users-table th {
        background-color: var(--brand-red);
        color: var(--white);
        font-weight: 600;
        text-align: right;
    }

    .users-table tr:hover {
        background-color: rgba(214, 40, 40, 0.05);
    }

    .role-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 30px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .role-manager {
        background-color: rgba(25, 135, 84, 0.15);
        color: #198754;
    }

    .role-warehouse-manager {
        background-color: rgba(13, 110, 253, 0.15);
        color: #0d6efd;
    }

    .role-employee {
        background-color: rgba(108, 117, 125, 0.15);
        color: #6c757d;
    }

    .status-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 30px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .status-active {
        background-color: rgba(25, 135, 84, 0.15);
        color: #198754;
    }

    .status-inactive {
        background-color: rgba(220, 53, 69, 0.15);
        color: #dc3545;
    }

    /* Enhanced action buttons */
    .action-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        margin: 0 2px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        border: none;
        color: white;
        font-size: 14px;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    }

    .btn-permissions {
        background: linear-gradient(135deg, var(--brand-gold), #a67c2d);
    }

    .btn-permissions:hover {
        background: linear-gradient(135deg, #a67c2d, var(--brand-gold));
    }

    .btn-delete {
        background: linear-gradient(135deg, var(--brand-red), #b31d1d);
    }

    .btn-delete:hover {
        background: linear-gradient(135deg, #b31d1d, var(--brand-red));
    }

    .btn-edit {
        background: linear-gradient(135deg, #0d6efd, #0b5ed7);
    }

    .btn-edit:hover {
        background: linear-gradient(135deg, #0b5ed7, #0d6efd);
    }

    .btn-add-user {
        background: linear-gradient(135deg, var(--success), #256d28);
        padding: 10px 20px;
        border-radius: 50px;
        font-weight: 600;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
        transition: all 0.3s ease;
        border: none;
        color: white;
    }

    .btn-add-user:hover {
        background: linear-gradient(135deg, #256d28, var(--success));
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
    }

    .btn-add-user i {
        margin-left: 8px;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .users-table {
            font-size: 0.85rem;
        }
        
        .users-table th,
        .users-table td {
            padding: 0.5rem;
        }
        
        .action-btn {
            width: 30px;
            height: 30px;
            font-size: 12px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <h1>
            <i class="fas fa-users me-2"></i>
            إدارة المستخدمين
        </h1>
    </div>

    <!-- معلومات عن المدير -->
    <div class="alert alert-warning mb-4">
        <h5><i class="fas fa-shield-alt"></i> صفحة خاصة بمدير النظام فقط</h5>
        <p>هذه الصفحة متاحة فقط لمدير النظام (admin). يمكنك من خلالها إدارة جميع المستخدمين والمديرين وإنشاء مستخدمين جدد.</p>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="mb-4">
        <a href="{% url 'inventory:add_user' %}" class="btn-add-user">
            <i class="fas fa-user-plus me-2"></i>
            إضافة مستخدم جديد
        </a>
    </div>

    <!-- بطاقات الملخص -->
    <div class="summary-cards">
        <div class="summary-card">
            <div class="value">{{ total_users }}</div>
            <div class="label">إجمالي المستخدمين</div>
        </div>
        <div class="summary-card">
            <div class="value">{{ managers|length }}</div>
            <div class="label">مدراء المخازن</div>
        </div>
        <div class="summary-card">
            <div class="value">{{ warehouse_managers|length }}</div>
            <div class="label">مسؤولي المخازن</div>
        </div>
        <div class="summary-card">
            <div class="value">{{ employees|length }}</div>
            <div class="label">موظفين</div>
        </div>
    </div>

    <!-- قسم المدراء -->
    {% if managers %}
    <div class="users-section">
        <h2 class="section-title">
            <i class="fas fa-crown"></i>
            مدراء المخازن
        </h2>
        <table class="users-table">
            <thead>
                <tr>
                    <th>اسم المستخدم</th>
                    <th>الاسم الكامل</th>
                    <th>الدور</th>
                    <th>المخازن المخصصة</th>
                    <th>القسم</th>
                    <th>رقم الهاتف</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for manager in managers %}
                <tr>
                    <td>{{ manager.username }}</td>
                    <td>{{ manager.full_name }}</td>
                    <td>
                        <span class="role-badge role-manager">{{ manager.role }}</span>
                    </td>
                    <td>{{ manager.warehouse }}</td>
                    <td>{{ manager.department }}</td>
                    <td>{{ manager.phone }}</td>
                    <td>
                        {% if manager.is_active %}
                        <span class="status-badge status-active">نشط</span>
                        {% else %}
                        <span class="status-badge status-inactive">غير نشط</span>
                        {% endif %}
                    </td>
                    <td>
                        <a href="{% url 'inventory:user_detail' user_id=manager.id %}" class="action-btn btn-permissions" data-bs-toggle="tooltip" title="إدارة الصلاحيات">
                            <i class="fas fa-user-shield"></i>
                        </a>
                        {% if manager.username != 'admin' %}
                        <a href="{% url 'inventory:delete_user' user_id=manager.id %}" class="action-btn btn-delete" data-bs-toggle="tooltip" title="حذف المستخدم">
                            <i class="fas fa-trash"></i>
                        </a>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}

    <!-- قسم مسؤولي المخازن -->
    {% if warehouse_managers %}
    <div class="users-section">
        <h2 class="section-title">
            <i class="fas fa-warehouse"></i>
            مسؤولي المخازن
        </h2>
        <table class="users-table">
            <thead>
                <tr>
                    <th>اسم المستخدم</th>
                    <th>الاسم الكامل</th>
                    <th>الدور</th>
                    <th>المخزن المخصص</th>
                    <th>القسم</th>
                    <th>رقم الهاتف</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for manager in warehouse_managers %}
                <tr>
                    <td>{{ manager.username }}</td>
                    <td>{{ manager.full_name }}</td>
                    <td>
                        <span class="role-badge role-warehouse-manager">{{ manager.role }}</span>
                    </td>
                    <td>{{ manager.warehouse }}</td>
                    <td>{{ manager.department }}</td>
                    <td>{{ manager.phone }}</td>
                    <td>
                        {% if manager.is_active %}
                        <span class="status-badge status-active">نشط</span>
                        {% else %}
                        <span class="status-badge status-inactive">غير نشط</span>
                        {% endif %}
                    </td>
                    <td>
                        <a href="{% url 'inventory:user_detail' user_id=manager.id %}" class="action-btn btn-permissions" data-bs-toggle="tooltip" title="إدارة الصلاحيات">
                            <i class="fas fa-user-shield"></i>
                        </a>
                        <a href="{% url 'inventory:delete_user' user_id=manager.id %}" class="action-btn btn-delete" data-bs-toggle="tooltip" title="حذف المستخدم">
                            <i class="fas fa-trash"></i>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}

    <!-- قسم الموظفين -->
    {% if employees %}
    <div class="users-section">
        <h2 class="section-title">
            <i class="fas fa-user"></i>
            الموظفين
        </h2>
        <table class="users-table">
            <thead>
                <tr>
                    <th>اسم المستخدم</th>
                    <th>الاسم الكامل</th>
                    <th>الدور</th>
                    <th>المخزن المخصص</th>
                    <th>القسم</th>
                    <th>رقم الهاتف</th>
                    <th>الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                {% for employee in employees %}
                <tr>
                    <td>{{ employee.username }}</td>
                    <td>{{ employee.full_name }}</td>
                    <td>
                        <span class="role-badge role-employee">{{ employee.role }}</span>
                    </td>
                    <td>{{ employee.warehouse }}</td>
                    <td>{{ employee.department }}</td>
                    <td>{{ employee.phone }}</td>
                    <td>
                        {% if employee.is_active %}
                        <span class="status-badge status-active">نشط</span>
                        {% else %}
                        <span class="status-badge status-inactive">غير نشط</span>
                        {% endif %}
                    </td>
                    <td>
                        <a href="{% url 'inventory:user_detail' user_id=employee.id %}" class="action-btn btn-permissions" data-bs-toggle="tooltip" title="إدارة الصلاحيات">
                            <i class="fas fa-user-shield"></i>
                        </a>
                        <a href="{% url 'inventory:delete_user' user_id=employee.id %}" class="action-btn btn-delete" data-bs-toggle="tooltip" title="حذف المستخدم">
                            <i class="fas fa-trash"></i>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
</div>
{% endblock %}