import os
import sys
import django
import time

# Setup Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

def test_reports_directly():
    """Test reports functionality directly"""
    print("🧪 TESTING REPORTS HTTP BEHAVIOR")
    print("=" * 40)
    
    # Test 1: Django test client
    print("1️⃣ Django Test Client:")
    try:
        from django.test import Client
        from django.contrib.auth.models import User
        
        client = Client()
        
        # Test without authentication
        response = client.get('/reports/')
        print(f"   Without auth: {response.status_code} -> {response.get('Location', 'No redirect')}")
        
        # Test with admin authentication
        admin_user = User.objects.get(username='admin')
        client.force_login(admin_user)
        
        response = client.get('/reports/')
        print(f"   With admin: {response.status_code}")
        if response.status_code == 302:
            print(f"   🚨 REDIRECTED TO: {response.get('Location')}")
        elif response.status_code == 200:
            print(f"   ✅ SUCCESS: Page loaded correctly")
            # Check if it's the right template
            content = response.content.decode('utf-8')
            if 'التقارير الأساسية' in content:
                print(f"   ✅ Correct content: Reports dashboard loaded")
            else:
                print(f"   ⚠️ Wrong content: Not reports template")
        
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    # Test 2: Check URL patterns
    print("\n2️⃣ URL Pattern Check:")
    try:
        from django.urls import reverse, resolve
        
        url = reverse('inventory:reports_main_dashboard')
        print(f"   URL resolves to: {url}")
        
        match = resolve(url)
        print(f"   View function: {match.func.__name__}")
        print(f"   App namespace: {match.namespace}")
        print(f"   URL name: {match.url_name}")
        
    except Exception as e:
        print(f"   ❌ URL Error: {str(e)}")
    
    # Test 3: Check user profile
    print("\n3️⃣ User Profile Check:")
    try:
        from inventory.models import UserProfile
        from inventory.views import get_user_warehouse
        
        admin_user = User.objects.get(username='admin')
        warehouse = get_user_warehouse(admin_user)
        
        print(f"   Admin user exists: ✅")
        print(f"   get_user_warehouse result: {warehouse}")
        
        if warehouse:
            print(f"   ✅ User has warehouse: {warehouse.warehouse_name}")
        else:
            print(f"   ❌ No warehouse assigned - THIS CAUSES REDIRECT!")
            
    except Exception as e:
        print(f"   ❌ Profile Error: {str(e)}")
    
    # Test 4: Template check
    print("\n4️⃣ Template Check:")
    template_path = "templates/inventory/reports/main_dashboard.html"
    if os.path.exists(template_path):
        with open(template_path, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            print(f"   Template exists: ✅")
            print(f"   Extends: {first_line}")
            if '"base.html"' in first_line:
                print(f"   ✅ Correct base template")
            else:
                print(f"   ❌ Wrong base template")
    else:
        print(f"   ❌ Template not found")

if __name__ == "__main__":
    test_reports_directly()