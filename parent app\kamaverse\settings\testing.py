"""
إعدادات الاختبار لمشروع KamaVerse
"""

from .base import *

# Debug settings for testing
DEBUG = False
TEMPLATE_DEBUG = False

# Test database (in-memory SQLite)
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
        'OPTIONS': {
            'timeout': 20,
        }
    }
}

# Password hashers for testing (faster)
PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.MD5PasswordHasher',
]

# Cache for testing
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# Email backend for testing
EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'

# Static files for testing
STATIC_ROOT = '/tmp/kamaverse_static/'
MEDIA_ROOT = '/tmp/kamaverse_media/'

# Disable migrations for testing
class DisableMigrations:
    def __contains__(self, item):
        return True
    
    def __getitem__(self, item):
        return None

MIGRATION_MODULES = DisableMigrations()

# Logging for testing (minimal)
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'WARNING',
    },
}

# Security settings for testing (relaxed)
SECURE_SSL_REDIRECT = False
SESSION_COOKIE_SECURE = False
CSRF_COOKIE_SECURE = False

# Testing specific settings
KAMAVERSE_SETTINGS.update({
    'ENVIRONMENT': 'testing',
    'DEBUG_MODE': False,
    'ENABLE_SILK_PROFILING': False,
    'MOCK_EXTERNAL_APIS': True,
    'FAST_TESTS': True,
})

# Channels settings for testing
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels.layers.InMemoryChannelLayer'
    }
}

# Celery settings for testing (eager execution)
CELERY_TASK_ALWAYS_EAGER = True
CELERY_TASK_EAGER_PROPAGATES = True

# Test-specific middleware (remove some for speed)
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
]

print("🧪 KamaVerse Testing Environment Loaded")
print(f"🗄️ Database: In-Memory SQLite")
print(f"🔐 Debug Mode: {DEBUG}")
print(f"⚡ Fast Tests: Enabled")
