# Generated by Django 5.2.5 on 2025-08-21 16:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0003_goodsreceipt_goodsreceiptitem_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='alert',
            name='alert_type',
            field=models.CharField(choices=[('LOW_STOCK', 'مخزون منخفض'), ('OUT_OF_STOCK', 'نفاد المخزون'), ('NEAR_EXPIRY', 'اقتراب انتهاء الصلاحية'), ('EXPIRED', 'منتهي الصلاحية'), ('SLOW_MOVING', 'بطيء الحركة'), ('WAREHOUSE_FULL', 'امتلاء المخزن'), ('ITEM_ADDED', 'إضافة صنف جديد'), ('ITEM_MODIFIED', 'تعديل صنف'), ('ITEM_DELETED', 'حذف صنف'), ('RECEIPT_CREATED', 'إذن استلام جديد'), ('ISSUE_CREATED', 'إذن صرف جديد'), ('TRANSFER_CREATED', 'نقل مخزون'), ('SHIPMENT_ARRIVED', 'وصول شحنة'), ('DAMAGE_DETECTED', 'اكتشاف تلف'), ('LOCATION_CHANGED', 'تغيير موقع تخزين'), ('WAREHOUSE_ADDED', 'إضافة مخزن جديد'), ('LOCATION_STATUS_CHANGED', 'تغيير حالة موقع التخزين')], max_length=30, verbose_name='نوع التنبيه'),
        ),
        migrations.AlterField(
            model_name='stockmovement',
            name='movement_type',
            field=models.CharField(choices=[('RECEIPT', 'إذن استلام'), ('ISSUE', 'إذن صرف'), ('TRANSFER_IN', 'نقل وارد'), ('TRANSFER_OUT', 'نقل صادر'), ('OPENING_BALANCE', 'رصيد افتتاحي'), ('DAMAGE', 'تلف'), ('EXPIRED', 'منتهي الصلاحية')], max_length=20, verbose_name='نوع الحركة'),
        ),
    ]
