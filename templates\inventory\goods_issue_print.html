<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة إذن الصرف - {{ issue.issue_number }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background: white;
            direction: rtl;
        }

        .print-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .company-header {
            text-align: center;
            border-bottom: 3px solid #D62828;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #D62828;
            margin-bottom: 5px;
        }

        .company-name-en {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
        }

        .document-title {
            font-size: 20px;
            font-weight: bold;
            color: #D62828;
            text-align: center;
            margin-bottom: 30px;
            padding: 10px;
            border: 2px solid #D62828;
            border-radius: 8px;
        }

        .info-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-group {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px 0;
            border-bottom: 1px dotted #ccc;
        }

        .info-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .info-label {
            font-weight: bold;
            color: #555;
        }

        .info-value {
            color: #333;
        }

        .issue-type-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .badge-sale {
            background: #d4edda;
            color: #155724;
        }

        .badge-damage {
            background: #f8d7da;
            color: #721c24;
        }

        .items-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #D62828;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 2px solid #D62828;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #333;
            padding: 8px;
            text-align: center;
        }

        .items-table th {
            background: #D62828;
            color: white;
            font-weight: bold;
        }

        .items-table tbody tr:nth-child(even) {
            background: #f9f9f9;
        }

        .totals-section {
            background: #f8f9fa;
            border: 2px solid #D62828;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 30px;
        }

        .totals-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .totals-row:last-child {
            margin-bottom: 0;
            font-weight: bold;
            font-size: 18px;
            color: #D62828;
            border-top: 1px solid #D62828;
            padding-top: 8px;
        }

        .notes-section {
            margin-bottom: 30px;
        }

        .notes-content {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            min-height: 60px;
        }

        .signatures-section {
            margin-top: 40px;
        }

        .signatures-table {
            width: 100%;
            border-collapse: collapse;
        }

        .signatures-table th,
        .signatures-table td {
            border: 1px solid #333;
            padding: 15px;
            text-align: center;
        }

        .signatures-table th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .signature-line {
            border-bottom: 1px solid #333;
            height: 40px;
            margin-bottom: 10px;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
        }

        .print-buttons {
            text-align: center;
            margin-bottom: 20px;
        }

        .btn {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }

        .btn-print {
            background: #D62828;
            color: white;
        }

        .btn-back {
            background: #6c757d;
            color: white;
        }

        .btn:hover {
            opacity: 0.8;
        }

        @media print {
            .print-buttons {
                display: none;
            }
            
            body {
                font-size: 12px;
            }
            
            .print-container {
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <!-- أزرار الطباعة -->
        <div class="print-buttons">
            <button class="btn btn-print" onclick="window.print()">
                طباعة الإذن
            </button>
            <a href="{% url 'inventory:goods_issue_detail' issue.pk %}" class="btn btn-back">
                العودة للتفاصيل
            </a>
        </div>

        <!-- رأس الشركة -->
        <div class="company-header">
            <div class="company-name">شركة القماش للاستيراد والتصدير</div>
            <div class="company-name-en">KamaVerse Import & Export Company</div>
        </div>

        <!-- عنوان المستند -->
        <div class="document-title">
            {% if issue.issue_type == 'SALE' %}
                إذن صرف - مبيعات
            {% else %}
                إذن صرف - تلف
            {% endif %}
        </div>

        <!-- معلومات الإذن -->
        <div class="info-section">
            <div class="info-group">
                <div class="info-row">
                    <span class="info-label">رقم الإذن:</span>
                    <span class="info-value">{{ issue.issue_number }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">نوع الصرف:</span>
                    <span class="info-value">
                        <span class="issue-type-badge {% if issue.issue_type == 'SALE' %}badge-sale{% else %}badge-damage{% endif %}">
                            {{ issue.get_issue_type_display }}
                        </span>
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">المخزن:</span>
                    <span class="info-value">{{ issue.warehouse.warehouse_name }}</span>
                </div>
            </div>
            
            <div class="info-group">
                <div class="info-row">
                    <span class="info-label">التاريخ:</span>
                    <span class="info-value">{{ issue.issue_date|date:"Y/m/d H:i" }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">المسؤول:</span>
                    <span class="info-value">{{ issue.created_by.get_full_name|default:issue.created_by.username }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">الحالة:</span>
                    <span class="info-value">{{ issue.get_status_display }}</span>
                </div>
            </div>
        </div>

        <!-- تفاصيل الأصناف -->
        <div class="items-section">
            <div class="section-title">تفاصيل الأصناف</div>
            
            <table class="items-table">
                <thead>
                    <tr>
                        <th>م</th>
                        <th>كود الصنف</th>
                        <th>اسم الصنف</th>
                        <th>الكمية</th>
                        <th>الوحدة</th>
                        <th>موقع التخزين</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in issue.issue_items.all %}
                    <tr>
                        <td>{{ forloop.counter }}</td>
                        <td>{{ item.item.item_code }}</td>
                        <td>{{ item.item.item_name_ar }}</td>
                        <td>{{ item.quantity|floatformat:3 }}</td>
                        <td>{{ item.unit_of_measure }}</td>
                        <td>{{ item.bin_location.location_code|default:"-" }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6">لا توجد أصناف مسجلة</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- المجاميع -->
        <div class="totals-section">
            <div class="totals-row">
                <span>عدد الأصناف:</span>
                <span>{{ issue.total_items }} صنف</span>
            </div>
            <div class="totals-row">
                <span>إجمالي الكمية:</span>
                <span>{{ issue.total_quantity|floatformat:3 }}</span>
            </div>
        </div>

        <!-- الملاحظات -->
        {% if issue.notes %}
        <div class="notes-section">
            <div class="section-title">الملاحظات</div>
            <div class="notes-content">
                {{ issue.notes|linebreaks }}
            </div>
        </div>
        {% endif %}

        <!-- التوقيعات -->
        <div class="signatures-section">
            <div class="section-title">التوقيعات</div>
            
            <table class="signatures-table">
                <thead>
                    <tr>
                        <th>المسؤول عن المخزن</th>
                        <th>مدير المخزون</th>
                        <th>المدير العام</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <div class="signature-line"></div>
                            <div>التوقيع: _______________</div>
                            <div>التاريخ: _______________</div>
                        </td>
                        <td>
                            <div class="signature-line"></div>
                            <div>التوقيع: _______________</div>
                            <div>التاريخ: _______________</div>
                        </td>
                        <td>
                            <div class="signature-line"></div>
                            <div>التوقيع: _______________</div>
                            <div>التاريخ: _______________</div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- التذييل -->
        <div class="footer">
            تم إنشاء هذا الإذن بواسطة نظام KamaVerse في {{ 'now'|date:'Y/m/d H:i' }}
        </div>
    </div>
</body>
</html>
