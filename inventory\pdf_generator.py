"""
مولد PDF لإيصالات الاستلام
PDF Generator for Goods Receipts
"""

import os
from io import BytesIO
from datetime import datetime
from decimal import Decimal

from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT

from django.conf import settings
from django.http import HttpResponse
from django.template.loader import render_to_string


class GoodsReceiptPDFGenerator:
    """مولد PDF لإيصالات الاستلام"""
    
    def __init__(self):
        self.setup_fonts()
        self.setup_styles()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        # استخدام خط افتراضي يدعم Unicode
        self.arabic_font = 'Helvetica'
    
    def setup_styles(self):
        """إعداد أنماط النص"""
        self.styles = getSampleStyleSheet()
        
        # نمط العنوان الرئيسي
        self.title_style = ParagraphStyle(
            'CustomTitle',
            parent=self.styles['Heading1'],
            fontName=self.arabic_font,
            fontSize=18,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#D62828'),
            spaceAfter=20
        )
        
        # نمط العنوان الفرعي
        self.subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=self.styles['Heading2'],
            fontName=self.arabic_font,
            fontSize=14,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#666666'),
            spaceAfter=15
        )
        
        # نمط النص العادي
        self.normal_style = ParagraphStyle(
            'CustomNormal',
            parent=self.styles['Normal'],
            fontName=self.arabic_font,
            fontSize=10,
            alignment=TA_RIGHT,
            rightIndent=0,
            leftIndent=0
        )
    
    def generate_receipt_pdf(self, receipt, response=None):
        """توليد PDF لإذن الاستلام"""
        if response is None:
            response = HttpResponse(content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="receipt_{receipt.receipt_number}.pdf"'
        
        # إنشاء المستند
        doc = SimpleDocTemplate(
            response,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )
        
        # محتوى المستند
        story = []
        
        # رأس الشركة
        story.append(Paragraph("شركة القماش للاستيراد والتصدير", self.title_style))
        story.append(Paragraph("Elkamash Import & Export Company", self.subtitle_style))
        story.append(Spacer(1, 20))
        
        # عنوان الإذن
        story.append(Paragraph(f"إذن استلام رقم: {receipt.receipt_number}", self.title_style))
        story.append(Spacer(1, 15))
        
        # معلومات الإذن
        info_data = [
            ['التاريخ:', receipt.receipt_date.strftime('%Y-%m-%d %H:%M')],
            ['المخزن:', receipt.warehouse.warehouse_name],
            ['المسؤول:', receipt.created_by.get_full_name() or receipt.created_by.username],
            ['الحالة:', receipt.get_status_display()],
        ]
        
        info_table = Table(info_data, colWidths=[3*cm, 6*cm])
        info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.HexColor('#f8f9fa')),
        ]))
        
        story.append(info_table)
        story.append(Spacer(1, 20))
        
        # جدول الأصناف
        if receipt.receipt_items.exists():
            # رأس الجدول
            headers = ['م', 'كود الصنف', 'اسم الصنف', 'الكمية', 'الوحدة', 'موقع التخزين', 'رقم الدفعة']
            table_data = [headers]
            
            # بيانات الأصناف
            for i, item in enumerate(receipt.receipt_items.all(), 1):
                row = [
                    str(i),
                    item.item.item_code,
                    item.item.item_name_ar,
                    str(item.quantity),
                    item.item.unit_of_measure,
                    item.bin_location.bin_code,
                    item.batch_number or '-'
                ]
                table_data.append(row)
            
            # إنشاء الجدول
            items_table = Table(table_data, colWidths=[1*cm, 2*cm, 4*cm, 2*cm, 2*cm, 2*cm, 2*cm])
            items_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#D62828')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#f8f9fa')),
            ]))
            
            story.append(items_table)
            story.append(Spacer(1, 20))
        
        # المجاميع
        totals_data = [
            ['عدد الأصناف:', str(receipt.total_items)],
            ['إجمالي الكمية:', str(receipt.total_quantity)],
        ]
        
        totals_table = Table(totals_data, colWidths=[4*cm, 3*cm])
        totals_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#D62828')),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.whitesmoke),
        ]))
        
        story.append(totals_table)
        story.append(Spacer(1, 30))
        
        # الملاحظات
        if receipt.notes:
            story.append(Paragraph("ملاحظات:", self.subtitle_style))
            story.append(Paragraph(receipt.notes, self.normal_style))
            story.append(Spacer(1, 20))
        
        # التوقيعات
        signatures_data = [
            ['مسؤول المخزن', 'مدير المخازن', 'المحاسب'],
            ['التوقيع: _______________', 'التوقيع: _______________', 'التوقيع: _______________']
        ]
        
        signatures_table = Table(signatures_data, colWidths=[5*cm, 5*cm, 5*cm])
        signatures_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#f8f9fa')),
        ]))
        
        story.append(signatures_table)
        
        # بناء المستند
        doc.build(story)
        return response
    
    def generate_daily_report(self, date, receipts, response=None):
        """توليد تقرير يومي"""
        if response is None:
            response = HttpResponse(content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="daily_report_{date.strftime("%Y-%m-%d")}.pdf"'
        
        doc = SimpleDocTemplate(response, pagesize=A4)
        story = []
        
        # العنوان
        story.append(Paragraph(f"تقرير إيصالات الاستلام اليومي - {date.strftime('%Y-%m-%d')}", self.title_style))
        story.append(Spacer(1, 20))
        
        if receipts:
            # جدول الإيصالات
            headers = ['رقم الإذن', 'المخزن', 'عدد الأصناف', 'إجمالي الكمية', 'المسؤول', 'الوقت']
            table_data = [headers]
            
            for receipt in receipts:
                row = [
                    receipt.receipt_number,
                    receipt.warehouse.warehouse_name,
                    str(receipt.total_items),
                    str(receipt.total_quantity),
                    receipt.created_by.get_full_name() or receipt.created_by.username,
                    receipt.receipt_date.strftime('%H:%M')
                ]
                table_data.append(row)
            
            table = Table(table_data)
            table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#D62828')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ]))
            
            story.append(table)
        else:
            story.append(Paragraph("لا توجد إيصالات استلام في هذا التاريخ", self.normal_style))
        
        doc.build(story)
        return response
    
    def generate_monthly_report(self, year, month, receipts, response=None):
        """توليد تقرير شهري"""
        if response is None:
            response = HttpResponse(content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="monthly_report_{year}-{month:02d}.pdf"'
        
        doc = SimpleDocTemplate(response, pagesize=A4)
        story = []
        
        # العنوان
        story.append(Paragraph(f"تقرير إيصالات الاستلام الشهري - {year}/{month:02d}", self.title_style))
        story.append(Spacer(1, 20))
        
        if receipts:
            # إحصائيات عامة
            total_receipts = receipts.count()
            total_items = sum(r.total_items for r in receipts)
            total_quantity = sum(r.total_quantity for r in receipts)
            
            stats_data = [
                ['إجمالي الإيصالات:', str(total_receipts)],
                ['إجمالي الأصناف:', str(total_items)],
                ['إجمالي الكمية:', str(total_quantity)],
            ]
            
            stats_table = Table(stats_data, colWidths=[4*cm, 3*cm])
            stats_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 12),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#D62828')),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.whitesmoke),
            ]))
            
            story.append(stats_table)
            story.append(Spacer(1, 20))
            
            # جدول تفصيلي
            headers = ['التاريخ', 'رقم الإذن', 'المخزن', 'عدد الأصناف', 'إجمالي الكمية', 'المسؤول']
            table_data = [headers]
            
            for receipt in receipts:
                row = [
                    receipt.receipt_date.strftime('%Y-%m-%d'),
                    receipt.receipt_number,
                    receipt.warehouse.warehouse_name,
                    str(receipt.total_items),
                    str(receipt.total_quantity),
                    receipt.created_by.get_full_name() or receipt.created_by.username,
                ]
                table_data.append(row)
            
            table = Table(table_data)
            table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#D62828')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ]))
            
            story.append(table)
        else:
            story.append(Paragraph("لا توجد إيصالات استلام في هذا الشهر", self.normal_style))
        
        doc.build(story)
        return response

    def generate_custom_report(self, start_date, end_date, receipts, response=None):
        """توليد تقرير مخصص"""
        if response is None:
            response = HttpResponse(content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="custom_report_{start_date}_{end_date}.pdf"'

        doc = SimpleDocTemplate(response, pagesize=A4)
        story = []

        # العنوان
        story.append(Paragraph(f"تقرير إيصالات الاستلام من {start_date} إلى {end_date}", self.title_style))
        story.append(Spacer(1, 20))

        if receipts:
            # إحصائيات عامة
            total_receipts = receipts.count()
            total_items = sum(r.total_items for r in receipts)
            total_quantity = sum(r.total_quantity for r in receipts)

            stats_data = [
                ['الفترة:', f'{start_date} - {end_date}'],
                ['إجمالي الإيصالات:', str(total_receipts)],
                ['إجمالي الأصناف:', str(total_items)],
                ['إجمالي الكمية:', str(total_quantity)],
            ]

            stats_table = Table(stats_data, colWidths=[4*cm, 4*cm])
            stats_table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 12),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#D62828')),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.whitesmoke),
            ]))

            story.append(stats_table)
            story.append(Spacer(1, 20))

            # جدول تفصيلي
            headers = ['التاريخ', 'رقم الإذن', 'المخزن', 'عدد الأصناف', 'إجمالي الكمية', 'المسؤول']
            table_data = [headers]

            for receipt in receipts:
                row = [
                    receipt.receipt_date.strftime('%Y-%m-%d'),
                    receipt.receipt_number,
                    receipt.warehouse.warehouse_name,
                    str(receipt.total_items),
                    str(receipt.total_quantity),
                    receipt.created_by.get_full_name() or receipt.created_by.username,
                ]
                table_data.append(row)

            table = Table(table_data)
            table.setStyle(TableStyle([
                ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#D62828')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ]))

            story.append(table)
        else:
            story.append(Paragraph("لا توجد إيصالات استلام في هذه الفترة", self.normal_style))

        doc.build(story)
        return response

    def generate_goods_issue_pdf(self, issue, response):
        """توليد PDF لإذن الصرف"""

        # إعداد المستند
        doc = SimpleDocTemplate(
            response,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=2*cm,
            bottomMargin=2*cm
        )

        story = []

        # رأس الشركة
        story.append(Paragraph("شركة القماش للاستيراد والتصدير", self.title_style))
        story.append(Paragraph("KamaVerse Import & Export Company", self.subtitle_style))
        story.append(Spacer(1, 0.5*cm))

        # عنوان الإذن
        issue_type_ar = "إذن صرف - مبيعات" if issue.issue_type == 'SALE' else "إذن صرف - تلف"
        story.append(Paragraph(issue_type_ar, self.title_style))
        story.append(Spacer(1, 0.3*cm))

        # معلومات الإذن
        issue_info = [
            ['رقم الإذن:', issue.issue_number, 'التاريخ:', issue.issue_date.strftime('%Y/%m/%d %H:%M')],
            ['نوع الصرف:', issue.get_issue_type_display(), 'المخزن:', issue.warehouse.warehouse_name],
            ['المسؤول:', issue.created_by.get_full_name() or issue.created_by.username, 'الحالة:', issue.get_status_display()],
        ]

        info_table = Table(issue_info, colWidths=[3*cm, 4*cm, 3*cm, 4*cm])
        info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),
        ]))

        story.append(info_table)
        story.append(Spacer(1, 0.5*cm))

        # جدول الأصناف
        story.append(Paragraph("تفاصيل الأصناف", self.subtitle_style))
        story.append(Spacer(1, 0.2*cm))

        # رأس الجدول
        table_data = [
            ['م', 'كود الصنف', 'اسم الصنف', 'الكمية', 'الوحدة', 'موقع التخزين']
        ]

        # بيانات الأصناف
        for i, item in enumerate(issue.issue_items.all(), 1):
            table_data.append([
                str(i),
                item.item.item_code,
                item.item.item_name_ar,
                f"{item.quantity:,.3f}",
                item.unit_of_measure,
                item.bin_location.location_code if item.bin_location else '-'
            ])

        # إنشاء الجدول
        items_table = Table(table_data, colWidths=[1*cm, 2.5*cm, 5*cm, 2*cm, 2*cm, 2.5*cm])
        items_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ]))

        story.append(items_table)
        story.append(Spacer(1, 0.5*cm))

        # المجاميع
        totals_data = [
            ['إجمالي عدد الأصناف:', f"{issue.total_items} صنف"],
            ['إجمالي الكمية:', f"{issue.total_quantity:,.3f}"],
        ]

        totals_table = Table(totals_data, colWidths=[4*cm, 4*cm])
        totals_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOX', (0, 0), (-1, -1), 2, colors.black),
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ]))

        story.append(totals_table)
        story.append(Spacer(1, 1*cm))

        # الملاحظات
        if issue.notes:
            story.append(Paragraph("ملاحظات:", self.subtitle_style))
            story.append(Paragraph(issue.notes, self.normal_style))
            story.append(Spacer(1, 0.5*cm))

        # التوقيعات
        signatures_data = [
            ['المسؤول عن المخزن', 'مدير المخزون', 'المدير العام'],
            ['', '', ''],
            ['التوقيع: _______________', 'التوقيع: _______________', 'التوقيع: _______________'],
            ['التاريخ: _______________', 'التاريخ: _______________', 'التاريخ: _______________'],
        ]

        signatures_table = Table(signatures_data, colWidths=[5*cm, 5*cm, 5*cm])
        signatures_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), self.arabic_font),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOX', (0, 0), (-1, -1), 1, colors.black),
            ('INNERGRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ]))

        story.append(signatures_table)

        # تذييل الصفحة
        story.append(Spacer(1, 1*cm))
        footer_text = f"تم إنشاء هذا الإذن بواسطة نظام KamaVerse في {datetime.now().strftime('%Y/%m/%d %H:%M')}"
        story.append(Paragraph(footer_text, self.normal_style))

        # بناء المستند
        doc.build(story)
        return response


# مثيل عام للاستخدام
pdf_generator = GoodsReceiptPDFGenerator()
