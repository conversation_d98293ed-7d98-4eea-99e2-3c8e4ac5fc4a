{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/kamaverse.css' %}">
<style>
    .form-section {
        background: white;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .section-title {
        color: #D62828;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #C89A3C;
    }
    
    .warehouse-info {
        background: linear-gradient(135deg, #D62828 0%, #B91C1C 100%);
        color: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .warehouse-name {
        font-size: 1.1rem;
        font-weight: bold;
        color: #C89A3C;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #C89A3C;
        box-shadow: 0 0 0 0.2rem rgba(200, 154, 60, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #D62828 0%, #B91C1C 100%);
        border: none;
        padding: 12px 30px;
        font-weight: 500;
    }
    
    .btn-primary:hover {
        background: linear-gradient(135deg, #B91C1C 0%, #991B1B 100%);
        transform: translateY(-1px);
    }
    
    .quantity-info {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 10px;
        margin-top: 10px;
        font-size: 0.9rem;
    }
    
    .required-field::after {
        content: " *";
        color: #D62828;
    }

    /* تحسينات الجدول المبسط */
    .items-table-container {
        margin-top: 20px;
    }

    .table th {
        background-color: #D62828;
        color: white;
        font-weight: 600;
        border: none;
        text-align: center;
        vertical-align: middle;
    }

    .table td {
        vertical-align: middle;
        border-color: #dee2e6;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .form-select, .form-control {
        border-radius: 8px;
        border: 1px solid #d1d5db;
        transition: all 0.3s ease;
    }

    .btn-outline-danger {
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-outline-danger:hover {
        transform: scale(1.05);
    }

    .unit-display {
        font-size: 0.9rem;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 6px;
        background-color: #f3f4f6;
        display: inline-block;
        min-width: 60px;
        text-align: center;
    }

    .unit-display.active {
        background-color: #dbeafe;
        color: #1e40af;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="text-primary mb-1">
                <i class="fas fa-plus-circle me-2"></i>{{ page_title }}
            </h2>
            <p class="text-muted mb-0">إنشاء طلب نقل مخزون من {{ user_profile.assigned_warehouse.warehouse_name }}</p>
        </div>
        <a href="{% url 'inventory:stock_transfer_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
        </a>
    </div>

    <form method="post" id="transferForm">
        {% csrf_token %}
        
        <!-- Source Warehouse Info -->
        <div class="warehouse-info">
            <div class="row">
                <div class="col-md-6">
                    <div class="warehouse-name">المخزن المصدر</div>
                    <div>{{ user_profile.assigned_warehouse.warehouse_name }}</div>
                    <small>{{ user_profile.assigned_warehouse.address }}</small>
                </div>
                <div class="col-md-6 text-end">
                    <div class="text-muted">مسؤول المخزن</div>
                    <div>{{ user_profile.user.get_full_name }}</div>
                </div>
            </div>
        </div>

        <!-- Transfer Details -->
        <div class="form-section">
            <h5 class="section-title">
                <i class="fas fa-info-circle me-2"></i>تفاصيل النقل
            </h5>

            <div class="row">
                <div class="col-md-6">
                    <label for="reason" class="form-label required-field">سبب النقل</label>
                    <input type="text" name="reason" id="reason" class="form-control"
                           placeholder="مثال: نقص في المخزون، طلب عاجل، إعادة توزيع" required>
                </div>

                <div class="col-md-6">
                    <label for="notes" class="form-label">ملاحظات إضافية</label>
                    <textarea name="notes" id="notes" class="form-control" rows="2"
                              placeholder="أي ملاحظات إضافية (اختياري)"></textarea>
                </div>
            </div>
        </div>

        <!-- Items Section -->
        <div class="form-section">
            <h5 class="section-title">
                <i class="fas fa-list me-2"></i>الأصناف المطلوبة
            </h5>

            <div class="items-table-container">
                <table class="table table-bordered" id="itemsTable">
                    <thead class="table-dark">
                        <tr>
                            <th width="50%">الصنف *</th>
                            <th width="20%">الكمية المطلوبة *</th>
                            <th width="15%">الوحدة</th>
                            <th width="15%">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="itemsTableBody">
                        <!-- سيتم إضافة الصفوف هنا بـ JavaScript -->
                    </tbody>
                </table>

                <button type="button" class="btn btn-outline-primary" onclick="addItemRow()">
                    <i class="fas fa-plus me-2"></i>إضافة صنف
                </button>
            </div>
        </div>



        <!-- Submit Buttons -->
        <div class="text-center">
            <button type="submit" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-paper-plane me-2"></i>إرسال طلب النقل
            </button>
            <a href="{% url 'inventory:stock_transfer_list' %}" class="btn btn-outline-secondary btn-lg">
                <i class="fas fa-times me-2"></i>إلغاء
            </a>
        </div>
    </form>
</div>

<script>
let itemRowCount = 0;

// بيانات الأصناف والمواقع
const itemsData = {
    {% for item in items %}
    '{{ item.id }}': {
        'name': '{{ item.item_name_ar }}',
        'code': '{{ item.item_code }}',
        'unit': '{{ item.unit_of_measure }}'
    },
    {% endfor %}
};

const binLocationsData = [
    {% for location in source_bin_locations %}
    {
        'id': '{{ location.id }}',
        'code': '{{ location.bin_code }}',
        'name': '{{ location.bin_name|default:"" }}'
    },
    {% endfor %}
];

document.addEventListener('DOMContentLoaded', function() {
    // إضافة صف أول تلقائياً
    addItemRow();

    // التحقق من النموذج قبل الإرسال
    document.getElementById('transferForm').addEventListener('submit', function(e) {
        const itemRows = document.querySelectorAll('#itemsTableBody tr');
        if (itemRows.length === 0) {
            e.preventDefault();
            alert('يرجى إضافة صنف واحد على الأقل');
            return;
        }

        let hasValidItems = false;
        itemRows.forEach(row => {
            const itemSelect = row.querySelector('select[name^="item_"]');
            const quantityInput = row.querySelector('input[name^="quantity_"]');

            if (itemSelect.value && quantityInput.value && parseFloat(quantityInput.value) > 0) {
                hasValidItems = true;
            }
        });

        if (!hasValidItems) {
            e.preventDefault();
            alert('يرجى إدخال بيانات صحيحة لصنف واحد على الأقل');
        }
    });
});

function addItemRow() {
    itemRowCount++;
    const tbody = document.getElementById('itemsTableBody');

    const row = document.createElement('tr');
    row.innerHTML = `
        <td>
            <select name="item_${itemRowCount}" class="form-select" required onchange="updateItemInfo(this, ${itemRowCount})">
                <option value="">اختر الصنف</option>
                {% for item in items %}
                <option value="{{ item.id }}" data-unit="{{ item.unit_of_measure }}">
                    {{ item.item_name_ar }} ({{ item.item_code }})
                </option>
                {% endfor %}
            </select>
        </td>
        <td>
            <input type="number" name="quantity_${itemRowCount}" class="form-control"
                   step="0.001" min="0.001" required placeholder="0.000">
        </td>
        <td class="text-center">
            <span id="unit_${itemRowCount}" class="unit-display">-</span>
        </td>
        <td class="text-center">
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItemRow(this)" title="مسح الصنف">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

    tbody.appendChild(row);
}

function removeItemRow(button) {
    const row = button.closest('tr');
    const tbody = document.getElementById('itemsTableBody');

    if (tbody.children.length > 1) {
        row.remove();
    } else {
        alert('يجب أن يحتوي الطلب على صنف واحد على الأقل');
    }
}

function updateItemInfo(selectElement, rowNumber) {
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const unitSpan = document.getElementById(`unit_${rowNumber}`);

    if (selectedOption.value) {
        const unit = selectedOption.dataset.unit;
        unitSpan.textContent = unit;
        unitSpan.classList.add('active');
    } else {
        unitSpan.textContent = '-';
        unitSpan.classList.remove('active');
    }
}
</script>
{% endblock %}
