"""
Authentication Forms for KamaVerse
نماذج المصادقة لنظام KamaVerse
"""

from django import forms
from django.contrib.auth.forms import AuthenticationForm
from django.core.validators import RegexValidator
from core.models import KeyUser


class LoginForm(forms.Form):
    """
    نموذج تسجيل الدخول
    """
    username = forms.CharField(
        max_length=150,
        widget=forms.TextInput(attrs={
            'class': 'form-control form-control-lg',
            'placeholder': 'اسم المستخدم',
            'dir': 'ltr',
            'autocomplete': 'username',
        }),
        label='اسم المستخدم',
        error_messages={
            'required': 'يرجى إدخال اسم المستخدم',
            'max_length': 'اسم المستخدم طويل جداً'
        }
    )
    
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control form-control-lg',
            'placeholder': 'كلمة المرور',
            'dir': 'ltr',
            'autocomplete': 'current-password',
        }),
        label='كلمة المرور',
        error_messages={
            'required': 'يرجى إدخال كلمة المرور'
        }
    )
    
    remember_me = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'form-check-input',
        }),
        label='تذكرني'
    )
    
    def clean_username(self):
        """التحقق من صحة اسم المستخدم"""
        username = self.cleaned_data.get('username')
        if username:
            username = username.strip().lower()
        return username
    
    def clean(self):
        """التحقق العام من النموذج"""
        cleaned_data = super().clean()
        username = cleaned_data.get('username')
        password = cleaned_data.get('password')
        
        if username and password:
            # التحقق من وجود المستخدم
            try:
                user = KeyUser.objects.get(username=username)
                if not user.is_active:
                    raise forms.ValidationError('هذا الحساب غير نشط')
            except KeyUser.DoesNotExist:
                raise forms.ValidationError('اسم المستخدم أو كلمة المرور خاطئة')
        
        return cleaned_data


class ChangePasswordForm(forms.Form):
    """
    نموذج تغيير كلمة المرور
    """
    current_password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'كلمة المرور الحالية',
            'dir': 'ltr',
        }),
        label='كلمة المرور الحالية',
        error_messages={
            'required': 'يرجى إدخال كلمة المرور الحالية'
        }
    )
    
    new_password = forms.CharField(
        min_length=8,
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'كلمة المرور الجديدة',
            'dir': 'ltr',
        }),
        label='كلمة المرور الجديدة',
        error_messages={
            'required': 'يرجى إدخال كلمة المرور الجديدة',
            'min_length': 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'
        }
    )
    
    confirm_password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'تأكيد كلمة المرور الجديدة',
            'dir': 'ltr',
        }),
        label='تأكيد كلمة المرور الجديدة',
        error_messages={
            'required': 'يرجى تأكيد كلمة المرور الجديدة'
        }
    )
    
    def __init__(self, user, *args, **kwargs):
        self.user = user
        super().__init__(*args, **kwargs)
    
    def clean_current_password(self):
        """التحقق من كلمة المرور الحالية"""
        current_password = self.cleaned_data.get('current_password')
        if current_password and not self.user.check_password(current_password):
            raise forms.ValidationError('كلمة المرور الحالية خاطئة')
        return current_password
    
    def clean(self):
        """التحقق من تطابق كلمة المرور الجديدة"""
        cleaned_data = super().clean()
        new_password = cleaned_data.get('new_password')
        confirm_password = cleaned_data.get('confirm_password')
        
        if new_password and confirm_password:
            if new_password != confirm_password:
                raise forms.ValidationError('كلمة المرور الجديدة وتأكيدها غير متطابقتين')
        
        return cleaned_data


class ProfileUpdateForm(forms.ModelForm):
    """
    نموذج تحديث الملف الشخصي
    """
    
    class Meta:
        model = KeyUser
        fields = ['first_name', 'last_name', 'email', 'phone_number']
        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'الاسم الأول'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'اسم العائلة'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'البريد الإلكتروني',
                'dir': 'ltr'
            }),
            'phone_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'رقم الهاتف',
                'dir': 'ltr'
            }),
        }
        labels = {
            'first_name': 'الاسم الأول',
            'last_name': 'اسم العائلة',
            'email': 'البريد الإلكتروني',
            'phone_number': 'رقم الهاتف',
        }
        error_messages = {
            'email': {
                'invalid': 'يرجى إدخال بريد إلكتروني صحيح'
            },
            'phone_number': {
                'invalid': 'يرجى إدخال رقم هاتف صحيح'
            }
        }
    
    def clean_email(self):
        """التحقق من عدم تكرار البريد الإلكتروني"""
        email = self.cleaned_data.get('email')
        if email:
            # التحقق من عدم استخدام البريد من قبل مستخدم آخر
            existing_user = KeyUser.objects.filter(email=email).exclude(pk=self.instance.pk).first()
            if existing_user:
                raise forms.ValidationError('هذا البريد الإلكتروني مستخدم من قبل مستخدم آخر')
        return email
