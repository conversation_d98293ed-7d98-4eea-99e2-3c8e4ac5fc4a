{% extends 'base.html' %}
{% load static %}

{% block title %}{{ warehouse.warehouse_name }} - تفاصيل المخزن - KamaVerse{% endblock %}

{% block extra_css %}
<link href="{% static 'css/warehouses.css' %}" rel="stylesheet">
<style>
/* Header Styles */
.warehouse-header {
    background: linear-gradient(135deg, var(--brand-red) 0%, var(--brand-red-dark) 100%);
    color: var(--white);
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    position: relative;
}

.warehouse-header h1 {
    margin: 0;
    font-weight: 700;
    font-size: 2.5rem;
}

.warehouse-code-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.warehouse-info {
    display: flex;
    gap: 2rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-item i {
    opacity: 0.8;
}

/* Stats Cards */
.stats-section {
    margin-bottom: 2rem;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--light-gray);
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    display: block;
}

.stat-label {
    color: #666;
    font-size: 0.95rem;
    font-weight: 500;
}

.stat-occupied { color: var(--brand-red); }
.stat-available { color: #28a745; }
.stat-items { color: #007bff; }
.stat-locations { color: #6f42c1; }

/* Progress Bar */
.occupancy-progress {
    background: #e9ecef;
    border-radius: 10px;
    height: 20px;
    overflow: hidden;
    margin: 1rem 0;
}

.occupancy-fill {
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.occupancy-fill.low { background: linear-gradient(90deg, #28a745, #20c997); }
.occupancy-fill.medium { background: linear-gradient(90deg, #ffc107, #fd7e14); }
.occupancy-fill.high { background: linear-gradient(90deg, #dc3545, #c82333); }

/* Items Table */
.items-section {
    background: var(--white);
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.section-header {
    background: var(--light-gray);
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;
}

.section-header h3 {
    margin: 0;
    color: var(--charcoal);
    font-weight: 600;
}

.items-table {
    width: 100%;
    margin: 0;
}

.items-table th,
.items-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
}

.items-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: var(--charcoal);
    font-size: 0.95rem;
}

.items-table tbody tr:hover {
    background: #f8f9fa;
}

.item-code {
    font-family: 'Courier New', monospace;
    background: #e9ecef;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 600;
}

.item-name {
    font-weight: 600;
    color: var(--charcoal);
}

.item-name-en {
    font-size: 0.85rem;
    color: #666;
    font-style: italic;
    margin-top: 0.25rem;
}

.quantity-badge {
    background: var(--brand-red);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.9rem;
}

.location-badge {
    background: #007bff;
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.9rem;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Responsive */
@media (max-width: 768px) {
    .warehouse-header {
        padding: 1.5rem;
    }
    
    .warehouse-header h1 {
        font-size: 2rem;
    }
    
    .warehouse-info {
        flex-direction: column;
        gap: 1rem;
    }
    
    .stats-row {
        grid-template-columns: 1fr;
    }
    
    .items-table {
        font-size: 0.9rem;
    }
    
    .items-table th,
    .items-table td {
        padding: 0.75rem 0.5rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Warehouse Header -->
<div class="warehouse-header">
    <div class="warehouse-code-badge">{{ warehouse.warehouse_code }}</div>
    <h1>{{ warehouse.warehouse_name }}</h1>
    <div class="warehouse-info">
        <div class="info-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>{{ warehouse.address }}</span>
        </div>
        <div class="info-item">
            <i class="fas fa-user-tie"></i>
            <span>{{ warehouse.warehouse_manager|default:"غير محدد" }}</span>
        </div>
        <div class="info-item">
            <i class="fas fa-warehouse"></i>
            <span>{{ warehouse.get_warehouse_type_display }}</span>
        </div>
    </div>
</div>

<!-- Statistics Section -->
<div class="stats-section">
    <div class="stats-row">
        <div class="stat-card">
            <span class="stat-number stat-items">{{ total_items }}</span>
            <div class="stat-label">إجمالي الأصناف</div>
        </div>
        
        <div class="stat-card">
            <span class="stat-number stat-locations">{{ total_locations }}</span>
            <div class="stat-label">مواقع التخزين</div>
        </div>
        
        <div class="stat-card">
            <span class="stat-number stat-occupied">{{ occupancy_percentage|floatformat:1 }}%</span>
            <div class="stat-label">نسبة الإشغال</div>
            <div class="occupancy-progress">
                <div class="occupancy-fill {% if occupancy_percentage <= 30 %}low{% elif occupancy_percentage <= 70 %}medium{% else %}high{% endif %}" 
                     style="width: {{ occupancy_percentage }}%"></div>
            </div>
        </div>
        
        <div class="stat-card">
            <span class="stat-number stat-available">{{ available_percentage|floatformat:1 }}%</span>
            <div class="stat-label">نسبة الفراغ</div>
        </div>
    </div>
</div>

<!-- Items Table Section -->
<div class="items-section">
    <div class="section-header">
        <div class="d-flex justify-content-between align-items-center">
            <h3>
                <i class="fas fa-boxes me-2"></i>
                الأصناف الموجودة في المخزن
            </h3>
            <a href="{% url 'inventory:warehouse_add_items' warehouse.pk %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة أصناف للمخزن
            </a>
        </div>
    </div>

    {% if stock_items %}
        <table class="items-table">
            <thead>
                <tr>
                    <th>كود الصنف</th>
                    <th>اسم الصنف</th>
                    <th>الكمية</th>
                    <th>الوحدة</th>
                    <th>موقع التخزين</th>
                </tr>
            </thead>
            <tbody>
                {% for stock in stock_items %}
                    <tr>
                        <td>
                            <span class="item-code">{{ stock.item.item_code }}</span>
                        </td>
                        <td>
                            <div class="item-name">{{ stock.item.item_name_ar }}</div>
                            {% if stock.item.item_name_en %}
                                <div class="item-name-en">{{ stock.item.item_name_en }}</div>
                            {% endif %}
                        </td>
                        <td>
                            <span class="quantity-badge">{{ stock.current_quantity|floatformat:0 }}</span>
                        </td>
                        <td>{{ stock.item.get_unit_of_measure_display }}</td>
                        <td>
                            <span class="location-badge">{{ stock.bin_location.bin_code }}</span>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <div class="empty-state">
            <i class="fas fa-box-open"></i>
            <h4>لا توجد أصناف في هذا المخزن</h4>
            <p>لم يتم تخزين أي أصناف في هذا المخزن بعد</p>
        </div>
    {% endif %}
</div>

<!-- Back Button -->
<div class="text-center mt-4">
    <a href="{% url 'inventory:warehouses_list' %}" class="btn btn-secondary">
        <i class="fas fa-arrow-right me-2"></i>
        العودة لقائمة المخازن
    </a>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any interactive functionality here
    console.log('Warehouse detail page loaded');
});
</script>
{% endblock %}
