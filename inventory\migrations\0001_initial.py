# Generated by Django 5.1 on 2025-08-18 14:08

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BinLocation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('bin_code', models.CharField(help_text='مثال: A1, B1, C1...', max_length=20, verbose_name='كود الموقع')),
                ('bin_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم الموقع')),
                ('aisle', models.CharField(help_text='مثال: A, B, C', max_length=10, verbose_name='الممر')),
                ('rack', models.CharField(help_text='مثال: 1, 2, 3', max_length=10, verbose_name='الرف')),
                ('level', models.CharField(blank=True, help_text='مثال: 1, 2, 3', max_length=10, null=True, verbose_name='المستوى')),
                ('capacity', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='السعة (متر مكعب)')),
                ('current_quantity', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية الحالية')),
                ('status', models.CharField(choices=[('AVAILABLE', 'متاح'), ('OCCUPIED', 'مشغول'), ('MAINTENANCE', 'تحت الصيانة'), ('BLOCKED', 'محجوز'), ('DAMAGED', 'تالف')], default='AVAILABLE', max_length=20, verbose_name='حالة الموقع')),
                ('temperature_zone', models.CharField(blank=True, max_length=50, null=True, verbose_name='منطقة الحرارة')),
                ('hazard_zone', models.CharField(blank=True, max_length=50, null=True, verbose_name='منطقة الخطورة')),
                ('last_movement_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ آخر حركة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'موقع تخزين',
                'verbose_name_plural': 'مواقع التخزين',
                'ordering': ['warehouse', 'aisle', 'rack', 'level'],
            },
        ),
        migrations.CreateModel(
            name='ItemMaster',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('item_code', models.CharField(help_text='كود تلقائي مع prefix حسب النوع', max_length=50, unique=True, verbose_name='كود الصنف')),
                ('item_name_ar', models.CharField(max_length=200, verbose_name='اسم الصنف بالعربية')),
                ('item_name_en', models.CharField(blank=True, max_length=200, null=True, verbose_name='الاسم العلمي/الإنجليزي')),
                ('category', models.CharField(choices=[('PLASTIC_RAW', 'خامات بلاستيك'), ('RUBBER_RAW', 'خامات مطاط'), ('CHEMICALS', 'مواد كيماوية'), ('ADDITIVES', 'إضافات'), ('COLORANTS', 'ملونات'), ('STABILIZERS', 'مثبتات'), ('FILLERS', 'حشوات'), ('RESINS', 'راتنجات'), ('OILS', 'زيوت'), ('ACIDS', 'أحماض'), ('OXIDES', 'أكاسيد'), ('FINISHED', 'منتجات نهائية')], max_length=20, verbose_name='مجموعة المواد')),
                ('material_type', models.CharField(choices=[('RAW_MATERIAL', 'مادة خام'), ('FINISHED_PRODUCT', 'منتج نهائي'), ('AUXILIARY_MATERIAL', 'مادة مساعدة'), ('PACKAGING', 'مواد تعبئة')], max_length=20, verbose_name='نوع المادة')),
                ('unit_of_measure', models.CharField(choices=[('KG', 'كيلوجرام'), ('TON', 'طن'), ('LITER', 'لتر'), ('GALLON', 'جالون'), ('BAG', 'شيكارة'), ('DRUM', 'برميل'), ('CARTON', 'كرتونة'), ('PIECE', 'قطعة'), ('METER', 'متر'), ('ROLL', 'لفة'), ('CUBIC_METER', 'متر مكعب')], default='KG', max_length=20, verbose_name='الوحدة الأساسية')),
                ('minimum_stock_level', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الحد الأدنى للمخزون')),
                ('reorder_point', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='نقطة إعادة الطلب')),
                ('weight_per_unit', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='الوزن لكل وحدة')),
                ('density', models.DecimalField(blank=True, decimal_places=4, max_digits=8, null=True, verbose_name='الكثافة (جم/سم³)')),
                ('color', models.CharField(blank=True, max_length=50, null=True, verbose_name='اللون')),
                ('chemical_formula', models.CharField(blank=True, max_length=100, null=True, verbose_name='الصيغة الكيميائية')),
                ('cas_number', models.CharField(blank=True, help_text='Chemical Abstracts Service Number', max_length=20, null=True, verbose_name='رقم CAS')),
                ('purity_percentage', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='نسبة النقاء (%)')),
                ('hazard_level', models.CharField(choices=[('NONE', 'غير خطر'), ('LOW', 'خطورة منخفضة'), ('MEDIUM', 'خطورة متوسطة'), ('HIGH', 'خطورة عالية'), ('CRITICAL', 'خطورة حرجة')], default='NONE', max_length=20, verbose_name='مستوى الخطورة')),
                ('safety_data_sheet', models.FileField(blank=True, null=True, upload_to='items/sds/', verbose_name='ورقة بيانات الأمان')),
                ('storage_temperature_min', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='درجة الحرارة الدنيا (°م)')),
                ('storage_temperature_max', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='درجة الحرارة العليا (°م)')),
                ('humidity_max', models.DecimalField(blank=True, decimal_places=2, max_digits=5, null=True, verbose_name='الرطوبة القصوى (%)')),
                ('shelf_life_months', models.PositiveIntegerField(blank=True, null=True, verbose_name='مدة الصلاحية (شهر)')),
                ('item_image', models.ImageField(blank=True, null=True, upload_to='items/images/', verbose_name='صورة المادة')),
                ('barcode', models.CharField(blank=True, max_length=50, null=True, verbose_name='الباركود')),
                ('supplier_item_code', models.CharField(blank=True, max_length=50, null=True, verbose_name='كود الصنف عند المورد')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_items', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('default_bin_location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.binlocation', verbose_name='موقع التخزين الافتراضي')),
            ],
            options={
                'verbose_name': 'صنف',
                'verbose_name_plural': 'الأصناف',
                'ordering': ['item_name_ar'],
            },
        ),
        migrations.CreateModel(
            name='Warehouse',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('warehouse_code', models.CharField(max_length=20, unique=True, verbose_name='كود المخزن')),
                ('warehouse_name', models.CharField(max_length=100, verbose_name='اسم المخزن')),
                ('warehouse_type', models.CharField(choices=[('MAIN', 'مخزن رئيسي'), ('BRANCH', 'مخزن فرعي'), ('QUARANTINE', 'مخزن حجر صحي'), ('DAMAGED', 'مخزن تالف'), ('TRANSIT', 'مخزن عبور')], default='MAIN', max_length=20, verbose_name='نوع المخزن')),
                ('address', models.TextField(verbose_name='العنوان التفصيلي')),
                ('city', models.CharField(max_length=50, verbose_name='المدينة')),
                ('governorate', models.CharField(max_length=50, verbose_name='المحافظة')),
                ('postal_code', models.CharField(blank=True, max_length=10, null=True, verbose_name='الرمز البريدي')),
                ('total_capacity', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='السعة الإجمالية (متر مكعب)')),
                ('current_occupancy', models.DecimalField(decimal_places=2, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الإشغال الحالي (متر مكعب)')),
                ('temperature_controlled', models.BooleanField(default=False, verbose_name='مكيف الهواء')),
                ('humidity_controlled', models.BooleanField(default=False, verbose_name='تحكم في الرطوبة')),
                ('warehouse_manager', models.CharField(max_length=100, verbose_name='مسؤول المخزن')),
                ('manager_phone', models.CharField(max_length=20, verbose_name='هاتف المسؤول')),
                ('manager_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='بريد المسؤول')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_warehouses', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'مخزن',
                'verbose_name_plural': 'المخازن',
                'ordering': ['warehouse_name'],
            },
        ),
        migrations.CreateModel(
            name='StockMovement',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('movement_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الحركة')),
                ('movement_type', models.CharField(choices=[('RECEIPT', 'إذن استلام'), ('ISSUE', 'إذن صرف'), ('TRANSFER_IN', 'نقل وارد'), ('TRANSFER_OUT', 'نقل صادر'), ('ADJUSTMENT_IN', 'تسوية زيادة'), ('ADJUSTMENT_OUT', 'تسوية نقص'), ('OPENING_BALANCE', 'رصيد افتتاحي'), ('DAMAGE', 'تلف'), ('EXPIRED', 'منتهي الصلاحية')], max_length=20, verbose_name='نوع الحركة')),
                ('movement_date', models.DateTimeField(verbose_name='تاريخ الحركة')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=12, validators=[django.core.validators.MinValueValidator(0.001)], verbose_name='الكمية')),
                ('unit_of_measure', models.CharField(max_length=20, verbose_name='وحدة القياس')),
                ('batch_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الدفعة')),
                ('production_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإنتاج')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الصلاحية')),
                ('reference_document', models.CharField(blank=True, max_length=100, null=True, verbose_name='المستند المرجعي')),
                ('supplier_invoice', models.CharField(blank=True, max_length=50, null=True, verbose_name='فاتورة المورد')),
                ('container_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الحاوية')),
                ('unit_cost', models.DecimalField(blank=True, decimal_places=4, max_digits=12, null=True, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='إجمالي التكلفة')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('PENDING', 'في الانتظار'), ('APPROVED', 'معتمد'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي')], default='DRAFT', max_length=20, verbose_name='حالة الحركة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاعتماد')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_movements', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('bin_location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='movements', to='inventory.binlocation', verbose_name='موقع التخزين')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_movements', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='movements', to='inventory.itemmaster', verbose_name='الصنف')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='movements', to='inventory.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'حركة مخزون',
                'verbose_name_plural': 'حركات المخزون',
                'ordering': ['-movement_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockBalance',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('batch_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الدفعة')),
                ('production_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإنتاج')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الصلاحية')),
                ('current_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية الحالية')),
                ('reserved_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية المحجوزة')),
                ('available_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية المتاحة')),
                ('unit_of_measure', models.CharField(max_length=20, verbose_name='وحدة القياس')),
                ('average_cost', models.DecimalField(decimal_places=4, default=0, max_digits=12, validators=[django.core.validators.MinValueValidator(0)], verbose_name='متوسط التكلفة')),
                ('total_value', models.DecimalField(decimal_places=2, default=0, max_digits=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='إجمالي القيمة')),
                ('last_movement_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ آخر حركة')),
                ('last_updated', models.DateTimeField(auto_now=True, verbose_name='آخر تحديث')),
                ('bin_location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_balances', to='inventory.binlocation', verbose_name='موقع التخزين')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_balances', to='inventory.itemmaster', verbose_name='الصنف')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_balances', to='inventory.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'رصيد مخزون',
                'verbose_name_plural': 'أرصدة المخزون',
                'ordering': ['item', 'warehouse', 'bin_location'],
            },
        ),
        migrations.AddField(
            model_name='itemmaster',
            name='default_warehouse',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='inventory.warehouse', verbose_name='المخزن الافتراضي'),
        ),
        migrations.AddField(
            model_name='binlocation',
            name='warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bin_locations', to='inventory.warehouse', verbose_name='المخزن'),
        ),
        migrations.CreateModel(
            name='Alert',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('alert_type', models.CharField(choices=[('LOW_STOCK', 'مخزون منخفض'), ('OUT_OF_STOCK', 'نفاد المخزون'), ('NEAR_EXPIRY', 'اقتراب انتهاء الصلاحية'), ('EXPIRED', 'منتهي الصلاحية'), ('SLOW_MOVING', 'بطيء الحركة'), ('WAREHOUSE_FULL', 'امتلاء المخزن'), ('ITEM_ADDED', 'إضافة صنف جديد'), ('ITEM_MODIFIED', 'تعديل صنف'), ('ITEM_DELETED', 'حذف صنف'), ('RECEIPT_CREATED', 'إذن استلام جديد'), ('ISSUE_CREATED', 'إذن صرف جديد'), ('TRANSFER_CREATED', 'نقل مخزون'), ('ADJUSTMENT_CREATED', 'تسوية مخزون'), ('SHIPMENT_ARRIVED', 'وصول شحنة'), ('DAMAGE_DETECTED', 'اكتشاف تلف'), ('LOCATION_CHANGED', 'تغيير موقع تخزين'), ('WAREHOUSE_ADDED', 'إضافة مخزن جديد'), ('LOCATION_STATUS_CHANGED', 'تغيير حالة موقع التخزين')], max_length=30, verbose_name='نوع التنبيه')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان التنبيه')),
                ('message', models.TextField(verbose_name='رسالة التنبيه')),
                ('priority', models.CharField(choices=[('LOW', 'منخفضة'), ('MEDIUM', 'متوسطة'), ('HIGH', 'عالية'), ('CRITICAL', 'حرجة')], default='MEDIUM', max_length=10, verbose_name='مستوى الأولوية')),
                ('status', models.CharField(choices=[('ACTIVE', 'نشط'), ('ACKNOWLEDGED', 'تم الاطلاع'), ('RESOLVED', 'تم الحل'), ('DISMISSED', 'تم الإغلاق')], default='ACTIVE', max_length=15, verbose_name='حالة التنبيه')),
                ('is_read', models.BooleanField(default=False, verbose_name='تم القراءة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الاطلاع')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('acknowledged_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='acknowledged_alerts', to=settings.AUTH_USER_MODEL, verbose_name='تم الاطلاع بواسطة')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_alerts', to=settings.AUTH_USER_MODEL, verbose_name='تم الحل بواسطة')),
                ('bin_location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='inventory.binlocation', verbose_name='موقع التخزين المرتبط')),
                ('item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='inventory.itemmaster', verbose_name='الصنف المرتبط')),
                ('movement', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='inventory.stockmovement', verbose_name='الحركة المرتبطة')),
                ('warehouse', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='inventory.warehouse', verbose_name='المخزن المرتبط')),
            ],
            options={
                'verbose_name': 'تنبيه',
                'verbose_name_plural': 'التنبيهات',
                'ordering': ['-created_at', '-priority'],
            },
        ),
        migrations.AddIndex(
            model_name='warehouse',
            index=models.Index(fields=['warehouse_code'], name='inventory_w_warehou_338bd5_idx'),
        ),
        migrations.AddIndex(
            model_name='warehouse',
            index=models.Index(fields=['warehouse_type'], name='inventory_w_warehou_b51245_idx'),
        ),
        migrations.AddIndex(
            model_name='warehouse',
            index=models.Index(fields=['is_active'], name='inventory_w_is_acti_3ddeac_idx'),
        ),
        migrations.AddIndex(
            model_name='warehouse',
            index=models.Index(fields=['city'], name='inventory_w_city_950b2f_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmovement',
            index=models.Index(fields=['movement_number'], name='inventory_s_movemen_0f2675_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmovement',
            index=models.Index(fields=['movement_type'], name='inventory_s_movemen_018f99_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmovement',
            index=models.Index(fields=['movement_date'], name='inventory_s_movemen_25c7cf_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmovement',
            index=models.Index(fields=['item'], name='inventory_s_item_id_1d9b15_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmovement',
            index=models.Index(fields=['warehouse'], name='inventory_s_warehou_a81054_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmovement',
            index=models.Index(fields=['status'], name='inventory_s_status_a4b4ed_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmovement',
            index=models.Index(fields=['batch_number'], name='inventory_s_batch_n_eac101_idx'),
        ),
        migrations.AddIndex(
            model_name='stockbalance',
            index=models.Index(fields=['item'], name='inventory_s_item_id_6fba08_idx'),
        ),
        migrations.AddIndex(
            model_name='stockbalance',
            index=models.Index(fields=['warehouse'], name='inventory_s_warehou_9bf06e_idx'),
        ),
        migrations.AddIndex(
            model_name='stockbalance',
            index=models.Index(fields=['bin_location'], name='inventory_s_bin_loc_06b5b6_idx'),
        ),
        migrations.AddIndex(
            model_name='stockbalance',
            index=models.Index(fields=['batch_number'], name='inventory_s_batch_n_0d6015_idx'),
        ),
        migrations.AddIndex(
            model_name='stockbalance',
            index=models.Index(fields=['expiry_date'], name='inventory_s_expiry__cc4e15_idx'),
        ),
        migrations.AddIndex(
            model_name='stockbalance',
            index=models.Index(fields=['current_quantity'], name='inventory_s_current_e8e02f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='stockbalance',
            unique_together={('item', 'warehouse', 'bin_location', 'batch_number')},
        ),
        migrations.AddIndex(
            model_name='itemmaster',
            index=models.Index(fields=['item_code'], name='inventory_i_item_co_bf442d_idx'),
        ),
        migrations.AddIndex(
            model_name='itemmaster',
            index=models.Index(fields=['category'], name='inventory_i_categor_18fb03_idx'),
        ),
        migrations.AddIndex(
            model_name='itemmaster',
            index=models.Index(fields=['material_type'], name='inventory_i_materia_c28d11_idx'),
        ),
        migrations.AddIndex(
            model_name='itemmaster',
            index=models.Index(fields=['is_active'], name='inventory_i_is_acti_85f6cd_idx'),
        ),
        migrations.AddIndex(
            model_name='itemmaster',
            index=models.Index(fields=['hazard_level'], name='inventory_i_hazard__75764f_idx'),
        ),
        migrations.AddIndex(
            model_name='binlocation',
            index=models.Index(fields=['warehouse', 'bin_code'], name='inventory_b_warehou_1a1f40_idx'),
        ),
        migrations.AddIndex(
            model_name='binlocation',
            index=models.Index(fields=['status'], name='inventory_b_status_42f7b4_idx'),
        ),
        migrations.AddIndex(
            model_name='binlocation',
            index=models.Index(fields=['aisle', 'rack'], name='inventory_b_aisle_ac0a6c_idx'),
        ),
        migrations.AddIndex(
            model_name='binlocation',
            index=models.Index(fields=['is_active'], name='inventory_b_is_acti_d7fada_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='binlocation',
            unique_together={('warehouse', 'bin_code')},
        ),
        migrations.AddIndex(
            model_name='alert',
            index=models.Index(fields=['alert_type'], name='inventory_a_alert_t_04e0f7_idx'),
        ),
        migrations.AddIndex(
            model_name='alert',
            index=models.Index(fields=['priority'], name='inventory_a_priorit_f118f0_idx'),
        ),
        migrations.AddIndex(
            model_name='alert',
            index=models.Index(fields=['status'], name='inventory_a_status_b5ae1c_idx'),
        ),
        migrations.AddIndex(
            model_name='alert',
            index=models.Index(fields=['is_read'], name='inventory_a_is_read_586f9c_idx'),
        ),
        migrations.AddIndex(
            model_name='alert',
            index=models.Index(fields=['created_at'], name='inventory_a_created_fabf5e_idx'),
        ),
        migrations.AddIndex(
            model_name='alert',
            index=models.Index(fields=['item'], name='inventory_a_item_id_2f00b8_idx'),
        ),
        migrations.AddIndex(
            model_name='alert',
            index=models.Index(fields=['warehouse'], name='inventory_a_warehou_ef7bc9_idx'),
        ),
    ]
