{% extends 'base.html' %}
{% load static %}

{% block title %}إضافة مستخدم جديد - Ka<PERSON>Verse{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, var(--brand-red), #FF6B6B);
        color: var(--white);
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .page-header h1 {
        font-weight: 700;
        margin: 0;
        color: var(--white);
    }

    .form-section {
        background-color: var(--white);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        border: 1px solid var(--line);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--brand-red);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-left: 0.5rem;
        color: var(--brand-gold);
    }

    .action-buttons {
        margin-top: 2rem;
        display: flex;
        gap: 1rem;
    }

    .form-control, .form-select {
        border-radius: 10px;
        padding: 0.75rem 1rem;
        border: 1px solid var(--line);
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--brand-red);
        box-shadow: 0 0 0 0.25rem rgba(214, 40, 40, 0.25);
    }

    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <h1>
            <i class="fas fa-user-plus me-2"></i>
            إضافة مستخدم جديد
        </h1>
    </div>

    <!-- نموذج إضافة المستخدم -->
    <div class="form-section">
        <h2 class="section-title">
            <i class="fas fa-user-edit"></i>
            بيانات المستخدم الجديد
        </h2>
        
        <form method="post" action="{% url 'inventory:add_user' %}">
            {% csrf_token %}
            
            <!-- معلومات تسجيل الدخول -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <label for="username" class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="username" name="username" required>
                </div>
                
                <div class="col-md-6">
                    <label for="password" class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                    <input type="password" class="form-control" id="password" name="password" required>
                </div>
            </div>
            
            <!-- المعلومات الشخصية -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <label for="first_name" class="form-label">الاسم الأول</label>
                    <input type="text" class="form-control" id="first_name" name="first_name">
                </div>
                
                <div class="col-md-6">
                    <label for="last_name" class="form-label">الاسم الأخير</label>
                    <input type="text" class="form-control" id="last_name" name="last_name">
                </div>
            </div>
            
            <!-- معلومات الوظيفة -->
            <div class="row mb-4">
                <div class="col-md-4">
                    <label for="role" class="form-label">الدور <span class="text-danger">*</span></label>
                    <select name="role" id="role" class="form-select" required>
                        <option value="">-- اختر الدور --</option>
                        {% for role_code, role_name in role_choices %}
                        <option value="{{ role_code }}">{{ role_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-4 warehouse-select" style="display: none;">
                    <label for="warehouse" class="form-label">المخزن المخصص</label>
                    <select name="warehouse" id="warehouse" class="form-select">
                        <option value="">-- اختر المخزن --</option>
                        {% for warehouse in warehouses %}
                        <option value="{{ warehouse.id }}">{{ warehouse.warehouse_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-4">
                    <label for="department" class="form-label">القسم</label>
                    <input type="text" class="form-control" id="department" name="department">
                </div>
            </div>
            
            <!-- معلومات الاتصال -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <label for="phone_number" class="form-label">رقم الهاتف</label>
                    <input type="text" class="form-control" id="phone_number" name="phone_number">
                </div>
            </div>
            
            <div class="action-buttons">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-user-plus me-2"></i>
                    إضافة المستخدم
                </button>
                
                <a href="{% url 'inventory:users_list' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إظهار/إخفاء حقل المخزن حسب الدور المختار
        const roleSelect = document.getElementById('role');
        const warehouseSelect = document.querySelector('.warehouse-select');
        
        if (roleSelect && warehouseSelect) {
            roleSelect.addEventListener('change', function() {
                if (this.value === 'WAREHOUSE_MANAGER') {
                    warehouseSelect.style.display = 'block';
                } else {
                    warehouseSelect.style.display = 'none';
                }
            });
        }
    });
</script>
{% endblock %}