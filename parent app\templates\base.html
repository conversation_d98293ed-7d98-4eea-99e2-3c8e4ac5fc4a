<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}KamaVerse - نظام إدارة شركة القماش{% endblock %}</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS with <PERSON><PERSON><PERSON><PERSON> Brand Colors -->
    <style>
        :root {
            --brand-red: #D62828;
            --brand-red-dark: #8B1116;
            --brand-red-light: #FCE8E8;
            --brand-gold: #C89A3C;
            --brand-gold-light: #F4D488;
            --brand-gold-dark: #8C6420;
            --ink: #1A1A1A;
            --slate: #4A4F57;
            --line: #E6E8ED;
            --canvas: #F7F8FB;
            --white: #FFFFFF;
            --accent-sand: #FFF3E0;
            --success: #2E7D32;
            --warning: #F39C12;
            --error: #C21807;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(180deg, #FFFFFF 0%, #F7F8FB 100%);
            color: var(--ink);
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--ink) !important;
        }

        .navbar-dark {
            background: var(--ink) !important;
        }

        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--brand-red) 0%, var(--brand-red-dark) 100%);
            color: white;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.9);
            padding: 12px 20px;
            border-radius: 12px;
            margin: 4px 0;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.15);
            border-color: var(--brand-gold-light);
            transform: translateX(-5px);
        }

        .sidebar .nav-link.active {
            color: white;
            background-color: var(--brand-gold);
            border-color: var(--brand-gold-light);
        }

        .main-content {
            padding: 20px;
        }

        .card {
            border: 1px solid var(--line);
            border-radius: 16px;
            box-shadow: 0 4px 12px rgba(26, 26, 26, 0.08);
            transition: all 0.3s ease;
            background: var(--white);
        }

        .card:hover {
            transform: translateY(-8px);
            box-shadow: 0 8px 25px rgba(214, 40, 40, 0.15);
            border-color: var(--brand-gold-light);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--brand-red) 0%, var(--brand-red-dark) 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 28px;
            font-weight: 600;
            color: var(--white);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--brand-red-dark) 0%, #6B0D10 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(214, 40, 40, 0.4);
            color: var(--white);
        }
        
        .btn-secondary {
            background: var(--white);
            border: 2px solid var(--brand-gold);
            color: var(--brand-gold-dark);
            border-radius: 12px;
            padding: 12px 28px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: var(--brand-gold-light);
            border-color: var(--brand-gold-dark);
            color: var(--brand-gold-dark);
            transform: translateY(-2px);
        }

        .alert {
            border: none;
            border-radius: 12px;
            border-left: 4px solid var(--brand-gold);
        }

        .alert-success {
            background: var(--accent-sand);
            color: var(--success);
            border-left-color: var(--success);
        }

        .alert-danger {
            background: var(--brand-red-light);
            color: var(--error);
            border-left-color: var(--error);
        }

        .alert-warning {
            background: #FFF8E1;
            color: var(--warning);
            border-left-color: var(--warning);
        }

        .logo {
            max-height: 50px;
            width: auto;
        }

        .module-card {
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid var(--line);
            background: var(--white);
            border-radius: 16px;
            overflow: hidden;
        }

        .module-card:hover {
            border-color: var(--brand-gold);
            transform: scale(1.05);
            box-shadow: 0 12px 30px rgba(200, 154, 60, 0.2);
        }

        .module-icon {
            font-size: 3rem;
            color: var(--brand-gold);
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .module-card:hover .module-icon {
            color: var(--brand-red);
            transform: scale(1.1);
        }

        .footer {
            background: var(--ink);
            color: var(--white);
            padding: 30px 0;
            margin-top: 50px;
        }

        .footer a {
            color: var(--brand-gold-light);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: var(--brand-gold);
        }
        
        /* Text Colors */
        .text-primary-custom {
            color: var(--brand-red) !important;
        }

        .text-gold {
            color: var(--brand-gold) !important;
        }

        .text-slate {
            color: var(--slate) !important;
        }

        /* Background Colors */
        .bg-canvas {
            background-color: var(--canvas) !important;
        }

        .bg-sand {
            background-color: var(--accent-sand) !important;
        }

        /* Borders */
        .border-gold {
            border-color: var(--brand-gold) !important;
        }

        .border-line {
            border-color: var(--line) !important;
        }

        /* RTL Adjustments */
        .text-start { text-align: right !important; }
        .text-end { text-align: left !important; }

        /* Loading Spinner */
        .spinner-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(26, 26, 26, 0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .spinner-border-custom {
            width: 3rem;
            height: 3rem;
            border: 0.3em solid rgba(255, 255, 255, 0.3);
            border-top: 0.3em solid var(--brand-red);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Custom Gradients */
        .gradient-red {
            background: linear-gradient(135deg, var(--brand-red) 0%, var(--brand-red-dark) 100%);
        }

        .gradient-gold {
            background: linear-gradient(135deg, var(--brand-gold-light) 0%, var(--brand-gold) 60%, var(--brand-gold-dark) 100%);
        }

        .gradient-soft {
            background: linear-gradient(180deg, var(--white) 0%, var(--canvas) 100%);
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Loading Spinner -->
    <div class="spinner-overlay" id="loadingSpinner">
        <div class="spinner-border-custom"></div>
    </div>

    {% if user.is_authenticated %}
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: var(--ink); box-shadow: 0 2px 10px rgba(26, 26, 26, 0.1);">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'authentication:dashboard' %}" style="color: var(--white) !important;">
                <img src="/static/images/logo.png" alt="Logo" class="logo me-3">
                <div>
                    <div style="font-size: 1.5rem; font-weight: 700; color: var(--white);">KamaVerse</div>
                    <small style="color: var(--brand-gold-light); font-size: 0.8rem;">نظام إدارة شركة القماش</small>
                </div>
            </a>

            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" style="color: var(--brand-gold);">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link px-3 py-2 rounded-pill" href="{% url 'authentication:dashboard' %}" style="color: var(--brand-gold-light); transition: all 0.3s ease;">
                            <i class="fas fa-home me-2"></i>الرئيسية
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center px-3 py-2 rounded-pill" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown" style="color: var(--white); background: rgba(200, 154, 60, 0.1); border: 1px solid var(--brand-gold);">
                            <div class="d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px; background: var(--brand-gold); border-radius: 50%;">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            {{ user.arabic_name|default:user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end border-0 shadow-lg" style="background: var(--white); border-radius: 12px; min-width: 200px;">
                            <li><a class="dropdown-item py-2 px-3" href="{% url 'authentication:profile' %}" style="color: var(--ink); border-radius: 8px; margin: 4px;">
                                <i class="fas fa-user-edit me-2 text-gold"></i> الملف الشخصي
                            </a></li>
                            <li><hr class="dropdown-divider" style="border-color: var(--line);"></li>
                            <li><a class="dropdown-item py-2 px-3" href="{% url 'authentication:logout' %}" style="color: var(--error); border-radius: 8px; margin: 4px;">
                                <i class="fas fa-sign-out-alt me-2"></i> تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <style>
        .navbar-nav .nav-link:hover {
            background: rgba(200, 154, 60, 0.2) !important;
            color: var(--brand-gold) !important;
        }

        .dropdown-item:hover {
            background: var(--canvas) !important;
            color: var(--brand-red) !important;
        }
    </style>
    {% endif %}

    <!-- Messages -->
    {% if messages %}
    <div class="container-fluid mt-3">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {% if message.tags == 'error' %}
                <i class="fas fa-exclamation-triangle"></i>
            {% elif message.tags == 'success' %}
                <i class="fas fa-check-circle"></i>
            {% elif message.tags == 'warning' %}
                <i class="fas fa-exclamation-circle"></i>
            {% else %}
                <i class="fas fa-info-circle"></i>
            {% endif %}
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-3">
                        <img src="/static/images/logo.png" alt="Logo" style="max-height: 40px; filter: brightness(0) invert(1);" class="me-3">
                        <div>
                            <h5 class="mb-1" style="color: var(--white); font-weight: 700;">KamaVerse</h5>
                            <small style="color: var(--brand-gold-light);">نظام إدارة متكامل</small>
                        </div>
                    </div>
                    <p style="color: rgba(255, 255, 255, 0.8); line-height: 1.6;">
                        نظام إدارة متكامل لشركة القماش للاستيراد والتصدير<br>
                        حلول تقنية متطورة لإدارة الأعمال
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <div class="mb-3">
                        <p class="mb-2" style="color: var(--white); font-weight: 600;">
                            &copy; 2024 شركة القماش للاستيراد والتصدير
                        </p>
                        <p class="mb-2" style="color: var(--brand-gold-light);">
                            جميع الحقوق محفوظة
                        </p>
                        <small style="color: rgba(255, 255, 255, 0.6);">
                            الإصدار 1.0.0 |
                            <a href="#" style="color: var(--brand-gold-light);">الدعم الفني</a>
                        </small>
                    </div>
                </div>
            </div>
            <hr style="border-color: rgba(255, 255, 255, 0.1); margin: 20px 0;">
            <div class="row">
                <div class="col-12 text-center">
                    <small style="color: rgba(255, 255, 255, 0.6);">
                        تم التطوير بواسطة فريق تقنية المعلومات - شركة القماش
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Show loading spinner
        function showLoading() {
            document.getElementById('loadingSpinner').style.display = 'flex';
        }
        
        // Hide loading spinner
        function hideLoading() {
            document.getElementById('loadingSpinner').style.display = 'none';
        }
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
        
        // Add loading to forms
        $('form').on('submit', function() {
            showLoading();
        });
        
        // Add loading to navigation links
        $('a[href]:not([href="#"]):not([data-bs-toggle])').on('click', function() {
            showLoading();
        });
        
        // Hide loading when page loads
        $(document).ready(function() {
            hideLoading();
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
