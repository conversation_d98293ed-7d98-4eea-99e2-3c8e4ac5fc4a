{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/kamaverse.css' %}">
<style>
    .detail-card {
        background: white;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .section-title {
        color: #D62828;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #C89A3C;
    }
    
    .status-badge {
        font-size: 1rem;
        padding: 8px 16px;
        border-radius: 25px;
        font-weight: 600;
    }
    
    .status-pending { background-color: #fff3cd; color: #856404; }
    .status-approved { background-color: #d1ecf1; color: #0c5460; }
    .status-completed { background-color: #d4edda; color: #155724; }
    .status-rejected { background-color: #f8d7da; color: #721c24; }
    .status-cancelled { background-color: #e2e3e5; color: #383d41; }
    
    .transfer-header {
        background: linear-gradient(135deg, #D62828 0%, #B91C1C 100%);
        color: white;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 20px;
    }
    
    .transfer-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #C89A3C;
    }
    
    .warehouse-flow {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 20px 0;
    }
    
    .warehouse-box {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        text-align: center;
        min-width: 200px;
    }
    
    .warehouse-name {
        font-weight: bold;
        font-size: 1.1rem;
        color: #C89A3C;
    }
    
    .flow-arrow {
        font-size: 2rem;
        color: #C89A3C;
        margin: 0 20px;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 600;
        color: #666;
    }
    
    .info-value {
        color: #333;
    }
    
    .approval-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
    }
    
    .btn-approve {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 500;
    }
    
    .btn-reject {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 500;
    }

    /* Distribution Management Styles */
    .distribution-item-container {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
    }

    .distribution-item-container:hover {
        box-shadow: 0 4px 16px rgba(0,0,0,0.12);
        transform: translateY(-2px);
    }

    .distribution-item-header {
        background: linear-gradient(135deg, #D62828 0%, #B91C1C 100%);
        color: white;
        padding: 15px 20px;
    }

    .item-title {
        color: white;
        margin: 0;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .distribution-table-container {
        padding: 20px;
        background: #fafbfc;
    }

    .distribution-table {
        margin: 0;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .distribution-table th {
        background: #343a40;
        color: white;
        font-weight: 600;
        border: none;
        text-align: center;
        vertical-align: middle;
        padding: 12px 8px;
    }

    .distribution-table td {
        vertical-align: middle;
        padding: 12px 8px;
        border-color: #e9ecef;
    }

    .distribution-table .form-select,
    .distribution-table .form-control {
        border-radius: 6px;
        border: 1px solid #ced4da;
        transition: all 0.3s ease;
    }

    .distribution-table .form-select:focus,
    .distribution-table .form-control:focus {
        border-color: #C89A3C;
        box-shadow: 0 0 0 0.2rem rgba(200, 154, 60, 0.25);
    }

    .unit-badge {
        background: #e9ecef;
        color: #495057;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 0.9rem;
        font-weight: 500;
    }

    .available-stock {
        color: #17a2b8;
        font-weight: 600;
        font-size: 0.95rem;
    }

    .distribution-summary {
        background: #e3f2fd;
        padding: 15px 20px;
        border-top: 1px solid #dee2e6;
        border-radius: 0 0 12px 12px;
    }

    .btn-outline-success, .btn-outline-danger {
        border-radius: 6px;
        transition: all 0.3s ease;
    }

    .btn-outline-success:hover {
        transform: scale(1.05);
    }

    .btn-outline-danger:hover {
        transform: scale(1.05);
    }

    /* Animation for new rows */
    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .distribution-row.new-row {
        animation: slideIn 0.3s ease-out;
    }
    
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline::before {
        content: '';
        position: absolute;
        left: 15px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #C89A3C;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -23px;
        top: 5px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #C89A3C;
    }
    
    .timeline-date {
        font-size: 0.9rem;
        color: #666;
    }

    /* Transfer Summary Styles */
    .transfer-summary-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #dee2e6;
        border-radius: 12px;
        padding: 20px;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .summary-label {
        font-weight: 600;
        color: #495057;
    }

    .summary-value {
        font-weight: 500;
        color: #C89A3C;
    }

    /* Warehouse Selection Styles */
    #destination_warehouse {
        border: 2px solid #dee2e6;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    #destination_warehouse:focus {
        border-color: #C89A3C;
        box-shadow: 0 0 0 0.2rem rgba(200, 154, 60, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="text-primary mb-1">
                <i class="fas fa-file-alt me-2"></i>{{ page_title }}
            </h2>
            <p class="text-muted mb-0">تفاصيل طلب نقل المخزون</p>
        </div>
        <a href="{% url 'inventory:stock_transfer_list' %}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
        </a>
    </div>

    <!-- Transfer Header -->
    <div class="transfer-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="transfer-number">{{ transfer.transfer_number }}</div>
                <div>{{ transfer.transfer_date|date:"Y/m/d H:i" }}</div>
            </div>
            <div class="col-md-6 text-end">
                <span class="status-badge status-{{ transfer.status|lower }}">
                    {{ transfer.get_status_display }}
                </span>
            </div>
        </div>
        
        <!-- Warehouse Flow -->
        <div class="warehouse-flow">
            <div class="warehouse-box">
                <div class="warehouse-name">{{ transfer.source_warehouse.warehouse_name }}</div>
                <div>المخزن المصدر</div>
            </div>
            <div class="flow-arrow">
                <i class="fas fa-arrow-left"></i>
            </div>
            <div class="warehouse-box">
                <div class="warehouse-name">{{ transfer.destination_warehouse.warehouse_name }}</div>
                <div>المخزن الهدف</div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Transfer Details -->
            <div class="detail-card">
                <h5 class="section-title">
                    <i class="fas fa-info-circle me-2"></i>تفاصيل النقل
                </h5>

                <div class="info-row">
                    <span class="info-label">سبب النقل:</span>
                    <span class="info-value">{{ transfer.reason }}</span>
                </div>

                {% if transfer.notes %}
                <div class="info-row">
                    <span class="info-label">ملاحظات:</span>
                    <span class="info-value">{{ transfer.notes }}</span>
                </div>
                {% endif %}
            </div>

            <!-- Items Details -->
            <div class="detail-card">
                <h5 class="section-title">
                    <i class="fas fa-list me-2"></i>الأصناف المطلوبة ({{ transfer.get_total_items_count }})
                </h5>

                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-dark">
                            <tr>
                                <th>م</th>
                                <th>كود الصنف</th>
                                <th>اسم الصنف</th>
                                <th>الكمية المطلوبة</th>
                                <th>الوحدة</th>
                                <th>موقع التخزين المصدر</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in transfer.transfer_items.all %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ item.item.item_code }}</td>
                                <td>{{ item.item.item_name_ar }}</td>
                                <td>{{ item.quantity|floatformat:3 }}</td>
                                <td>{{ item.item.unit_of_measure }}</td>
                                <td>{{ item.source_bin_location.bin_code|default:"-" }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center text-muted">
                                    لا توجد أصناف مسجلة
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Warehouse Selection Section (for Manager only) -->
            {% if user_profile.role == 'MANAGER' and transfer.status == 'PENDING' %}
            <div class="detail-card">
                <h5 class="section-title">
                    <i class="fas fa-warehouse me-2"></i>اختيار المخزن الهدف
                </h5>

                <form method="post" action="{% url 'inventory:stock_transfer_approve' transfer.id %}" id="approvalForm">
                    {% csrf_token %}

                    <!-- Warehouse Selection -->
                    <div class="mb-4">
                        <label for="destination_warehouse" class="form-label">
                            <i class="fas fa-warehouse me-2"></i>المخزن الهدف (الذي ستنقص منه الكمية) *
                        </label>
                        <select name="destination_warehouse" id="destination_warehouse" class="form-select" required>
                            <option value="">اختر المخزن الهدف</option>
                            {% for warehouse in warehouses %}
                            {% if warehouse != transfer.source_warehouse %}
                            <option value="{{ warehouse.id }}">{{ warehouse.warehouse_name }}</option>
                            {% endif %}
                            {% endfor %}
                        </select>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            سيتم نقل جميع الأصناف من هذا المخزن إلى المخزن المرسل للطلب ({{ transfer.source_warehouse.warehouse_name }})
                        </div>
                    </div>

                    <!-- Transfer Summary -->
                    <div class="transfer-summary-card mb-4">
                        <h6 class="mb-3">
                            <i class="fas fa-exchange-alt me-2"></i>ملخص عملية النقل
                        </h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="summary-item">
                                    <span class="summary-label">من المخزن:</span>
                                    <span class="summary-value" id="from-warehouse">سيتم تحديده عند الاختيار</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="summary-item">
                                    <span class="summary-label">إلى المخزن:</span>
                                    <span class="summary-value">{{ transfer.source_warehouse.warehouse_name }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-12">
                                <div class="summary-item">
                                    <span class="summary-label">عدد الأصناف:</span>
                                    <span class="summary-value">{{ transfer.get_total_items_count }} صنف</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Approval Notes -->
                    <div class="mb-3">
                        <label for="approval_notes" class="form-label">ملاحظات الاعتماد (اختياري)</label>
                        <textarea name="approval_notes" id="approval_notes" class="form-control" rows="3"
                                  placeholder="أي ملاحظات حول اعتماد الطلب..."></textarea>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-approve w-100" id="approveBtn">
                                <i class="fas fa-check me-2"></i>اعتماد الطلب
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-reject w-100" onclick="showRejectModal()">
                                <i class="fas fa-times me-2"></i>رفض الطلب
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            {% endif %}

            <!-- Approval Notes -->
            {% if transfer.approval_notes %}
            <div class="detail-card">
                <h5 class="section-title">
                    <i class="fas fa-comment me-2"></i>ملاحظات الاعتماد
                </h5>
                <div class="alert alert-info">
                    {{ transfer.approval_notes }}
                </div>
            </div>
            {% endif %}
        </div>

        <div class="col-md-4">
            <!-- Request Info -->
            <div class="detail-card">
                <h5 class="section-title">
                    <i class="fas fa-user me-2"></i>معلومات الطلب
                </h5>
                
                <div class="info-row">
                    <span class="info-label">طلب بواسطة:</span>
                    <span class="info-value">{{ transfer.requested_by.get_full_name }}</span>
                </div>
                
                <div class="info-row">
                    <span class="info-label">تاريخ الطلب:</span>
                    <span class="info-value">{{ transfer.created_at|date:"Y/m/d H:i" }}</span>
                </div>
                
                {% if transfer.approved_by %}
                <div class="info-row">
                    <span class="info-label">اعتمد بواسطة:</span>
                    <span class="info-value">{{ transfer.approved_by.get_full_name }}</span>
                </div>
                {% endif %}
                
                {% if transfer.approved_at %}
                <div class="info-row">
                    <span class="info-label">تاريخ الاعتماد:</span>
                    <span class="info-value">{{ transfer.approved_at|date:"Y/m/d H:i" }}</span>
                </div>
                {% endif %}
                
                {% if transfer.completed_at %}
                <div class="info-row">
                    <span class="info-label">تاريخ الإكمال:</span>
                    <span class="info-value">{{ transfer.completed_at|date:"Y/m/d H:i" }}</span>
                </div>
                {% endif %}
            </div>

            <!-- Timeline -->
            <div class="detail-card">
                <h5 class="section-title">
                    <i class="fas fa-history me-2"></i>تسلسل العمليات
                </h5>
                
                <div class="timeline">
                    <div class="timeline-item">
                        <div><strong>تم إنشاء الطلب</strong></div>
                        <div class="timeline-date">{{ transfer.created_at|date:"Y/m/d H:i" }}</div>
                    </div>
                    
                    {% if transfer.approved_at %}
                    <div class="timeline-item">
                        <div><strong>
                            {% if transfer.status == 'APPROVED' or transfer.status == 'COMPLETED' %}
                                تم اعتماد الطلب
                            {% elif transfer.status == 'REJECTED' %}
                                تم رفض الطلب
                            {% endif %}
                        </strong></div>
                        <div class="timeline-date">{{ transfer.approved_at|date:"Y/m/d H:i" }}</div>
                    </div>
                    {% endif %}
                    
                    {% if transfer.completed_at %}
                    <div class="timeline-item">
                        <div><strong>تم إكمال النقل</strong></div>
                        <div class="timeline-date">{{ transfer.completed_at|date:"Y/m/d H:i" }}</div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reject Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectModalLabel">رفض الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'inventory:stock_transfer_reject' transfer.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="rejection_notes" class="form-label">سبب الرفض *</label>
                        <textarea name="rejection_notes" id="rejection_notes" class="form-control" rows="4"
                                  placeholder="يرجى توضيح سبب رفض الطلب..." required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-danger">رفض الطلب</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>






// تحديث ملخص النقل عند اختيار المخزن
function updateTransferSummary() {
    const warehouseSelect = document.getElementById('destination_warehouse');
    const fromWarehouseSpan = document.getElementById('from-warehouse');
    const approveBtn = document.getElementById('approveBtn');

    if (warehouseSelect.value) {
        const selectedOption = warehouseSelect.options[warehouseSelect.selectedIndex];
        fromWarehouseSpan.textContent = selectedOption.text;
        fromWarehouseSpan.style.color = '#C89A3C';
        fromWarehouseSpan.style.fontWeight = '600';

        // تفعيل زر الاعتماد
        approveBtn.disabled = false;
        approveBtn.className = 'btn btn-approve w-100';
    } else {
        fromWarehouseSpan.textContent = 'سيتم تحديده عند الاختيار';
        fromWarehouseSpan.style.color = '#6c757d';
        fromWarehouseSpan.style.fontWeight = 'normal';

        // تعطيل زر الاعتماد
        approveBtn.disabled = true;
        approveBtn.className = 'btn btn-secondary w-100';
    }
}

// ربط الحدث بقائمة المخازن
document.addEventListener('DOMContentLoaded', function() {
    const warehouseSelect = document.getElementById('destination_warehouse');
    if (warehouseSelect) {
        warehouseSelect.addEventListener('change', updateTransferSummary);
        // تحديث أولي
        updateTransferSummary();
    }
});

// إظهار نافذة الرفض
function showRejectModal() {
    const modal = new bootstrap.Modal(document.getElementById('rejectModal'));
    modal.show();
}

// التحقق من النموذج قبل الإرسال
document.getElementById('approvalForm').addEventListener('submit', function(e) {
    const warehouseSelect = document.getElementById('destination_warehouse');

    if (!warehouseSelect.value) {
        e.preventDefault();
        alert('يرجى اختيار المخزن الهدف أولاً');
        warehouseSelect.focus();
        return;
    }

    // عرض ملخص النقل للتأكيد
    const selectedWarehouse = warehouseSelect.options[warehouseSelect.selectedIndex].text;
    const summaryText = `ملخص عملية النقل:

من المخزن: ${selectedWarehouse}
إلى المخزن: {{ transfer.source_warehouse.warehouse_name }}
عدد الأصناف: {{ transfer.get_total_items_count }}

سيتم نقل جميع الأصناف المطلوبة من المخزن المحدد إلى المخزن المرسل للطلب.

هل أنت متأكد من اعتماد هذا الطلب؟`;

    if (!confirm(summaryText)) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
