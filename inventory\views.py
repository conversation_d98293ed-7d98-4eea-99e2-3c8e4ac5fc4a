from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum, Avg
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.core.management import call_command
from decimal import Decimal, InvalidOperation
import json
from datetime import datetime, date
from io import StringIO
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from .models import ItemMaster, Warehouse, BinLocation, StockBalance, Alert, GoodsReceipt, GoodsReceiptItem, GoodsIssue, GoodsIssueItem, UserProfile, StockTransfer, StockTransferItem, StockMovement
from .forms import WarehouseForm, BinLocationForm, WarehouseItemForm
from .pdf_generator import pdf_generator
from .excel_processor import ExcelProcessor


def get_user_warehouse(user):
    """دالة للحصول على مخزن المستخدم"""
    try:
        profile = UserProfile.objects.get(user=user)
        return profile.assigned_warehouse
    except UserProfile.DoesNotExist:
        return None

@login_required(login_url='inventory:login')
def dashboard(request):
    """
    الصفحة الرئيسية لموديول المخزون
    Dashboard for Inventory Module
    """
    # حساب الإحصائيات الحقيقية
    total_items = ItemMaster.objects.filter(is_active=True).count()
    total_warehouses = Warehouse.objects.filter(is_active=True).count()
    active_alerts = Alert.objects.filter(status='ACTIVE').count()

    # حساب إجمالي قيمة المخزون
    total_value = StockBalance.objects.aggregate(
        total=Sum('total_value')
    )['total'] or 0

    # أكثر 3 منتجات في المخازن حسب الكمية
    top_items_by_quantity = StockBalance.objects.values(
        'item__item_name_ar',
        'item__item_code',
        'item__unit_of_measure'
    ).annotate(
        total_quantity=Sum('current_quantity')
    ).filter(
        total_quantity__gt=0
    ).order_by('-total_quantity')[:3]

    # المخازن مع نسب الإشغال
    warehouses_stats = []
    for warehouse in Warehouse.objects.filter(is_active=True):
        warehouses_stats.append({
            'warehouse': warehouse,
            'occupancy_percentage': warehouse.get_occupancy_percentage(),
            'available_capacity': warehouse.get_available_capacity(),
        })

    # حركات المخزون الشهرية للـ 12 شهر الماضية
    from datetime import datetime, timedelta
    from django.utils import timezone
    from django.db.models import Count
    from django.db.models.functions import TruncMonth

    # حساب آخر 12 شهر
    end_date = timezone.now()
    start_date = end_date - timedelta(days=365)

    # جلب حركات الاستلام الشهرية
    receipts_monthly = GoodsReceipt.objects.filter(
        receipt_date__gte=start_date,
        status='CONFIRMED'
    ).annotate(
        month=TruncMonth('receipt_date')
    ).values('month').annotate(
        count=Count('id')
    ).order_by('month')

    # جلب حركات الصرف الشهرية
    issues_monthly = GoodsIssue.objects.filter(
        issue_date__gte=start_date,
        status='CONFIRMED'
    ).annotate(
        month=TruncMonth('issue_date')
    ).values('month').annotate(
        count=Count('id')
    ).order_by('month')

    # جلب حركات النقل الشهرية
    transfers_monthly = StockTransfer.objects.filter(
        transfer_date__gte=start_date,
        status='COMPLETED'
    ).annotate(
        month=TruncMonth('transfer_date')
    ).values('month').annotate(
        count=Count('id')
    ).order_by('month')

    # إنشاء قائمة الشهور للـ 12 شهر الماضية
    monthly_data = []
    current_date = start_date.replace(day=1)

    while current_date <= end_date:
        month_str = current_date.strftime('%Y-%m')
        month_name = current_date.strftime('%b %Y')

        # البحث عن البيانات لهذا الشهر
        receipts_count = next((item['count'] for item in receipts_monthly if item['month'].strftime('%Y-%m') == month_str), 0)
        issues_count = next((item['count'] for item in issues_monthly if item['month'].strftime('%Y-%m') == month_str), 0)
        transfers_count = next((item['count'] for item in transfers_monthly if item['month'].strftime('%Y-%m') == month_str), 0)

        monthly_data.append({
            'month': month_name,
            'receipts': receipts_count,
            'issues': issues_count,
            'transfers': transfers_count,
            'total': receipts_count + issues_count + transfers_count
        })

        # الانتقال للشهر التالي
        if current_date.month == 12:
            current_date = current_date.replace(year=current_date.year + 1, month=1)
        else:
            current_date = current_date.replace(month=current_date.month + 1)

    context = {
        'page_title': 'الصفحة الرئيسية',
        'total_items': total_items,
        'total_warehouses': total_warehouses,
        'active_alerts': active_alerts,
        'total_value': total_value,
        'top_items_by_quantity': top_items_by_quantity,
        'warehouses_stats': warehouses_stats,
        'monthly_movements': json.dumps(monthly_data),
        'current_month_total': monthly_data[-1]['total'] if monthly_data else 0,
        'is_admin': request.user.username == 'admin',  # تحديد ما إذا كان المستخدم هو المدير
    }

    return render(request, 'inventory/dashboard.html', context)


def item_add(request):
    """صفحة إضافة صنف جديد"""
    if request.method == 'POST':
        try:
            # إنشاء صنف جديد من البيانات المرسلة
            # تجاهل الحقول غير الموجودة في النموذج مثل storage_conditions
            item = ItemMaster.objects.create(
                item_code=request.POST.get('item_code'),
                item_name_ar=request.POST.get('item_name_ar'),
                item_name_en=request.POST.get('item_name_en', ''),
                chemical_formula=request.POST.get('chemical_formula', ''),
                notes=request.POST.get('description', ''),  # استخدام notes بدلاً من description
                category=request.POST.get('category'),
                material_type=request.POST.get('material_type'),
                hazard_level=request.POST.get('hazard_level'),
                unit_of_measure=request.POST.get('base_unit', 'KG'),  # استخدام unit_of_measure بدلاً من base_unit
                minimum_stock_level=request.POST.get('min_stock_level') or 0,  # استخدام minimum_stock_level
                reorder_point=request.POST.get('max_stock_level') or 0,  # استخدام reorder_point
                shelf_life_months=request.POST.get('shelf_life_months') or None,
                is_active=True
            )

            # إنشاء تنبيه لإضافة صنف جديد
            Alert.objects.create(
                alert_type='ITEM_ADDED',
                title='تم إضافة صنف جديد',
                message=f'تم إضافة صنف جديد "{item.item_name_ar}" بكود {item.item_code}',
                priority='MEDIUM',
                item=item,
                status='ACTIVE',
                is_read=False
            )

            messages.success(request, f'تم إضافة الصنف "{item.item_name_ar}" بنجاح!')
            return redirect('inventory:items_list')

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إضافة الصنف: {str(e)}')

    return render(request, 'inventory/item_add.html')


def item_detail(request, pk):
    """صفحة عرض تفاصيل الصنف"""
    try:
        item = ItemMaster.objects.get(pk=pk)
        
        # الحصول على معلومات المخزون الحالي
        stock_balances = StockBalance.objects.filter(
            item=item,
            current_quantity__gt=0
        ).select_related('warehouse', 'bin_location')
        
        # حساب إجمالي الكمية
        total_quantity = sum(balance.current_quantity for balance in stock_balances)
        
        # عدد المخازن المتوفر بها
        warehouse_count = stock_balances.values('warehouse').distinct().count()
        
        # آخر حركة
        last_movement = StockMovement.objects.filter(
            item=item
        ).order_by('-movement_date').first()
        
        context = {
            'item': item,
            'stock_balances': stock_balances,
            'total_quantity': total_quantity,
            'warehouse_count': warehouse_count,
            'last_movement': last_movement,
        }
        
        return render(request, 'inventory/item_detail.html', context)
    except ItemMaster.DoesNotExist:
        messages.error(request, 'الصنف المطلوب غير موجود')
        return redirect('inventory:items_list')


def item_edit(request, pk):
    """صفحة تعديل الصنف"""
    try:
        item = ItemMaster.objects.get(pk=pk)

        if request.method == 'POST':
            try:
                # تحديث بيانات الصنف
                item.item_code = request.POST.get('item_code')
                item.item_name_ar = request.POST.get('item_name_ar')
                item.item_name_en = request.POST.get('item_name_en', '')
                item.chemical_formula = request.POST.get('chemical_formula', '')
                item.notes = request.POST.get('description', '')  # استخدام notes بدلاً من description
                item.category = request.POST.get('category')
                item.material_type = request.POST.get('material_type')
                item.hazard_level = request.POST.get('hazard_level')
                item.unit_of_measure = request.POST.get('base_unit', 'KG')  # استخدام unit_of_measure
                item.minimum_stock_level = request.POST.get('min_stock_level') or 0  # استخدام minimum_stock_level
                item.reorder_point = request.POST.get('current_quantity') or 0  # استخدام reorder_point
                item.shelf_life_months = request.POST.get('shelf_life_months') or None
                item.is_active = request.POST.get('is_active') == '1'

                item.save()

                # إنشاء تنبيه لتعديل صنف
                Alert.objects.create(
                    alert_type='ITEM_MODIFIED',
                    title='تم تعديل صنف',
                    message=f'تم تعديل بيانات الصنف "{item.item_name_ar}" بكود {item.item_code}',
                    priority='MEDIUM',
                    item=item,
                    status='ACTIVE',
                    is_read=False
                )

                messages.success(request, f'تم تحديث الصنف "{item.item_name_ar}" بنجاح!')
                return redirect('inventory:item_detail', pk=item.pk)

            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء تحديث الصنف: {str(e)}')

        return render(request, 'inventory/item_edit.html', {'item': item})

    except ItemMaster.DoesNotExist:
        messages.error(request, 'الصنف المطلوب غير موجود')
        return redirect('inventory:items_list')


def item_delete(request, pk):
    """حذف صنف مع التحقق من العلاقات"""
    try:
        item = ItemMaster.objects.get(pk=pk)
        
        # التحقق من وجود بيانات مرتبطة
        related_data = []
        
        # فحص موازين المخزون
        stock_balances = item.stock_balances.all()
        if stock_balances.exists():
            related_data.append(f"موازين المخزون ({stock_balances.count()})")
        
        # فحص حركات المخزون
        movements = item.movements.all()
        if movements.exists():
            related_data.append(f"حركات المخزون ({movements.count()})")
        
        # فحص أذون الاستلام
        receipt_items = item.receipt_items.all()
        if receipt_items.exists():
            related_data.append(f"أذون الاستلام ({receipt_items.count()})")
        
        # فحص أذون الصرف
        issue_items = item.issue_items.all()
        if issue_items.exists():
            related_data.append(f"أذون الصرف ({issue_items.count()})")
        
        # فحص عمليات النقل
        transfer_items = item.transfer_items.all()
        if transfer_items.exists():
            related_data.append(f"عمليات النقل ({transfer_items.count()})")
        
        # فحص التنبيهات
        alerts = item.alerts.all()
        if alerts.exists():
            related_data.append(f"التنبيهات ({alerts.count()})")
        
        if request.method == 'POST':
            # حذف الصنف نهائياً مع جميع البيانات المرتبطة
            item_name = item.item_name_ar
            item_code = item.item_code
            
            # حذف موازين المخزون المرتبطة وتسجيل التنبيهات
            deleted_balances = 0
            for balance in stock_balances:
                # إنشاء تنبيه لحذف رصيد المخزون
                Alert.create_alert(
                    alert_type='ITEM_DELETED',
                    title=f'حذف رصيد مخزون للصنف {item_name}',
                    message=f'تم حذف رصيد مخزون الصنف "{item_name}" ({item_code}) من المخزن {balance.warehouse.warehouse_name} بموقع {balance.bin_location.bin_code} بالكمية {balance.current_quantity} {item.get_unit_of_measure_display}',
                    priority='MEDIUM',
                    item=item,
                    warehouse=balance.warehouse,
                    bin_location=balance.bin_location
                )
                balance.delete()
                deleted_balances += 1
            
            # حذف حركات المخزون المرتبطة
            movements_count = movements.count()
            movements.delete()
            
            # حذف أصناف أذون الاستلام المرتبطة
            receipt_items_count = receipt_items.count()
            receipt_items.delete()
            
            # حذف أصناف أذون الصرف المرتبطة
            issue_items_count = issue_items.count()
            issue_items.delete()
            
            # حذف أصناف عمليات النقل المرتبطة
            transfer_items_count = transfer_items.count()
            transfer_items.delete()
            
            # حذف الصنف نفسه
            item.delete()
            
            # إنشاء تنبيه لحذف الصنف
            Alert.create_alert(
                alert_type='ITEM_DELETED',
                title='تم حذف صنف',
                message=f'تم حذف الصنف "{item_name}" ({item_code}) من النظام بشكل نهائي مع جميع البيانات المرتبطة',
                priority='HIGH'
            )
            
            messages.success(request, f'تم حذف الصنف "{item_name}" بنجاح مع جميع البيانات المرتبطة ({deleted_balances} رصيد مخزون، {movements_count} حركة، {receipt_items_count} استلام، {issue_items_count} صرف، {transfer_items_count} نقل)')
            return redirect('inventory:items_list')
        
        # عرض صفحة تأكيد الحذف
        context = {
            'item': item,
            'related_data': related_data,
            'stock_balances': stock_balances,
            'page_title': f'حذف الصنف: {item.item_name_ar}'
        }
        return render(request, 'inventory/item_delete_confirm.html', context)
        
    except ItemMaster.DoesNotExist:
        messages.error(request, 'الصنف المطلوب غير موجود')
        return redirect('inventory:items_list')


def items_list(request):
    """
    صفحة قائمة الأصناف مع البحث والتصفية
    Items List Page with Search and Filtering
    """
    # الحصول على معاملات البحث والتصفية
    search_query = request.GET.get('search', '')
    category_filter = request.GET.get('category', '')
    material_type_filter = request.GET.get('material_type', '')
    hazard_level_filter = request.GET.get('hazard_level', '')
    status_filter = request.GET.get('status', 'active')  # افتراضي: الأصناف النشطة فقط

    # بناء الاستعلام
    items = ItemMaster.objects.all()

    # تطبيق فلتر الحالة
    if status_filter == 'active':
        items = items.filter(is_active=True)
    elif status_filter == 'inactive':
        items = items.filter(is_active=False)
    # إذا كان 'all' فلا نطبق فلتر

    # تطبيق البحث
    if search_query:
        items = items.filter(
            Q(item_name_ar__icontains=search_query) |
            Q(item_name_en__icontains=search_query) |
            Q(item_code__icontains=search_query) |
            Q(chemical_formula__icontains=search_query) |
            Q(cas_number__icontains=search_query)
        )

    # تطبيق الفلاتر
    if category_filter:
        items = items.filter(category=category_filter)

    if material_type_filter:
        items = items.filter(material_type=material_type_filter)

    if hazard_level_filter:
        items = items.filter(hazard_level=hazard_level_filter)

    # ترتيب النتائج
    items = items.order_by('item_name_ar')

    # تقسيم الصفحات (20 صنف لكل صفحة)
    paginator = Paginator(items, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # إحصائيات للفلاتر
    categories_stats = ItemMaster.objects.filter(is_active=True).values('category').annotate(
        count=Count('id')
    ).order_by('category')

    material_types_stats = ItemMaster.objects.filter(is_active=True).values('material_type').annotate(
        count=Count('id')
    ).order_by('material_type')

    context = {
        'page_title': 'إدارة الأصناف',
        'page_obj': page_obj,
        'search_query': search_query,
        'category_filter': category_filter,
        'material_type_filter': material_type_filter,
        'hazard_level_filter': hazard_level_filter,
        'status_filter': status_filter,
        'categories_stats': categories_stats,
        'material_types_stats': material_types_stats,
        'total_items': paginator.count,
        # خيارات الفلاتر
        'category_choices': ItemMaster.PRODUCT_CATEGORIES,
        'material_type_choices': ItemMaster.MATERIAL_TYPES,
        'hazard_level_choices': ItemMaster.HAZARD_LEVELS,
    }

    return render(request, 'inventory/items_list.html', context)


def items_export_excel(request):
    """
    تصدير قائمة الأصناف إلى Excel
    Export Items List to Excel
    """
    # التحقق من تسجيل الدخول
    if not request.user.is_authenticated:
        messages.error(request, 'يجب تسجيل الدخول أولاً')
        return redirect('/admin/login/')

    # الحصول على معاملات البحث والتصفية
    search_query = request.GET.get('search', '')
    category_filter = request.GET.get('category', '')
    material_type_filter = request.GET.get('material_type', '')
    hazard_level_filter = request.GET.get('hazard_level', '')
    status_filter = request.GET.get('status', 'active')

    # بناء الاستعلام
    items = ItemMaster.objects.all()

    # تطبيق فلتر الحالة
    if status_filter == 'active':
        items = items.filter(is_active=True)
    elif status_filter == 'inactive':
        items = items.filter(is_active=False)

    # تطبيق فلاتر البحث
    if search_query:
        items = items.filter(
            Q(item_name_ar__icontains=search_query) |
            Q(item_name_en__icontains=search_query) |
            Q(item_code__icontains=search_query) |
            Q(chemical_formula__icontains=search_query) |
            Q(cas_number__icontains=search_query)
        )

    if category_filter:
        items = items.filter(category=category_filter)

    if material_type_filter:
        items = items.filter(material_type=material_type_filter)

    if hazard_level_filter:
        items = items.filter(hazard_level=hazard_level_filter)

    items = items.order_by('item_name_ar')

    # إنشاء ملف Excel
    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = "قائمة الأصناف"

    # تنسيق الخط العربي
    arabic_font = openpyxl.styles.Font(name='Arial', size=11, bold=False)
    header_font = openpyxl.styles.Font(name='Arial', size=12, bold=True, color='FFFFFF')

    # تنسيق الخلفية
    header_fill = openpyxl.styles.PatternFill(start_color='D62828', end_color='D62828', fill_type='solid')

    # تنسيق المحاذاة
    center_alignment = openpyxl.styles.Alignment(horizontal='center', vertical='center')
    right_alignment = openpyxl.styles.Alignment(horizontal='right', vertical='center')

    # تنسيق الحدود
    thin_border = openpyxl.styles.Border(
        left=openpyxl.styles.Side(style='thin'),
        right=openpyxl.styles.Side(style='thin'),
        top=openpyxl.styles.Side(style='thin'),
        bottom=openpyxl.styles.Side(style='thin')
    )

    # رأس الجدول
    headers = [
        'كود الصنف',
        'اسم الصنف (عربي)',
        'اسم الصنف (إنجليزي)',
        'الفئة',
        'نوع المادة',
        'وحدة القياس',
        'مستوى الخطر',
        'الحد الأدنى للمخزون',
        'الصيغة الكيميائية',
        'رقم CAS',
        'الحالة',
        'تاريخ الإنشاء'
    ]

    # كتابة رأس الجدول
    for col, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = thin_border

    # كتابة البيانات
    for row, item in enumerate(items, 2):
        # كود الصنف
        cell = worksheet.cell(row=row, column=1)
        cell.value = item.item_code
        cell.font = arabic_font
        cell.alignment = center_alignment
        cell.border = thin_border

        # اسم الصنف (عربي)
        cell = worksheet.cell(row=row, column=2)
        cell.value = item.item_name_ar
        cell.font = arabic_font
        cell.alignment = right_alignment
        cell.border = thin_border

        # اسم الصنف (إنجليزي)
        cell = worksheet.cell(row=row, column=3)
        cell.value = item.item_name_en or ''
        cell.font = arabic_font
        cell.alignment = right_alignment
        cell.border = thin_border

        # الفئة
        cell = worksheet.cell(row=row, column=4)
        cell.value = item.get_category_display()
        cell.font = arabic_font
        cell.alignment = right_alignment
        cell.border = thin_border

        # نوع المادة
        cell = worksheet.cell(row=row, column=5)
        cell.value = item.get_material_type_display()
        cell.font = arabic_font
        cell.alignment = right_alignment
        cell.border = thin_border

        # وحدة القياس
        cell = worksheet.cell(row=row, column=6)
        cell.value = item.get_unit_of_measure_display()
        cell.font = arabic_font
        cell.alignment = center_alignment
        cell.border = thin_border

        # مستوى الخطر
        cell = worksheet.cell(row=row, column=7)
        cell.value = item.get_hazard_level_display()
        cell.font = arabic_font
        cell.alignment = center_alignment
        cell.border = thin_border

        # الحد الأدنى للمخزون
        cell = worksheet.cell(row=row, column=8)
        cell.value = float(item.minimum_stock_level) if item.minimum_stock_level else 0
        cell.font = arabic_font
        cell.alignment = center_alignment
        cell.border = thin_border

        # الصيغة الكيميائية
        cell = worksheet.cell(row=row, column=9)
        cell.value = item.chemical_formula or ''
        cell.font = arabic_font
        cell.alignment = center_alignment
        cell.border = thin_border

        # رقم CAS
        cell = worksheet.cell(row=row, column=10)
        cell.value = item.cas_number or ''
        cell.font = arabic_font
        cell.alignment = center_alignment
        cell.border = thin_border

        # الحالة
        cell = worksheet.cell(row=row, column=11)
        cell.value = 'نشط' if item.is_active else 'غير نشط'
        cell.font = arabic_font
        cell.alignment = center_alignment
        cell.border = thin_border

        # تاريخ الإنشاء
        cell = worksheet.cell(row=row, column=12)
        cell.value = item.created_at.strftime('%Y-%m-%d') if item.created_at else ''
        cell.font = arabic_font
        cell.alignment = center_alignment
        cell.border = thin_border

    # تنسيق عرض الأعمدة
    column_widths = [15, 25, 25, 15, 15, 12, 12, 15, 20, 15, 10, 12]
    for col, width in enumerate(column_widths, 1):
        worksheet.column_dimensions[get_column_letter(col)].width = width

    # إعداد الاستجابة
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )

    # تحديد اسم الملف
    filename = f'items_list_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    response['Content-Disposition'] = f'attachment; filename="{filename}"'

    # حفظ الملف
    workbook.save(response)
    return response


def test_view(request):
    """
    صفحة اختبار للتأكد من عمل النظام
    Test view to ensure system is working
    """
    return HttpResponse("""
    <div style="text-align: center; padding: 50px; font-family: Arial;">
        <h1 style="color: #D62828;">🎉 مرحباً بك في KamaVerse!</h1>
        <h2 style="color: #C89A3C;">نظام إدارة المخزون - شركة القماش</h2>
        <p style="color: #4A4F57; font-size: 18px;">النظام يعمل بنجاح! ✅</p>
        <p style="color: #4A4F57;">Django 5.1 + SQLite + Arabic RTL Support</p>
    </div>
    """)


# ==================== WAREHOUSE MANAGEMENT VIEWS ====================

def warehouses_list(request):
    """
    صفحة قائمة المخازن
    Warehouses List Page
    """
    # التحقق من تسجيل الدخول
    if not request.user.is_authenticated:
        messages.error(request, 'يجب تسجيل الدخول أولاً')
        return redirect('/admin/login/')

    # تطبيق نظام الصلاحيات
    try:
        user_profile = UserProfile.objects.get(user=request.user)
        warehouses = user_profile.get_accessible_warehouses()
    except UserProfile.DoesNotExist:
        # إذا لم يكن للمستخدم ملف شخصي، يمكنه الوصول لجميع المخازن (للمدير)
        warehouses = Warehouse.objects.all()

    # البحث والفلترة
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')

    if search_query:
        warehouses = warehouses.filter(
            Q(warehouse_name__icontains=search_query) |
            Q(warehouse_code__icontains=search_query) |
            Q(address__icontains=search_query)
        )

    if status_filter:
        if status_filter == 'active':
            warehouses = warehouses.filter(is_active=True)
        elif status_filter == 'inactive':
            warehouses = warehouses.filter(is_active=False)

    # إضافة إحصائيات لكل مخزن
    warehouses_with_stats = []
    for warehouse in warehouses:
        # حساب عدد المواقع
        total_locations = BinLocation.objects.filter(warehouse=warehouse).count()
        occupied_locations = BinLocation.objects.filter(
            warehouse=warehouse,
            current_quantity__gt=0
        ).count()

        # حساب إجمالي المخزون
        total_stock_value = StockBalance.objects.filter(
            bin_location__warehouse=warehouse
        ).aggregate(total=Sum('total_value'))['total'] or 0

        # حساب عدد الأصناف الفريدة
        unique_items_count = StockBalance.objects.filter(
            bin_location__warehouse=warehouse
        ).values('item').distinct().count()

        warehouses_with_stats.append({
            'warehouse': warehouse,
            'total_locations': total_locations,
            'occupied_locations': occupied_locations,
            'occupancy_percentage': warehouse.get_occupancy_percentage(),
            'available_capacity': warehouse.get_available_capacity(),
            'total_stock_value': total_stock_value,
            'unique_items_count': unique_items_count,
        })

    # ترقيم الصفحات
    paginator = Paginator(warehouses_with_stats, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_title': 'إدارة المخازن',
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'total_warehouses': warehouses.count(),
    }

    return render(request, 'inventory/warehouses_list.html', context)


def warehouse_detail(request, pk):
    """
    صفحة تفاصيل المخزن
    Warehouse Detail Page
    """
    # التحقق من تسجيل الدخول
    if not request.user.is_authenticated:
        messages.error(request, 'يجب تسجيل الدخول أولاً')
        return redirect('/admin/login/')

    warehouse = get_object_or_404(Warehouse, pk=pk)

    # التحقق من الصلاحيات
    try:
        user_profile = UserProfile.objects.get(user=request.user)
        if not user_profile.can_access_warehouse(warehouse):
            messages.error(request, 'ليس لديك صلاحية للوصول إلى هذا المخزن')
            return redirect('inventory:warehouses_list')
    except UserProfile.DoesNotExist:
        # المدير يمكنه الوصول لجميع المخازن
        pass

    # إحصائيات المخزن
    total_locations = BinLocation.objects.filter(warehouse=warehouse).count()
    occupied_locations = BinLocation.objects.filter(
        warehouse=warehouse,
        current_quantity__gt=0
    ).count()

    # الأصناف الموجودة في المخزن
    stock_items = StockBalance.objects.filter(
        bin_location__warehouse=warehouse,
        current_quantity__gt=0
    ).select_related('item', 'bin_location').order_by('item__item_name_ar')

    # حساب عدد الأصناف الفريدة
    total_items = stock_items.values('item').distinct().count()

    # حساب نسب الإشغال والفراغ
    occupancy_percentage = warehouse.get_occupancy_percentage()
    available_percentage = 100 - occupancy_percentage

    context = {
        'page_title': f'تفاصيل المخزن - {warehouse.warehouse_name}',
        'warehouse': warehouse,
        'total_locations': total_locations,
        'occupied_locations': occupied_locations,
        'total_items': total_items,
        'occupancy_percentage': occupancy_percentage,
        'available_percentage': available_percentage,
        'stock_items': stock_items,
    }

    return render(request, 'inventory/warehouse_detail.html', context)


def bin_locations_list(request, warehouse_id):
    """
    صفحة إدارة مواقع التخزين
    Bin Locations Management Page
    """
    warehouse = get_object_or_404(Warehouse, id=warehouse_id)

    # البحث والفلترة
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')

    bin_locations = BinLocation.objects.filter(warehouse=warehouse)

    if search_query:
        bin_locations = bin_locations.filter(
            Q(bin_code__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    if status_filter:
        if status_filter == 'occupied':
            bin_locations = bin_locations.filter(current_quantity__gt=0)
        elif status_filter == 'empty':
            bin_locations = bin_locations.filter(current_quantity=0)
        elif status_filter == 'blocked':
            bin_locations = bin_locations.filter(status='BLOCKED')

    # إضافة معلومات المخزون لكل موقع
    bin_locations_with_stock = []
    for bin_location in bin_locations:
        stock_balance = StockBalance.objects.filter(bin_location=bin_location).first()
        bin_locations_with_stock.append({
            'bin_location': bin_location,
            'stock_balance': stock_balance,
            'utilization_percentage': bin_location.get_utilization_percentage(),
        })

    # ترقيم الصفحات
    paginator = Paginator(bin_locations_with_stock, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_title': f'مواقع التخزين - {warehouse.warehouse_name}',
        'warehouse': warehouse,
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'total_locations': bin_locations.count(),
    }

    return render(request, 'inventory/bin_locations_list.html', context)


def warehouse_create(request):
    """إنشاء مخزن جديد"""
    if request.method == 'POST':
        form = WarehouseForm(request.POST)
        if form.is_valid():
            warehouse = form.save()
            messages.success(request, f'تم إنشاء المخزن "{warehouse.warehouse_name}" بنجاح!')
            return redirect('inventory:warehouse_detail', warehouse_id=warehouse.id)
    else:
        form = WarehouseForm()

    context = {
        'page_title': 'إنشاء مخزن جديد',
        'form': form,
        'form_action': 'create'
    }

    return render(request, 'inventory/warehouse_form.html', context)


def warehouse_edit(request, warehouse_id):
    """تعديل مخزن موجود"""
    warehouse = get_object_or_404(Warehouse, id=warehouse_id)

    if request.method == 'POST':
        form = WarehouseForm(request.POST, instance=warehouse)
        if form.is_valid():
            warehouse = form.save()
            messages.success(request, f'تم تحديث المخزن "{warehouse.warehouse_name}" بنجاح!')
            return redirect('inventory:warehouse_detail', warehouse_id=warehouse.id)
    else:
        form = WarehouseForm(instance=warehouse)

    context = {
        'page_title': f'تعديل المخزن - {warehouse.warehouse_name}',
        'form': form,
        'warehouse': warehouse,
        'form_action': 'edit'
    }

    return render(request, 'inventory/warehouse_form.html', context)


def bin_location_create(request, warehouse_id):
    """إنشاء موقع تخزين جديد"""
    warehouse = get_object_or_404(Warehouse, id=warehouse_id)

    if request.method == 'POST':
        form = BinLocationForm(request.POST, warehouse=warehouse)
        if form.is_valid():
            bin_location = form.save(commit=False)
            bin_location.warehouse = warehouse
            bin_location.save()
            messages.success(request, f'تم إنشاء موقع التخزين "{bin_location.bin_code}" بنجاح!')
            return redirect('inventory:bin_locations_list', warehouse_id=warehouse.id)
    else:
        form = BinLocationForm(warehouse=warehouse)

    context = {
        'page_title': f'إنشاء موقع تخزين جديد - {warehouse.warehouse_name}',
        'form': form,
        'warehouse': warehouse,
        'form_action': 'create'
    }

    return render(request, 'inventory/bin_location_form.html', context)


def warehouse_add_items(request, warehouse_id):
    """إضافة أصناف موجودة للمخزن"""
    warehouse = get_object_or_404(Warehouse, id=warehouse_id)
    
    if request.method == 'POST':
        form = WarehouseItemForm(request.POST)
        if form.is_valid():
            try:
                # الحصول على الصنف المختار والكمية
                selected_item = form.cleaned_data['selected_item']
                initial_quantity = form.cleaned_data['initial_quantity']
                
                # التحقق من عدم وجود الصنف في المخزن بالفعل
                existing_stock = StockBalance.objects.filter(
                    item=selected_item,
                    bin_location__warehouse=warehouse
                ).first()
                
                if existing_stock:
                    # إذا كان الصنف موجود بالفعل، قم بتحديث الكمية
                    existing_stock.current_quantity += initial_quantity
                    existing_stock.available_quantity += initial_quantity
                    existing_stock.save()
                    
                    # تحديث موقع التخزين
                    bin_location = existing_stock.bin_location
                    bin_location.current_quantity += initial_quantity
                    bin_location.save()
                    
                    messages.success(request, f'تم تحديث كمية الصنف "{selected_item.item_name_ar}" في المخزن "{warehouse.warehouse_name}" بنجاح! الكمية الجديدة: {existing_stock.current_quantity}')
                else:
                    # إنشاء رصيد جديد للصنف في المخزن
                    # البحث عن موقع تخزين فارغ أو إنشاء واحد جديد
                    bin_location = BinLocation.objects.filter(
                        warehouse=warehouse,
                        status='ACTIVE'
                    ).order_by('current_quantity').first()
                    
                    if not bin_location:
                        # إنشاء موقع تخزين جديد إذا لم يوجد
                        next_code = f"LOC-{BinLocation.objects.filter(warehouse=warehouse).count() + 1:03d}"
                        bin_location = BinLocation.objects.create(
                            warehouse=warehouse,
                            bin_code=next_code,
                            bin_name=f"موقع {next_code}",
                            aisle='A',
                            rack='01',
                            level='01',
                            capacity=1000.000,
                            status='ACTIVE'
                        )
                    
                    # إنشاء رصيد مخزون جديد
                    stock_balance = StockBalance.objects.create(
                        item=selected_item,
                        warehouse=warehouse,
                        bin_location=bin_location,
                        current_quantity=initial_quantity,
                        available_quantity=initial_quantity,
                        reserved_quantity=0,
                        average_cost=0.000,
                        total_value=0.000,
                        unit_of_measure=selected_item.unit_of_measure
                    )
                    
                    # تحديث موقع التخزين
                    bin_location.current_quantity += initial_quantity
                    bin_location.save()
                    
                    messages.success(request, f'تم إضافة الصنف "{selected_item.item_name_ar}" للمخزن "{warehouse.warehouse_name}" بنجاح! الكمية: {initial_quantity}')
                
                return redirect('inventory:warehouse_detail', pk=warehouse.pk)
                
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء إضافة الصنف: {str(e)}')
    else:
        form = WarehouseItemForm(initial={'warehouse_id': warehouse.pk})
    
    # إضافة بيانات الأصناف للجافاسكريبت
    items_data = []
    for item in ItemMaster.objects.filter(is_active=True).order_by('item_name_ar'):
        items_data.append({
            'id': str(item.id),
            'item_code': item.item_code,
            'item_name_ar': item.item_name_ar,
            'item_name_en': item.item_name_en or '',
            'chemical_formula': item.chemical_formula or '',
            'category': item.get_category_display(),
            'material_type': item.get_material_type_display(),
            'hazard_level': item.get_hazard_level_display(),
            'unit_of_measure': item.get_unit_of_measure_display(),
            'minimum_stock_level': str(item.minimum_stock_level),
            'reorder_point': str(item.reorder_point),
            'shelf_life_months': item.shelf_life_months or 0,
            'notes': item.notes or ''
        })
    
    context = {
        'page_title': f'إضافة صنف للمخزن - {warehouse.warehouse_name}',
        'form': form,
        'warehouse': warehouse,
        'form_action': 'add_to_warehouse',
        'items_data_json': json.dumps(items_data)
    }
    
    return render(request, 'inventory/warehouse_add_items.html', context)


# ==================== حركات المخزون ====================

def movements_list(request):
    """
    صفحة قائمة حركات المخزون
    Stock Movements List
    """
    # التحقق من تسجيل الدخول
    if not request.user.is_authenticated:
        messages.error(request, 'يجب تسجيل الدخول أولاً')
        return redirect('/admin/login/')

    # تطبيق نظام الصلاحيات
    try:
        user_profile = UserProfile.objects.get(user=request.user)
        accessible_warehouses = user_profile.get_accessible_warehouses()
    except UserProfile.DoesNotExist:
        # إذا لم يكن للمستخدم ملف شخصي، يمكنه الوصول لجميع المخازن (للمدير)
        accessible_warehouses = Warehouse.objects.filter(is_active=True)

    # حساب إحصائيات أذون الصرف والاستلام
    from datetime import date, datetime
    from django.utils import timezone

    today = timezone.now().date()
    current_month = today.replace(day=1)

    # إحصائيات أذون الصرف
    today_issues = GoodsIssue.objects.filter(
        issue_date__date=today,
        status='CONFIRMED'
    ).count()

    month_issues = GoodsIssue.objects.filter(
        issue_date__date__gte=current_month,
        status='CONFIRMED'
    ).count()

    # إحصائيات أذون الاستلام
    today_receipts = GoodsReceipt.objects.filter(
        receipt_date__date=today,
        status='CONFIRMED'
    ).count()

    month_receipts = GoodsReceipt.objects.filter(
        receipt_date__date__gte=current_month,
        status='CONFIRMED'
    ).count()

    # إحصائيات نقل المخزون
    today_transfers = StockTransfer.objects.filter(
        transfer_date__date=today,
        status='COMPLETED'
    ).count()

    month_transfers = StockTransfer.objects.filter(
        transfer_date__date__gte=current_month,
        status='COMPLETED'
    ).count()

    context = {
        'page_title': 'حركات المخزون',
        'accessible_warehouses': accessible_warehouses,
        'today_issues': today_issues,
        'month_issues': month_issues,
        'today_receipts': today_receipts,
        'month_receipts': month_receipts,
        'today_transfers': today_transfers,
        'month_transfers': month_transfers,
    }
    return render(request, 'inventory/movements_list.html', context)


def goods_receipt_create(request):
    """
    صفحة إنشاء إذن استلام جديد
    Create New Goods Receipt
    """
    # التحقق من تسجيل الدخول
    if not request.user.is_authenticated:
        messages.error(request, 'يجب تسجيل الدخول أولاً')
        return redirect('/admin/login/')

    # التحقق من صلاحيات المستخدم
    user_profile = None
    accessible_warehouses = Warehouse.objects.filter(is_active=True)

    try:
        user_profile = UserProfile.objects.get(user=request.user)
        accessible_warehouses = user_profile.get_accessible_warehouses()
    except UserProfile.DoesNotExist:
        # إذا لم يكن للمستخدم ملف شخصي، يمكنه الوصول لجميع المخازن (للمدير)
        pass

    if request.method == 'POST':
        try:
            # الحصول على بيانات الإذن
            warehouse_id = request.POST.get('warehouse')
            notes = request.POST.get('notes', '')
            data_source = request.POST.get('data_source', 'manual')

            # التحقق من المخزن
            warehouse = get_object_or_404(Warehouse, id=warehouse_id)
            if user_profile and not user_profile.can_access_warehouse(warehouse):
                messages.error(request, 'ليس لديك صلاحية للوصول إلى هذا المخزن')
                return redirect('inventory:goods_receipt_create')

            # إنشاء إذن الاستلام
            receipt = GoodsReceipt.objects.create(
                warehouse=warehouse,
                created_by=request.user,
                notes=notes
            )

            items_data = []

            # معالجة البيانات حسب المصدر
            if data_source == 'excel':
                # معالجة ملف Excel
                excel_file = request.FILES.get('excel_file')
                if excel_file:
                    items_data = process_excel_data(excel_file, warehouse, receipt, request)
                else:
                    receipt.delete()
                    messages.error(request, 'يرجى رفع ملف Excel')
                    return redirect('inventory:goods_receipt_create')
            else:
                # معالجة الإدخال اليدوي
                item_count = int(request.POST.get('item_count', request.POST.get('itemCount', 0)))
                if item_count == 0:
                    receipt.delete()
                    messages.error(request, 'يجب إضافة صنف واحد على الأقل')
                    return redirect('inventory:goods_receipt_create')
                items_data = process_manual_data(request, item_count, warehouse, receipt)

            if items_data:
                # تأكيد الإذن وتحديث المخزون
                receipt.confirm_receipt()

                # توليد PDF تلقائياً وحفظه
                try:
                    # إنشاء مجلد للـ PDFs إذا لم يكن موجوداً
                    import os
                    from django.conf import settings

                    media_root = getattr(settings, 'MEDIA_ROOT', os.path.join(settings.BASE_DIR, 'media'))
                    pdf_dir = os.path.join(media_root, 'receipts_pdf')
                    os.makedirs(pdf_dir, exist_ok=True)

                    # توليد PDF وحفظه
                    pdf_filename = f'receipt_{receipt.receipt_number}.pdf'
                    pdf_path = os.path.join(pdf_dir, pdf_filename)

                    with open(pdf_path, 'wb') as pdf_file:
                        pdf_generator.generate_receipt_pdf(receipt, pdf_file)

                    messages.success(request, f'تم إنشاء إذن الاستلام {receipt.receipt_number} بنجاح وتم توليد PDF')

                    # إضافة رابط تحميل PDF في الرسالة
                    pdf_url = f'/media/receipts_pdf/{pdf_filename}'
                    messages.info(request, f'يمكنك تحميل PDF من <a href="{pdf_url}" target="_blank">هنا</a>')

                except Exception as pdf_error:
                    messages.warning(request, f'تم إنشاء الإذن بنجاح ولكن فشل في توليد PDF: {str(pdf_error)}')

                return redirect('inventory:goods_receipt_detail', pk=receipt.pk)
            else:
                receipt.delete()
                messages.error(request, 'يجب إضافة صنف واحد على الأقل')

        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')

    # الحصول على الأصناف النشطة
    items = ItemMaster.objects.filter(is_active=True).order_by('item_name_ar')

    context = {
        'page_title': 'إذن استلام جديد',
        'warehouses': accessible_warehouses,
        'items': items,
        'user_profile': user_profile,
    }

    return render(request, 'inventory/goods_receipt_create.html', context)


def goods_receipt_detail(request, pk):
    """
    صفحة تفاصيل إذن الاستلام
    Goods Receipt Detail
    """
    # التحقق من تسجيل الدخول
    if not request.user.is_authenticated:
        messages.error(request, 'يجب تسجيل الدخول أولاً')
        return redirect('/admin/login/')

    receipt = get_object_or_404(GoodsReceipt, pk=pk)

    # التحقق من الصلاحيات
    try:
        user_profile = UserProfile.objects.get(user=request.user)
        if not user_profile.can_access_warehouse(receipt.warehouse):
            messages.error(request, 'ليس لديك صلاحية لعرض هذا الإذن')
            return redirect('inventory:movements_list')
    except UserProfile.DoesNotExist:
        # المدير يمكنه الوصول لجميع الإيصالات
        pass

    context = {
        'page_title': f'إذن استلام {receipt.receipt_number}',
        'receipt': receipt,
    }

    return render(request, 'inventory/goods_receipt_detail.html', context)


def goods_receipt_print(request, pk):
    """
    صفحة طباعة إذن الاستلام
    Print Goods Receipt
    """
    # التحقق من تسجيل الدخول
    if not request.user.is_authenticated:
        messages.error(request, 'يجب تسجيل الدخول أولاً')
        return redirect('/admin/login/')

    receipt = get_object_or_404(GoodsReceipt, pk=pk)

    context = {
        'page_title': f'طباعة إذن {receipt.receipt_number}',
        'receipt': receipt,
    }

    return render(request, 'inventory/goods_receipt_print.html', context)


def process_excel_data(excel_file, warehouse, receipt, request=None):
    """معالجة بيانات ملف Excel"""
    processor = ExcelProcessor()

    if processor.process_excel_file(excel_file, warehouse):
        # إنشاء الأصناف الجديدة إذا لزم الأمر
        processor.create_items_from_data()

        items_data = []
        for data in processor.processed_data:
            # إنشاء الصنف الجديد إذا لم يكن موجوداً
            if not data['item'] and data['item_status'] == "صنف جديد - سيتم إنشاؤه":
                try:
                    # إنشاء الصنف الجديد
                    item = ItemMaster.objects.create(
                        item_code=data['item_code'],
                        item_name_ar=data['item_name'],
                        item_name_en=data['item_name'],
                        unit_of_measure=data['unit'],
                        category='CHEMICAL',  # افتراضي
                        is_active=True
                    )
                    data['item'] = item
                    if request:
                        messages.success(request, f'تم إنشاء الصنف الجديد: {item.item_name_ar}')
                except Exception as e:
                    if request:
                        messages.error(request, f'خطأ في إنشاء الصنف {data["item_code"]}: {str(e)}')
                    continue
            
            if data['item'] and data['quantity']:
                # إنشاء صنف في الإذن
                receipt_item = GoodsReceiptItem.objects.create(
                    receipt=receipt,
                    item=data['item'],
                    quantity=data['quantity'],
                    bin_location=get_default_bin_location(warehouse),
                )
                items_data.append(receipt_item)

        # إضافة التحذيرات كرسائل
        if request:
            for warning in processor.warnings:
                messages.warning(request, f'تحذير: {warning}')

        return items_data
    else:
        # إضافة الأخطاء كرسائل
        if request:
            for error in processor.errors:
                messages.error(request, f'خطأ في Excel: {error}')
        return []


def process_manual_data(request, item_count, warehouse, receipt):
    """معالجة بيانات الإدخال اليدوي"""
    items_data = []

    # البحث عن جميع الحقول المرسلة
    processed_items = 0
    i = 1
    while processed_items < item_count and i <= 100:  # حد أقصى 100 صف لتجنب اللوب اللانهائي
        item_name_id = request.POST.get(f'item_name_{i}')
        quantity_str = request.POST.get(f'quantity_{i}')
        unit = request.POST.get(f'unit_{i}')
        bin_location_id = request.POST.get(f'bin_location_{i}')

        # إذا لم نجد البيانات بهذا الفهرس، جرب الفهرس التالي
        if not item_name_id:
            i += 1
            continue

        if item_name_id and quantity_str and unit:
            try:
                # التعامل مع الأصناف الجديدة
                if item_name_id == 'new_item':
                    new_item_name = request.POST.get(f'new_item_name_{i}')
                    new_item_code = request.POST.get(f'new_item_code_{i}')

                    if new_item_name and new_item_code:
                        # إنشاء صنف جديد
                        item = ItemMaster.objects.create(
                            item_code=new_item_code,
                            item_name_ar=new_item_name,
                            item_name_en=new_item_name,
                            unit_of_measure=unit,
                            category='CHEMICAL',  # افتراضي
                            is_active=True
                        )
                    else:
                        i += 1
                        continue
                else:
                    item = get_object_or_404(ItemMaster, id=item_name_id)

                quantity = Decimal(quantity_str)

                # الحصول على موقع التخزين
                bin_location = None
                if bin_location_id:
                    bin_location = get_object_or_404(BinLocation, id=bin_location_id)
                else:
                    bin_location = get_default_bin_location(warehouse)

                # إنشاء صنف في الإذن
                receipt_item = GoodsReceiptItem.objects.create(
                    receipt=receipt,
                    item=item,
                    quantity=quantity,
                    bin_location=bin_location
                )
                items_data.append(receipt_item)
                processed_items += 1

            except Exception as e:
                messages.error(request, f'خطأ في الصنف {i}: {str(e)}')

        i += 1

    return items_data


def get_default_bin_location(warehouse):
    """الحصول على موقع تخزين افتراضي"""
    return BinLocation.objects.filter(
        warehouse=warehouse,
        is_active=True
    ).first()


def goods_receipt_pdf(request, pk):
    """تحميل PDF لإذن الاستلام"""
    receipt = get_object_or_404(GoodsReceipt, pk=pk)

    # التحقق من الصلاحيات
    try:
        user_profile = UserProfile.objects.get(user=request.user)
        if not user_profile.can_access_warehouse(receipt.warehouse):
            messages.error(request, 'ليس لديك صلاحية لتحميل هذا الإذن')
            return redirect('inventory:movements_list')
    except UserProfile.DoesNotExist:
        pass  # المدير يمكنه الوصول لجميع الإيصالات

    return pdf_generator.generate_receipt_pdf(receipt)


def daily_receipts_report(request):
    """تقرير إيصالات الاستلام اليومي"""
    target_date = request.GET.get('date')
    if target_date:
        try:
            target_date = datetime.strptime(target_date, '%Y-%m-%d').date()
        except ValueError:
            target_date = date.today()
    else:
        target_date = date.today()

    # الحصول على إيصالات اليوم
    receipts = GoodsReceipt.objects.filter(
        receipt_date__date=target_date,
        status='CONFIRMED'
    ).order_by('-receipt_date')

    return pdf_generator.generate_daily_report(target_date, receipts)


def monthly_receipts_report(request):
    """تقرير إيصالات الاستلام الشهري"""
    year = int(request.GET.get('year', date.today().year))
    month = int(request.GET.get('month', date.today().month))

    # الحصول على إيصالات الشهر
    receipts = GoodsReceipt.objects.filter(
        receipt_date__year=year,
        receipt_date__month=month,
        status='CONFIRMED'
    ).order_by('-receipt_date')

    return pdf_generator.generate_monthly_report(year, month, receipts)


def download_excel_template(request):
    """تحميل قالب Excel"""
    processor = ExcelProcessor()
    workbook = processor.generate_template()

    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = 'attachment; filename="goods_receipt_template.xlsx"'

    workbook.save(response)
    return response


def process_excel_preview(request):
    """معالجة ملف Excel وإرجاع بيانات المعاينة"""
    if request.method == 'POST' and request.FILES.get('excel_file'):
        excel_file = request.FILES['excel_file']
        warehouse_id = request.POST.get('warehouse')
        
        try:
            warehouse = get_object_or_404(Warehouse, id=warehouse_id)
            processor = ExcelProcessor()
            
            if processor.process_excel_file(excel_file, warehouse):
                # تحضير بيانات المعاينة
                preview_data = []
                for data in processor.processed_data:
                    preview_data.append({
                        'item_code': data['item_code'],
                        'item_name': data['item_name'],
                        'quantity': str(data['quantity']),
                        'unit': data.get('unit_display', data['unit']),  # استخدام النص العربي للعرض
                        'status': data['item_status'],
                        'status_class': 'success' if 'موجود' in data['item_status'] else 'info'
                    })
                
                return JsonResponse({
                    'success': True,
                    'data': preview_data,
                    'total_items': len(preview_data),
                    'warnings': processor.warnings,
                    'errors': processor.errors
                })
            else:
                return JsonResponse({
                    'success': False,
                    'errors': processor.errors
                })
                
        except Exception as e:
            return JsonResponse({
                'success': False,
                'errors': [f'خطأ في معالجة الملف: {str(e)}']
            })
    
    return JsonResponse({'success': False, 'errors': ['لم يتم رفع ملف']})


def process_issue_excel_preview(request):
    """معالجة ملف Excel لإذن الصرف وإرجاع بيانات المعاينة"""
    if request.method == 'POST' and request.FILES.get('excel_file'):
        excel_file = request.FILES['excel_file']
        
        try:
            # الحصول على معرف المستخدم للمخزن
            user_warehouse = UserProfile.get_user_warehouse(request.user)
            if not user_warehouse:
                return JsonResponse({
                    'success': False,
                    'errors': ['لم يتم تحديد مخزن للمستخدم']
                })
            
            processor = ExcelProcessor()
            
            if processor.process_issue_excel_file(excel_file, user_warehouse):
                # تحضير بيانات المعاينة
                preview_data = []
                for data in processor.processed_data:
                    # الحصول على معلومات الصنف
                    item_status = 'غير محدد'
                    status_class = 'info'
                    
                    # البحث عن الصنف في قاعدة البيانات
                    try:
                        item = ItemMaster.objects.get(item_code=data['item_code'])
                        item_status = 'صنف موجود'
                        status_class = 'success'
                        
                        # التحقق من الكمية المتاحة
                        available_stock = StockBalance.objects.filter(
                            item=item,
                            warehouse=user_warehouse
                        ).aggregate(total=Sum('available_quantity'))['total'] or 0
                        
                        if available_stock < data['quantity']:
                            item_status = f'كمية غير كافية (متاح: {available_stock})'
                            status_class = 'warning'
                        
                    except ItemMaster.DoesNotExist:
                        item_status = 'صنف غير موجود'
                        status_class = 'danger'
                    
                    preview_data.append({
                        'item_code': data['item_code'],
                        'item_name': data['item_name'],
                        'quantity': str(data['quantity']),
                        'unit': data.get('unit_display', data['unit']),
                        'status': item_status,
                        'status_class': status_class
                    })
                
                return JsonResponse({
                    'success': True,
                    'data': preview_data,
                    'total_items': len(preview_data),
                    'warnings': processor.warnings,
                    'errors': processor.errors
                })
            else:
                return JsonResponse({
                    'success': False,
                    'errors': processor.errors
                })
                
        except Exception as e:
            return JsonResponse({
                'success': False,
                'errors': [f'خطأ في معالجة الملف: {str(e)}']
            })
    
    return JsonResponse({'success': False, 'errors': ['لم يتم رفع ملف']})


def reports_dashboard(request):
    """لوحة تحكم التقارير"""
    # التحقق من تسجيل الدخول
    if not request.user.is_authenticated:
        messages.error(request, 'يجب تسجيل الدخول أولاً')
        return redirect('inventory:login')
    
    # التحقق من أن المستخدم هو admin فقط
    if not check_manager_access(request.user):
        messages.error(request, 'غير مصرح لك بالوصول لصفحات التقارير - يسمح للمدير المسؤول (admin) فقط')
        return redirect('inventory:dashboard')
    
    from datetime import date, timedelta

    today = date.today()
    yesterday = today - timedelta(days=1)
    current_year = today.year
    current_month = today.month

    # إحصائيات اليوم
    today_receipts = GoodsReceipt.objects.filter(
        receipt_date__date=today,
        status='CONFIRMED'
    )
    today_stats = {
        'receipts_count': today_receipts.count(),
        'items_count': sum(r.total_items for r in today_receipts)
    }

    # إحصائيات الشهر
    month_receipts = GoodsReceipt.objects.filter(
        receipt_date__year=current_year,
        receipt_date__month=current_month,
        status='CONFIRMED'
    )
    month_stats = {
        'receipts_count': month_receipts.count(),
        'items_count': sum(r.total_items for r in month_receipts)
    }

    # أنشط المخازن
    active_warehouses = []
    for warehouse in Warehouse.objects.filter(is_active=True):
        receipts_count = GoodsReceipt.objects.filter(
            warehouse=warehouse,
            receipt_date__year=current_year,
            receipt_date__month=current_month,
            status='CONFIRMED'
        ).count()
        if receipts_count > 0:
            active_warehouses.append({
                'name': warehouse.warehouse_name,
                'receipts_count': receipts_count
            })

    active_warehouses.sort(key=lambda x: x['receipts_count'], reverse=True)
    active_warehouses = active_warehouses[:5]  # أفضل 5 مخازن

    # قائمة السنوات والشهور
    years = list(range(current_year - 2, current_year + 2))
    months = [
        {'number': 1, 'name': 'يناير'}, {'number': 2, 'name': 'فبراير'},
        {'number': 3, 'name': 'مارس'}, {'number': 4, 'name': 'أبريل'},
        {'number': 5, 'name': 'مايو'}, {'number': 6, 'name': 'يونيو'},
        {'number': 7, 'name': 'يوليو'}, {'number': 8, 'name': 'أغسطس'},
        {'number': 9, 'name': 'سبتمبر'}, {'number': 10, 'name': 'أكتوبر'},
        {'number': 11, 'name': 'نوفمبر'}, {'number': 12, 'name': 'ديسمبر'}
    ]

    # الشهر الماضي
    last_month = today.replace(day=1) - timedelta(days=1)

    context = {
        'page_title': 'مركز التقارير',
        'today_stats': today_stats,
        'month_stats': month_stats,
        'active_warehouses': active_warehouses,
        'years': years,
        'months': months,
        'current_year': current_year,
        'current_month': current_month,
        'yesterday': yesterday,
        'last_month': last_month,
        'warehouses': Warehouse.objects.filter(is_active=True)
    }

    return render(request, 'inventory/reports_dashboard.html', context)


def custom_receipts_report(request):
    """تقرير إيصالات الاستلام المخصص"""
    from datetime import datetime

    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    warehouse_id = request.GET.get('warehouse')

    if not start_date or not end_date:
        messages.error(request, 'يرجى تحديد تاريخ البداية والنهاية')
        return redirect('inventory:reports_dashboard')

    try:
        start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
    except ValueError:
        messages.error(request, 'تنسيق التاريخ غير صحيح')
        return redirect('inventory:reports_dashboard')

    # بناء الاستعلام
    receipts = GoodsReceipt.objects.filter(
        receipt_date__date__gte=start_date,
        receipt_date__date__lte=end_date,
        status='CONFIRMED'
    )

    if warehouse_id:
        receipts = receipts.filter(warehouse_id=warehouse_id)

    receipts = receipts.order_by('-receipt_date')

    # توليد PDF
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="custom_report_{start_date}_{end_date}.pdf"'

    return pdf_generator.generate_custom_report(start_date, end_date, receipts, response)


# ===== Goods Issue Views - إذن الصرف =====

def goods_issue_dashboard(request):
    """لوحة تحكم إذن الصرف - الصفحة الرئيسية"""

    # حساب إحصائيات المبيعات
    sales_stats = GoodsIssue.objects.filter(
        issue_type='SALE',
        status='CONFIRMED'
    ).aggregate(
        total_count=Count('id'),
        total_quantity=Sum('total_quantity')
    )

    # حساب إحصائيات التلف
    damage_stats = GoodsIssue.objects.filter(
        issue_type='DAMAGE',
        status='CONFIRMED'
    ).aggregate(
        total_count=Count('id'),
        total_quantity=Sum('total_quantity')
    )

    context = {
        'page_title': 'إذن الصرف - لوحة التحكم',
        'sales_count': sales_stats['total_count'] or 0,
        'sales_quantity': sales_stats['total_quantity'] or 0,
        'damage_count': damage_stats['total_count'] or 0,
        'damage_quantity': damage_stats['total_quantity'] or 0,
    }

    return render(request, 'inventory/goods_issue_dashboard.html', context)


def goods_issue_sales_list(request):
    """قائمة فواتير المبيعات"""

    sales_issues = GoodsIssue.objects.filter(
        issue_type='SALE'
    ).order_by('-created_at')

    # تطبيق الفلاتر
    search_query = request.GET.get('search', '')
    if search_query:
        sales_issues = sales_issues.filter(
            Q(issue_number__icontains=search_query) |
            Q(notes__icontains=search_query)
        )

    # تقسيم الصفحات
    paginator = Paginator(sales_issues, 20)
    page_number = request.GET.get('page')
    sales_issues = paginator.get_page(page_number)

    context = {
        'page_title': 'فواتير المبيعات',
        'issues': sales_issues,
        'search_query': search_query,
        'issue_type': 'SALE',
    }

    return render(request, 'inventory/goods_issue_list.html', context)


def goods_issue_damage_list(request):
    """قائمة فواتير التلف"""

    damage_issues = GoodsIssue.objects.filter(
        issue_type='DAMAGE'
    ).order_by('-created_at')

    # تطبيق الفلاتر
    search_query = request.GET.get('search', '')
    if search_query:
        damage_issues = damage_issues.filter(
            Q(issue_number__icontains=search_query) |
            Q(notes__icontains=search_query)
        )

    # تقسيم الصفحات
    paginator = Paginator(damage_issues, 20)
    page_number = request.GET.get('page')
    damage_issues = paginator.get_page(page_number)

    context = {
        'page_title': 'فواتير التلف',
        'issues': damage_issues,
        'search_query': search_query,
        'issue_type': 'DAMAGE',
    }

    return render(request, 'inventory/goods_issue_list.html', context)


def goods_issue_create(request):
    """إنشاء إذن صرف جديد"""

    # التحقق من صلاحيات المستخدم
    user_warehouse = UserProfile.get_user_warehouse(request.user)
    if not user_warehouse:
        messages.error(request, 'لا يمكنك إنشاء إذن صرف بدون تخصيص مخزن')
        return redirect('inventory:goods_issue_dashboard')

    if request.method == 'POST':
        try:
            # إنشاء إذن الصرف
            issue = GoodsIssue.objects.create(
                issue_type=request.POST.get('issue_type'),
                warehouse=user_warehouse,
                created_by=request.user,
                notes=request.POST.get('notes', '')
            )

            # معالجة البيانات حسب المصدر
            data_source = request.POST.get('data_source', 'manual')
            items_data = []

            if data_source == 'excel':
                # معالجة بيانات Excel من الحقول المخفية
                excel_item_count = int(request.POST.get('excel_item_count', 0))
                if excel_item_count > 0:
                    items_data = process_excel_hidden_fields(request, excel_item_count, user_warehouse, issue)
                else:
                    issue.delete()
                    messages.error(request, 'لا توجد بيانات صحيحة من ملف Excel')
                    return redirect('inventory:goods_issue_create')
            else:
                # معالجة الإدخال اليدوي
                item_count = int(request.POST.get('item_count', 0))
                if item_count == 0:
                    issue.delete()
                    messages.error(request, 'يجب إضافة صنف واحد على الأقل')
                    return redirect('inventory:goods_issue_create')
                items_data = process_issue_manual_data(request, item_count, user_warehouse, issue)

            if items_data:
                # حساب المجاميع
                issue.total_items = len(items_data)
                issue.total_quantity = sum(item['quantity'] for item in items_data)
                issue.save()

                # تأكيد الإذن وتحديث المخزون
                try:
                    confirm_result = issue.confirm_issue()
                    if confirm_result:
                        messages.success(request, f'تم حفظ إذن الصرف رقم {issue.issue_number} بنجاح وتم تحديث المخزون!')
                    else:
                        messages.warning(request, f'تم حفظ إذن الصرف رقم {issue.issue_number} ولكن لم يتم تحديث المخزون (الإذن ليس في حالة مسودة)')
                except Exception as e:
                    messages.error(request, f'تم حفظ إذن الصرف ولكن حدث خطأ في تحديث المخزون: {str(e)}')

                # تحويل المستخدم إلى صفحة طباعة الإذن كما طلب المستخدم
                return redirect('inventory:goods_issue_print', pk=issue.id)
            else:
                issue.delete()
                messages.error(request, 'حدث خطأ في معالجة البيانات')
                return redirect('inventory:goods_issue_create')

        except Exception as e:
            messages.error(request, f'حدث خطأ: {str(e)}')
            return redirect('inventory:goods_issue_create')

    # جلب البيانات للعرض
    items = ItemMaster.objects.filter(is_active=True).order_by('item_name_ar')
    warehouses = [user_warehouse] if user_warehouse else []
    bin_locations = BinLocation.objects.filter(warehouse=user_warehouse) if user_warehouse else []

    context = {
        'page_title': 'إنشاء إذن صرف جديد',
        'items': items,
        'warehouses': warehouses,
        'bin_locations': bin_locations,
        'user_warehouse': user_warehouse,
    }

    return render(request, 'inventory/goods_issue_create.html', context)


def goods_issue_detail(request, pk):
    """عرض تفاصيل إذن الصرف"""
    issue = get_object_or_404(GoodsIssue, pk=pk)

    context = {
        'page_title': f'تفاصيل إذن الصرف - {issue.issue_number}',
        'issue': issue,
    }

    return render(request, 'inventory/goods_issue_detail.html', context)


def goods_issue_print(request, pk):
    """صفحة طباعة إذن الصرف"""
    issue = get_object_or_404(GoodsIssue, pk=pk)

    context = {
        'page_title': f'طباعة إذن الصرف - {issue.issue_number}',
        'issue': issue,
    }

    return render(request, 'inventory/goods_issue_print.html', context)


def goods_issue_pdf(request, pk):
    """توليد PDF لإذن الصرف"""
    issue = get_object_or_404(GoodsIssue, pk=pk)

    # توليد PDF
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="goods_issue_{issue.issue_number}.pdf"'

    return pdf_generator.generate_goods_issue_pdf(issue, response)


# ===== Helper Functions - دوال مساعدة =====

def process_issue_manual_data(request, item_count, warehouse, issue):
    """معالجة البيانات اليدوية لإذن الصرف"""
    items_data = []

    for i in range(1, item_count + 1):
        item_id = request.POST.get(f'item_{i}')
        quantity = request.POST.get(f'quantity_{i}')
        bin_location_id = request.POST.get(f'bin_location_{i}')

        if item_id and quantity:
            try:
                item = ItemMaster.objects.get(id=item_id)
                quantity = Decimal(quantity)

                # التحقق من توفر الكمية
                available_stock = StockBalance.objects.filter(
                    item=item,
                    warehouse=warehouse
                ).aggregate(total=Sum('available_quantity'))['total'] or 0

                if available_stock < quantity:
                    raise ValueError(f'الكمية المتاحة للصنف {item.item_name_ar} غير كافية')

                bin_location = None
                if bin_location_id:
                    bin_location = BinLocation.objects.get(id=bin_location_id)

                # الحصول على متوسط التكلفة من المخزون
                stock_balance = StockBalance.objects.filter(
                    item=item,
                    warehouse=warehouse
                ).first()
                unit_cost = stock_balance.average_cost if stock_balance else 0

                # إنشاء صنف الإذن
                issue_item = GoodsIssueItem.objects.create(
                    issue=issue,
                    item=item,
                    quantity=quantity,
                    bin_location=bin_location,
                    unit_cost=unit_cost
                )

                items_data.append({
                    'item': item,
                    'quantity': quantity,
                    'bin_location': bin_location
                })

            except Exception as e:
                raise ValueError(f'خطأ في الصنف رقم {i}: {str(e)}')

    return items_data


def process_excel_hidden_fields(request, item_count, warehouse, issue):
    """معالجة بيانات Excel من الحقول المخفية"""
    items_data = []

    try:
        for i in range(item_count):
            # قراءة البيانات من الحقول المخفية
            item_code = request.POST.get(f'excel_item_{i}_code', '').strip()
            item_name = request.POST.get(f'excel_item_{i}_name', '').strip()
            quantity_str = request.POST.get(f'excel_item_{i}_quantity', '').strip()
            bin_location_code = request.POST.get(f'excel_item_{i}_bin_location', '').strip()

            if not item_code or not quantity_str:
                continue

            try:
                quantity = Decimal(str(quantity_str))
                if quantity <= 0:
                    messages.warning(request, f'الكمية للصنف {item_code} يجب أن تكون أكبر من صفر')
                    continue
            except (InvalidOperation, ValueError):
                messages.warning(request, f'الكمية للصنف {item_code} غير صحيحة')
                continue

            # البحث عن الصنف
            item = ItemMaster.objects.filter(item_code=item_code).first()
            if not item:
                messages.warning(request, f'الصنف {item_code} غير موجود في النظام')
                continue

            # التحقق من توفر الكمية في المخزون
            available_stock = StockBalance.objects.filter(
                item=item,
                warehouse=warehouse
            ).aggregate(total=Sum('available_quantity'))['total'] or 0

            if available_stock < quantity:
                messages.warning(request, f'الكمية المتاحة للصنف {item.item_name_ar} ({available_stock}) غير كافية للكمية المطلوبة ({quantity})')
                continue

            # البحث عن موقع التخزين
            bin_location = None
            if bin_location_code:
                bin_location = BinLocation.objects.filter(
                    warehouse=warehouse,
                    bin_code=bin_location_code
                ).first()
                if not bin_location:
                    messages.warning(request, f'موقع التخزين {bin_location_code} غير موجود')

            # الحصول على متوسط التكلفة
            stock_balance = StockBalance.objects.filter(
                item=item,
                warehouse=warehouse
            ).first()
            unit_cost = stock_balance.average_cost if stock_balance else 0

            # إنشاء صنف الإذن
            issue_item = GoodsIssueItem.objects.create(
                issue=issue,
                item=item,
                quantity=quantity,
                bin_location=bin_location,
                unit_cost=unit_cost
            )

            items_data.append({
                'item': item,
                'quantity': quantity,
                'bin_location': bin_location
            })

        return items_data

    except Exception as e:
        messages.error(request, f'خطأ في معالجة بيانات Excel: {str(e)}')
        return []


def process_issue_excel_data(excel_file, warehouse, issue, request):
    """معالجة ملف Excel لإذن الصرف"""
    try:
        processor = ExcelProcessor()
        excel_data = processor.process_issue_excel(excel_file)
        items_data = []

        for row in excel_data:
            item_code = row.get('item_code', '').strip()
            item_name = row.get('item_name', '').strip()
            quantity = row.get('quantity', 0)
            bin_location_name = row.get('bin_location', '').strip()

            if not item_code or not quantity:
                continue

            try:
                # البحث عن الصنف
                item = ItemMaster.objects.filter(item_code=item_code).first()
                if not item:
                    messages.warning(request, f'الصنف {item_code} غير موجود')
                    continue

                quantity = Decimal(str(quantity))

                # التحقق من توفر الكمية
                available_stock = StockBalance.objects.filter(
                    item=item,
                    warehouse=warehouse
                ).aggregate(total=Sum('available_quantity'))['total'] or 0

                if available_stock < quantity:
                    messages.warning(request, f'الكمية المتاحة للصنف {item.item_name_ar} غير كافية')
                    continue

                # البحث عن موقع التخزين
                bin_location = None
                if bin_location_name:
                    bin_location = BinLocation.objects.filter(
                        warehouse=warehouse,
                        location_code=bin_location_name
                    ).first()

                # الحصول على متوسط التكلفة من المخزون
                stock_balance = StockBalance.objects.filter(
                    item=item,
                    warehouse=warehouse
                ).first()
                unit_cost = stock_balance.average_cost if stock_balance else 0

                # إنشاء صنف الإذن
                issue_item = GoodsIssueItem.objects.create(
                    issue=issue,
                    item=item,
                    quantity=quantity,
                    bin_location=bin_location,
                    unit_cost=unit_cost
                )

                items_data.append({
                    'item': item,
                    'quantity': quantity,
                    'bin_location': bin_location
                })

            except Exception as e:
                messages.warning(request, f'خطأ في معالجة الصنف {item_code}: {str(e)}')
                continue

        return items_data

    except Exception as e:
        raise ValueError(f'خطأ في معالجة ملف Excel: {str(e)}')


# ==========================================
# Stock Transfer Views - عروض نقل المخزون
# ==========================================

def stock_transfer_list(request):
    """صفحة قائمة عمليات نقل المخزون"""
    # التحقق من صلاحيات المستخدم
    user_profile = UserProfile.objects.filter(user=request.user).first()

    # الحصول على جميع طلبات النقل
    transfers = StockTransfer.objects.all().order_by('-created_at')

    # فلترة حسب المخزن إذا لم يكن مدير عام
    if user_profile and user_profile.role != 'MANAGER':
        transfers = transfers.filter(
            Q(source_warehouse=user_profile.assigned_warehouse) |
            Q(destination_warehouse=user_profile.assigned_warehouse)
        )

    # فلترة حسب الحالة
    status_filter = request.GET.get('status')
    if status_filter:
        transfers = transfers.filter(status=status_filter)

    # فلترة حسب المخزن
    warehouse_filter = request.GET.get('warehouse')
    if warehouse_filter:
        transfers = transfers.filter(
            Q(source_warehouse_id=warehouse_filter) |
            Q(destination_warehouse_id=warehouse_filter)
        )

    # إحصائيات سريعة
    total_transfers = transfers.count()
    pending_transfers = transfers.filter(status='PENDING').count()
    approved_transfers = transfers.filter(status='APPROVED').count()
    completed_transfers = transfers.filter(status='COMPLETED').count()

    # تقسيم الصفحات
    paginator = Paginator(transfers, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # الحصول على قائمة المخازن للفلترة
    warehouses = Warehouse.objects.filter(is_active=True)

    context = {
        'page_title': 'عمليات نقل المخزون',
        'transfers': page_obj,
        'total_transfers': total_transfers,
        'pending_transfers': pending_transfers,
        'approved_transfers': approved_transfers,
        'completed_transfers': completed_transfers,
        'warehouses': warehouses,
        'status_filter': status_filter,
        'warehouse_filter': warehouse_filter,
        'user_profile': user_profile,
    }

    return render(request, 'inventory/stock_transfer_list.html', context)


def stock_transfer_create(request):
    """صفحة إنشاء طلب نقل مخزون جديد"""
    # التحقق من صلاحيات المستخدم
    user_profile = UserProfile.objects.filter(user=request.user).first()
    if not user_profile or not user_profile.assigned_warehouse:
        messages.error(request, 'غير مصرح لك بإنشاء طلبات نقل')
        return redirect('inventory:stock_transfer_list')

    if request.method == 'POST':
        try:
            # الحصول على البيانات الأساسية
            reason = request.POST.get('reason', '').strip()
            notes = request.POST.get('notes', '').strip()

            # التحقق من البيانات الأساسية
            if not reason:
                messages.error(request, 'يرجى إدخال سبب النقل')
                return redirect('inventory:stock_transfer_create')

            # جمع بيانات الأصناف
            items_data = []
            post_keys = list(request.POST.keys())

            # البحث عن جميع الأصناف المدخلة
            item_numbers = set()
            for key in post_keys:
                if key.startswith('item_'):
                    item_number = key.split('_')[1]
                    item_numbers.add(item_number)

            # معالجة كل صنف
            for item_number in item_numbers:
                item_id = request.POST.get(f'item_{item_number}')
                quantity_str = request.POST.get(f'quantity_{item_number}')
                source_bin_location_id = request.POST.get(f'source_bin_location_{item_number}')

                if item_id and quantity_str:
                    try:
                        quantity = Decimal(quantity_str)
                        if quantity > 0:
                            items_data.append({
                                'item_id': item_id,
                                'quantity': quantity,
                                'source_bin_location_id': source_bin_location_id
                            })
                    except (ValueError, InvalidOperation):
                        continue

            # التحقق من وجود أصناف
            if not items_data:
                messages.error(request, 'يرجى إضافة صنف واحد على الأقل بكمية صحيحة')
                return redirect('inventory:stock_transfer_create')

            # ملاحظة: تم إزالة التحقق من الكميات المتاحة
            # مدير المخزون هو من سيراجع ويقرر قبول أو رفض الطلب

            # إنشاء طلب النقل (بدون مخزن هدف)
            transfer = StockTransfer.objects.create(
                source_warehouse=user_profile.assigned_warehouse,
                reason=reason,
                notes=notes,
                requested_by=request.user
            )

            # إنشاء أصناف الطلب
            for item_data in items_data:
                item = get_object_or_404(ItemMaster, id=item_data['item_id'])
                source_bin_location = None

                if item_data['source_bin_location_id']:
                    source_bin_location = get_object_or_404(BinLocation, id=item_data['source_bin_location_id'])

                StockTransferItem.objects.create(
                    transfer=transfer,
                    item=item,
                    quantity=item_data['quantity'],
                    source_bin_location=source_bin_location
                )

            # إنشاء تنبيه لمدير المخازن
            items_count = len(items_data)
            items_text = f"{items_count} صنف" if items_count > 1 else "صنف واحد"

            Alert.create_alert(
                alert_type='TRANSFER_REQUESTED',
                title=f'طلب نقل مخزون جديد {transfer.transfer_number}',
                message=f'طلب نقل {items_text} من {user_profile.assigned_warehouse.warehouse_name}. السبب: {reason}',
                priority='HIGH',
                warehouse=user_profile.assigned_warehouse
            )

            messages.success(request, f'تم إنشاء طلب النقل {transfer.transfer_number} بنجاح وإرساله للمدير للموافقة')
            return redirect('inventory:stock_transfer_detail', pk=transfer.id)

        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء طلب النقل: {str(e)}')

    # الحصول على البيانات للنموذج
    warehouses = Warehouse.objects.filter(is_active=True).exclude(id=user_profile.assigned_warehouse.id)
    items = ItemMaster.objects.filter(is_active=True)
    source_bin_locations = BinLocation.objects.filter(
        warehouse=user_profile.assigned_warehouse,
        is_active=True
    )

    context = {
        'page_title': 'إنشاء طلب نقل مخزون جديد',
        'warehouses': warehouses,
        'items': items,
        'source_bin_locations': source_bin_locations,
        'user_profile': user_profile,
    }

    return render(request, 'inventory/stock_transfer_create.html', context)


def stock_transfer_detail(request, pk):
    """صفحة تفاصيل طلب نقل المخزون"""
    transfer = get_object_or_404(StockTransfer, pk=pk)

    # التحقق من الصلاحيات
    user_profile = UserProfile.objects.filter(user=request.user).first()
    if user_profile and user_profile.role != 'MANAGER':
        # التحقق من أن المستخدم له علاقة بهذا الطلب
        if (transfer.source_warehouse != user_profile.assigned_warehouse and
            transfer.destination_warehouse != user_profile.assigned_warehouse):
            messages.error(request, 'غير مصرح لك بعرض هذا الطلب')
            return redirect('inventory:stock_transfer_list')

    # الحصول على قائمة المخازن للمدير
    warehouses = []
    if user_profile and user_profile.role == 'MANAGER':
        warehouses = Warehouse.objects.filter(is_active=True)

    context = {
        'page_title': f'تفاصيل طلب النقل {transfer.transfer_number}',
        'transfer': transfer,
        'user_profile': user_profile,
        'warehouses': warehouses,
    }

    return render(request, 'inventory/stock_transfer_detail.html', context)


def stock_transfer_approve(request, pk):
    """اعتماد طلب نقل المخزون مع تحديد المخزن الهدف"""
    # التحقق من أن المستخدم مدير
    user_profile = UserProfile.objects.filter(user=request.user).first()
    if not user_profile or user_profile.role != 'MANAGER':
        messages.error(request, 'غير مصرح لك باعتماد طلبات النقل')
        return redirect('inventory:stock_transfer_list')

    transfer = get_object_or_404(StockTransfer, pk=pk)

    if request.method == 'POST':
        destination_warehouse_id = request.POST.get('destination_warehouse')
        approval_notes = request.POST.get('approval_notes', '')

        # التحقق من اختيار المخزن الهدف
        if not destination_warehouse_id:
            messages.error(request, 'يرجى اختيار المخزن الهدف')
            return redirect('inventory:stock_transfer_detail', pk=pk)

        destination_warehouse = get_object_or_404(Warehouse, id=destination_warehouse_id)

        # التحقق من أن المخزن الهدف مختلف عن المخزن المصدر
        if destination_warehouse == transfer.source_warehouse:
            messages.error(request, 'لا يمكن أن يكون المخزن الهدف نفس المخزن المرسل للطلب')
            return redirect('inventory:stock_transfer_detail', pk=pk)

        # اعتماد الطلب
        try:
            # استخدام الوظيفة الموجودة في النموذج
            if transfer.approve(request.user, destination_warehouse, approval_notes):
                messages.success(request, f'تم اعتماد طلب النقل {transfer.transfer_number} بنجاح وتحديث أرصدة المخازن')
            else:
                messages.error(request, 'فشل في اعتماد الطلب')

        except Exception as e:
            messages.error(request, f'فشل في اعتماد الطلب: {str(e)}')
            return redirect('inventory:stock_transfer_detail', pk=pk)

    return redirect('inventory:stock_transfer_detail', pk=pk)


def stock_transfer_reject(request, pk):
    """رفض طلب نقل المخزون"""
    # التحقق من أن المستخدم مدير
    user_profile = UserProfile.objects.filter(user=request.user).first()
    if not user_profile or user_profile.role != 'MANAGER':
        messages.error(request, 'غير مصرح لك برفض طلبات النقل')
        return redirect('inventory:stock_transfer_list')

    transfer = get_object_or_404(StockTransfer, pk=pk)

    if request.method == 'POST':
        rejection_notes = request.POST.get('rejection_notes', '')

        if transfer.reject(request.user, rejection_notes):
            messages.success(request, f'تم رفض طلب النقل {transfer.transfer_number}')
        else:
            messages.error(request, 'فشل في رفض الطلب')

    return redirect('inventory:stock_transfer_detail', pk=pk)


def login_design_view(request):
    """عرض صفحة تصميم تسجيل الدخول"""
    return render(request, 'inventory/login_design.html')


def custom_login_view(request):
    """تسجيل الدخول الحقيقي للمستخدمين باستخدام التصميم الجميل"""
    if request.user.is_authenticated:
        return redirect('inventory:dashboard')

    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        remember_me = request.POST.get('remember_me')

        if username and password:
            user = authenticate(request, username=username, password=password)
            if user is not None:
                if user.is_active:
                    login(request, user)

                    # تعيين مدة الجلسة حسب "تذكرني"
                    if not remember_me:
                        request.session.set_expiry(0)  # انتهاء عند إغلاق المتصفح
                    else:
                        request.session.set_expiry(1209600)  # أسبوعين

                    messages.success(request, f'مرحباً {user.get_full_name() or user.username}! تم تسجيل الدخول بنجاح')

                    # التوجيه للصفحة المطلوبة أو الصفحة الرئيسية
                    next_url = request.GET.get('next')
                    if next_url:
                        return redirect(next_url)
                    elif user.username == 'admin':
                        # مدير النظام - الذهاب لصفحة إدارة المستخدمين
                        return redirect('inventory:users_list')
                    else:
                        # المستخدمون الآخرون - الذهاب للوحة المعلومات
                        return redirect('inventory:dashboard')
                else:
                    messages.error(request, 'حسابك غير مفعل. يرجى الاتصال بالإدارة')
            else:
                messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة')
        else:
            messages.error(request, 'يرجى إدخال اسم المستخدم وكلمة المرور')

    return render(request, 'inventory/login_design.html')


def custom_logout_view(request):
    """تسجيل الخروج"""
    user_name = request.user.get_full_name() or request.user.username if request.user.is_authenticated else ''
    logout(request)
    messages.success(request, f'تم تسجيل الخروج بنجاح. وداعاً {user_name}!')
    return redirect('inventory:login')


# ==================================================================================
# Reports Access Control - التحكم في الوصول للتقارير
# ==================================================================================

def check_manager_access(user):
    """التحقق من أن المستخدم هو admin فقط"""
    return user.username == 'admin'

# ==================================================================================
# Reports Views - عروض التقارير الأساسية (المرحلة الخامسة)
# ==================================================================================

@login_required(login_url='inventory:login')
def reports_main_dashboard(request):
    """الصفحة الرئيسية للتقارير"""
    # التحقق من أن المستخدم هو admin أو أحمد محمد (manager)
    if request.user.username != 'admin' and request.user.username != 'manager':
        messages.error(request, 'غير مصرح لك بالوصول لصفحات التقارير - يسمح للمدير المسؤول (admin) وأحمد محمد فقط')
        return redirect('inventory:dashboard')
    
    # التحقق من صلاحيات المستخدم
    if request.user.username != 'admin' and request.user.username != 'manager':
        user_warehouse = get_user_warehouse(request.user)
        if not user_warehouse:
            messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
            return redirect('inventory:dashboard')
    else:
        # للمدير وأحمد محمد نجلب المخزن لأغراض العرض فقط
        user_warehouse = get_user_warehouse(request.user)

    # جمع الإحصائيات العامة
    today = timezone.now().date()
    this_month = timezone.now().replace(day=1).date()
    
    context = {
        'user_warehouse': user_warehouse,
        'total_items': ItemMaster.objects.filter(is_active=True).count(),
        'total_warehouses': Warehouse.objects.filter(is_active=True).count(),
        'total_movements_today': StockMovement.objects.filter(
            movement_date__date=today
        ).count(),
        'total_movements_month': StockMovement.objects.filter(
            movement_date__date__gte=this_month
        ).count(),
        # Removed pricing references per requirements
        'total_items_in_stock': StockBalance.objects.filter(
            current_quantity__gt=0
        ).count(),
    }
    
    return render(request, 'inventory/reports/main_dashboard.html', context)

@login_required(login_url='inventory:login')
def current_stock_balances_report(request):
    """تقرير الأرصدة الحالية لجميع الأصناف"""
    # التحقق من أن المستخدم هو admin أو أحمد محمد (manager)
    if request.user.username != 'admin' and request.user.username != 'manager':
        messages.error(request, 'غير مصرح لك بالوصول لصفحات التقارير - يسمح للمدير المسؤول (admin) وأحمد محمد فقط')
        return redirect('inventory:dashboard')
    
    user_warehouse = get_user_warehouse(request.user)
    if not user_warehouse:
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
        return redirect('inventory:dashboard')

    # فلترة حسب المخزن إذا لم يكن مدير عام
    user_profile = UserProfile.objects.filter(user=request.user).first()


@login_required(login_url='inventory:login')
def alerts_list(request):
    """صفحة التنبيهات - متاحة فقط للأدمن والمدير"""
    # التحقق من أن المستخدم هو admin أو manager
    if request.user.username != 'admin' and request.user.username != 'manager':
        messages.error(request, 'غير مصرح لك بالوصول لصفحة التنبيهات')
        return redirect('inventory:dashboard')

    # تحديد فقط التنبيهات المطلوبة حسب الشروط
    required_alert_types = [
        'ITEM_ADDED', 'ITEM_MODIFIED', 'ITEM_DELETED',
        'RECEIPT_CREATED', 'ISSUE_CREATED', 
        'TRANSFER_REQUESTED', 'TRANSFER_APPROVED', 'TRANSFER_COMPLETED'
    ]
    
    # جلب التنبيهات حسب النوع وترتيبها بالأحدث أولاً
    alerts = Alert.objects.filter(alert_type__in=required_alert_types).order_by('-created_at')
    
    # فلترة حسب البحث والنوع
    search_query = request.GET.get('search', '')
    alert_type_filter = request.GET.get('alert_type', '')
    
    if search_query:
        alerts = alerts.filter(
            Q(title__icontains=search_query) | 
            Q(message__icontains=search_query) |
            Q(item__item_name_ar__icontains=search_query) |
            Q(item__item_code__icontains=search_query)
        )
    
    if alert_type_filter:
        alerts = alerts.filter(alert_type=alert_type_filter)
    
    # تقسيم التنبيهات إلى مقروءة وغير مقروءة
    unread_alerts = alerts.filter(is_read=False)
    read_alerts = alerts.filter(is_read=True)
    
    # تطبيق الترقيم على الصفحات - 5 تنبيهات لكل صفحة
    from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
    
    # إنشاء paginators منفصلة لكل من التنبيهات المقروءة وغير المقروءة
    unread_paginator = Paginator(unread_alerts, 5)  # 5 تنبيهات في الصفحة
    read_paginator = Paginator(read_alerts, 5)  # 5 تنبيهات في الصفحة
    
    # الحصول على رقم الصفحة من الطلب
    unread_page = request.GET.get('unread_page', 1)
    read_page = request.GET.get('read_page', 1)
    
    # الحصول على الصفحة المطلوبة من كل paginator
    try:
        unread_page_obj = unread_paginator.page(unread_page)
    except PageNotAnInteger:
        unread_page_obj = unread_paginator.page(1)
    except EmptyPage:
        unread_page_obj = unread_paginator.page(unread_paginator.num_pages)
    
    try:
        read_page_obj = read_paginator.page(read_page)
    except PageNotAnInteger:
        read_page_obj = read_paginator.page(1)
    except EmptyPage:
        read_page_obj = read_paginator.page(read_paginator.num_pages)
    
    # إنشاء قائمة خيارات أنواع التنبيهات للفلتر
    alert_type_choices = [
        ('ITEM_ADDED', 'إضافة صنف جديد'),
        ('ITEM_MODIFIED', 'تعديل صنف'),
        ('ITEM_DELETED', 'حذف صنف'),
        ('RECEIPT_CREATED', 'إذن استلام جديد'),
        ('ISSUE_CREATED', 'إذن صرف جديد'),
        ('TRANSFER_REQUESTED', 'طلب نقل مخزون'),
        ('TRANSFER_APPROVED', 'اعتماد نقل مخزون'),
        ('TRANSFER_COMPLETED', 'إكمال نقل مخزون'),
    ]
    
    context = {
        'page_title': 'التنبيهات',
        'unread_alerts': unread_page_obj,
        'read_alerts': read_page_obj,
        'total_alerts': alerts.count(),
        'unread_count': unread_alerts.count(),
        'search_query': search_query,
        'alert_type_filter': alert_type_filter,
        'alert_type_choices': alert_type_choices,
    }
    
    return render(request, 'inventory/alerts_list.html', context)


@login_required(login_url='inventory:login')
def mark_alert_read(request, pk):
    """تمييز التنبيه كمقروء"""
    # التحقق من أن المستخدم هو admin أو manager
    if request.user.username != 'admin' and request.user.username != 'manager':
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
        return redirect('inventory:dashboard')
    
    try:
        alert = Alert.objects.get(pk=pk)
        alert.mark_as_read(request.user)
        messages.success(request, 'تم تحديد التنبيه كمقروء')
    except Alert.DoesNotExist:
        messages.error(request, 'التنبيه غير موجود')
    
    return redirect('inventory:alerts_list')

    if user_profile and user_profile.role != 'MANAGER':
        stock_balances = StockBalance.objects.filter(
            warehouse=user_warehouse,
            current_quantity__gt=0
        ).select_related('item', 'warehouse', 'bin_location')
    else:
        stock_balances = StockBalance.objects.filter(
            current_quantity__gt=0
        ).select_related('item', 'warehouse', 'bin_location')

    # تطبيق فلاتر البحث
    search_query = request.GET.get('search', '')
    warehouse_filter = request.GET.get('warehouse', '')
    category_filter = request.GET.get('category', '')
    
    if search_query:
        stock_balances = stock_balances.filter(
            models.Q(item__item_name_ar__icontains=search_query) |
            models.Q(item__item_code__icontains=search_query)
        )
    
    if warehouse_filter:
        stock_balances = stock_balances.filter(warehouse__id=warehouse_filter)
    
    if category_filter:
        stock_balances = stock_balances.filter(item__category=category_filter)

    # تجميع البيانات
    summary_data = {
        'total_items': stock_balances.count(),
        'total_quantity': stock_balances.aggregate(
            total=models.Sum('current_quantity')
        )['total'] or 0,
        # Removed pricing references per requirements
    }

    context = {
        'user_warehouse': user_warehouse,
        'stock_balances': stock_balances.order_by('item__item_name_ar'),
        'summary_data': summary_data,
        'search_query': search_query,
        'warehouse_filter': warehouse_filter,
        'category_filter': category_filter,
        'warehouses': Warehouse.objects.filter(is_active=True),
        'categories': ItemMaster.PRODUCT_CATEGORIES,
    }
    
    return render(request, 'inventory/reports/current_stock_balances.html', context)

@login_required(login_url='inventory:login')
def stock_movements_report(request):
    """تقرير حركات المخزون (استلام/صرف/نقل)"""
    # التحقق من أن المستخدم هو admin أو أحمد محمد (manager)
    if request.user.username != 'admin' and request.user.username != 'manager':
        messages.error(request, 'غير مصرح لك بالوصول لصفحات التقارير - يسمح للمدير المسؤول (admin) وأحمد محمد فقط')
        return redirect('inventory:dashboard')
    
    user_warehouse = get_user_warehouse(request.user)
    if not user_warehouse:
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
        return redirect('inventory:dashboard')

    # فلترة حسب المخزن إذا لم يكن مدير عام
    user_profile = UserProfile.objects.filter(user=request.user).first()
    if user_profile and user_profile.role != 'MANAGER':
        movements = StockMovement.objects.filter(
            warehouse=user_warehouse
        ).select_related('item', 'warehouse', 'bin_location', 'created_by')
    else:
        movements = StockMovement.objects.all().select_related(
            'item', 'warehouse', 'bin_location', 'created_by'
        )

    # تطبيق فلاتر التاريخ والنوع
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    movement_type = request.GET.get('movement_type', '')
    warehouse_filter = request.GET.get('warehouse', '')
    
    if date_from:
        movements = movements.filter(movement_date__date__gte=date_from)
    
    if date_to:
        movements = movements.filter(movement_date__date__lte=date_to)
    
    if movement_type:
        movements = movements.filter(movement_type=movement_type)
    
    if warehouse_filter:
        movements = movements.filter(warehouse__id=warehouse_filter)

    # ترتيب حسب التاريخ (الأحدث أولاً)
    movements = movements.order_by('-movement_date')

    # إحصائيات الحركات
    summary_data = {
        'total_movements': movements.count(),
        'total_receipts': movements.filter(movement_type='RECEIPT').count(),
        'total_issues': movements.filter(movement_type='ISSUE').count(),
        'total_transfers_in': movements.filter(movement_type='TRANSFER_IN').count(),
        'total_transfers_out': movements.filter(movement_type='TRANSFER_OUT').count(),
    }

    context = {
        'user_warehouse': user_warehouse,
        'movements': movements,
        'summary_data': summary_data,
        'date_from': date_from,
        'date_to': date_to,
        'movement_type': movement_type,
        'warehouse_filter': warehouse_filter,
        'warehouses': Warehouse.objects.filter(is_active=True),
        'movement_types': StockMovement.MOVEMENT_TYPES,
    }
    
    return render(request, 'inventory/reports/stock_movements.html', context)

@login_required(login_url='inventory:login')
def warehouse_stock_report(request):
    """تقرير الأرصدة حسب المخزن"""
    # التحقق من أن المستخدم هو admin أو أحمد محمد (manager)
    if request.user.username != 'admin' and request.user.username != 'manager':
        messages.error(request, 'غير مصرح لك بالوصول لصفحات التقارير - يسمح للمدير المسؤول (admin) وأحمد محمد فقط')
        return redirect('inventory:dashboard')
    
    user_warehouse = get_user_warehouse(request.user)
    if not user_warehouse:
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
        return redirect('inventory:dashboard')

    # فلترة حسب صلاحيات المستخدم
    user_profile = UserProfile.objects.filter(user=request.user).first()
    if user_profile and user_profile.role != 'MANAGER':
        all_warehouses = Warehouse.objects.filter(id=user_warehouse.id, is_active=True)
    else:
        all_warehouses = Warehouse.objects.filter(is_active=True)
    
    # الحصول على المخزن المختار
    selected_warehouse_id = request.GET.get('warehouse_id', '')
    selected_warehouse = None
    warehouse_data = None
    free_percentage = 0
    
    if selected_warehouse_id:
        try:
            selected_warehouse = Warehouse.objects.get(id=selected_warehouse_id, is_active=True)
            
            # الحصول على أرصدة المخزن المحدد
            stock_balances = StockBalance.objects.filter(
                warehouse=selected_warehouse,
                current_quantity__gt=0
            ).select_related('item')
            
            # حساب إحصائيات المخزن
            warehouse_data = {
                'warehouse': selected_warehouse,
                'total_items': stock_balances.count(),
                'total_quantity': stock_balances.aggregate(
                    total=models.Sum('current_quantity')
                )['total'] or 0,
                'stock_balances': stock_balances.order_by('item__item_name_ar'),
                'occupancy_percentage': selected_warehouse.get_occupancy_percentage(),
            }
            
            # حساب نسبة الفراغ
            free_percentage = 100 - warehouse_data['occupancy_percentage']
        except Warehouse.DoesNotExist:
            selected_warehouse = None
    
    # الحصول على قائمة المخازن المتاحة للاختيار
    warehouses = []
    for warehouse in all_warehouses:
        stock_balances = StockBalance.objects.filter(
            warehouse=warehouse,
            current_quantity__gt=0
        ).select_related('item')
        
        warehouse_summary = {
            'warehouse': warehouse,
            'total_items': stock_balances.count(),
            'total_quantity': stock_balances.aggregate(
                total=models.Sum('current_quantity')
            )['total'] or 0,
            'occupancy_percentage': warehouse.get_occupancy_percentage(),
        }
        warehouses.append(warehouse_summary)

    context = {
        'user_warehouse': user_warehouse,
        'warehouse_data': warehouse_data,
        'selected_warehouse': selected_warehouse,
        'selected_warehouse_id': selected_warehouse_id,
        'all_warehouses': all_warehouses,
        'free_percentage': free_percentage,
    }
    
    return render(request, 'inventory/reports/warehouse_stock.html', context)

@login_required(login_url='inventory:login')
def bin_location_stock_report(request):
    """تقرير الأرصدة حسب موقع التخزين"""
    # التحقق من أن المستخدم هو admin أو أحمد محمد (manager)
    if request.user.username != 'admin' and request.user.username != 'manager':
        messages.error(request, 'غير مصرح لك بالوصول لصفحات التقارير - يسمح للمدير المسؤول (admin) وأحمد محمد فقط')
        return redirect('inventory:dashboard')
    
    user_warehouse = get_user_warehouse(request.user)
    if not user_warehouse:
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
        return redirect('inventory:dashboard')

    # فلترة حسب المخزن المختار أو مخزن المستخدم
    warehouse_filter = request.GET.get('warehouse', '')
    user_profile = UserProfile.objects.filter(user=request.user).first()
    
    if user_profile and user_profile.role != 'MANAGER':
        # مسؤول مخزن واحد
        selected_warehouse = user_warehouse
    elif warehouse_filter:
        # مدير عام اختار مخزن محدد
        selected_warehouse = get_object_or_404(Warehouse, id=warehouse_filter)
    else:
        # افتراضي: أول مخزن متاح
        selected_warehouse = Warehouse.objects.filter(is_active=True).first()

    bin_data = []
    if selected_warehouse:
        bin_locations = BinLocation.objects.filter(
            warehouse=selected_warehouse,
            is_active=True
        ).prefetch_related('stock_balances')
        
        for bin_location in bin_locations:
            stock_balances = bin_location.stock_balances.filter(
                current_quantity__gt=0
            ).select_related('item')
            
            bin_summary = {
                'bin_location': bin_location,
                'total_items': stock_balances.count(),
                'total_quantity': stock_balances.aggregate(
                    total=models.Sum('current_quantity')
                )['total'] or 0,
                # Removed pricing references per requirements
                'occupancy_percentage': bin_location.calculate_occupancy_percentage() if hasattr(bin_location, 'calculate_occupancy_percentage') else 0,
                'stock_balances': stock_balances.order_by('item__item_name_ar'),
            }
            bin_data.append(bin_summary)

    context = {
        'user_warehouse': user_warehouse,
        'selected_warehouse': selected_warehouse,
        'bin_data': bin_data,
        'warehouse_filter': warehouse_filter,
        'warehouses': Warehouse.objects.filter(is_active=True) if user_profile and user_profile.role == 'MANAGER' else [user_warehouse],
    }
    
    return render(request, 'inventory/reports/bin_location_stock.html', context)

# ==================================================================================
# Enhanced Reports Views - عروض التقارير المحسنة
# ==================================================================================

@login_required(login_url='inventory:login')
def enhanced_items_quantity_report(request):
    """تقرير الأصناف بكمياتها المحسن"""
    # التحقق من أن المستخدم هو admin أو أحمد محمد (manager)
    if request.user.username != 'admin' and request.user.username != 'manager':
        messages.error(request, 'غير مصرح لك بالوصول لصفحات التقارير - يسمح للمدير المسؤول (admin) وأحمد محمد فقط')
        return redirect('inventory:dashboard')
    
    user_warehouse = get_user_warehouse(request.user)
    if not user_warehouse:
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
        return redirect('inventory:dashboard')

    # فلترة حسب المخزن إذا لم يكن مدير عام
    user_profile = UserProfile.objects.filter(user=request.user).first()
    if user_profile and user_profile.role != 'MANAGER':
        stock_balances = StockBalance.objects.filter(
            warehouse=user_warehouse,
            current_quantity__gt=0
        ).select_related('item', 'warehouse', 'bin_location')
    else:
        stock_balances = StockBalance.objects.filter(
            current_quantity__gt=0
        ).select_related('item', 'warehouse', 'bin_location')

    # تطبيق فلاتر البحث
    search_query = request.GET.get('search', '')
    warehouse_filter = request.GET.get('warehouse', '')
    category_filter = request.GET.get('category', '')
    date_filter = request.GET.get('date_filter', 'all')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    
    if search_query:
        stock_balances = stock_balances.filter(
            models.Q(item__item_name_ar__icontains=search_query) |
            models.Q(item__item_code__icontains=search_query)
        )
    
    if warehouse_filter:
        stock_balances = stock_balances.filter(warehouse__id=warehouse_filter)
        selected_warehouse = Warehouse.objects.filter(id=warehouse_filter).first()
    else:
        selected_warehouse = None
    
    if category_filter:
        stock_balances = stock_balances.filter(item__category=category_filter)
    
    # إعداد بيانات المخازن
    warehouses = Warehouse.objects.filter(is_active=True) if user_profile and user_profile.role == 'MANAGER' else [user_warehouse]
    warehouses_data = []
    for warehouse in warehouses:
        if warehouse_filter and str(warehouse.id) != warehouse_filter:
            continue
            
        warehouse_data = {
            'id': warehouse.id,
            'warehouse_name': warehouse.warehouse_name,
            'location': warehouse.city,
        }
        
        warehouses_data.append(warehouse_data)
    
    # فلترة حسب التاريخ
    from django.utils import timezone
    today = timezone.now().date()
    
    if date_filter == 'today':
        stock_balances = stock_balances.filter(last_updated__date=today)
    elif date_filter == 'week':
        week_ago = today - timezone.timedelta(days=7)
        stock_balances = stock_balances.filter(last_updated__date__gte=week_ago)
    elif date_filter == 'month':
        month_ago = today - timezone.timedelta(days=30)
        stock_balances = stock_balances.filter(last_updated__date__gte=month_ago)

    # تجميع البيانات حسب الصنف
    items_summary = {}
    
    # إذا كان هناك بحث، أضف فقط الأصناف التي تطابق البحث
    if search_query:
        matching_items = ItemMaster.objects.filter(
            models.Q(item_name_ar__icontains=search_query) |
            models.Q(item_code__icontains=search_query),
            is_active=True
        )
        for item in matching_items:
            items_summary[item.item_code] = {
                'item': item,
                'total_quantity': 0,
                'warehouses': [],
                'locations_count': 0,
                'last_movement': None,
            }
    else:
        # إذا لم يكن هناك بحث، أضف جميع الأصناف النشطة
        for item in ItemMaster.objects.filter(is_active=True):
            items_summary[item.item_code] = {
                'item': item,
                'total_quantity': 0,
                'warehouses': [],
                'locations_count': 0,
                'last_movement': None,
            }
    
    # ثانياً: تحديث معلومات الأصناف التي لديها رصيد
    # ثانياً: تحديث معلومات الأصناف التي لديها رصيد
    for balance in stock_balances:
        item_key = balance.item.item_code
        
        # تحديث الكمية ومعلومات المخزن
        items_summary[item_key]['total_quantity'] += balance.current_quantity
        
        warehouse_info = {
            'warehouse': balance.warehouse,
            'bin_location': balance.bin_location,
            'quantity': balance.current_quantity,
            'batch_number': balance.batch_number,
            'expiry_date': balance.expiry_date,
        }
        items_summary[item_key]['warehouses'].append(warehouse_info)
        items_summary[item_key]['locations_count'] += 1
        
        if (not items_summary[item_key]['last_movement'] or 
            (balance.last_movement_date and balance.last_movement_date > items_summary[item_key]['last_movement'])):
            items_summary[item_key]['last_movement'] = balance.last_movement_date

    # إحصائيات عامة
    if search_query:
        # إذا كان هناك بحث، الإحصائيات تعتمد على الأصناف المطابقة فقط
        summary_data = {
            'total_items': len(items_summary),  # عدد الأصناف المطابقة للبحث
            'total_quantity': sum([item['total_quantity'] for item in items_summary.values()]),
            'total_locations': sum([item['locations_count'] for item in items_summary.values()]),
            'warehouses_count': stock_balances.values('warehouse').distinct().count() if stock_balances else 0,
        }
    else:
        # إذا لم يكن هناك بحث، الإحصائيات تشمل جميع الأصناف النشطة
        summary_data = {
            'total_items': ItemMaster.objects.filter(is_active=True).count(),  # إجمالي الأصناف النشطة
            'total_quantity': sum([item['total_quantity'] for item in items_summary.values()]),
            'total_locations': sum([item['locations_count'] for item in items_summary.values()]),
            'warehouses_count': stock_balances.values('warehouse').distinct().count() if stock_balances else 0,
        }

    context = {
        'user_warehouse': user_warehouse,
        'summary_data': summary_data,
        'date_from': date_from,
        'date_to': date_to,
        'warehouse_filter': warehouse_filter,
        'warehouses': warehouses_data,
        'warehouses_data': warehouses_data,
        'selected_warehouse': selected_warehouse,
        'items_summary': items_summary,
        'report_date': timezone.now(),
    }
    
    return render(request, 'inventory/reports/enhanced_items_quantity.html', context)

@login_required(login_url='inventory:login')
def consolidated_movements_report(request):
    """تقرير حركات المخزون المجمعة"""
    # التحقق من أن المستخدم هو admin أو أحمد محمد (manager)
    if request.user.username != 'admin' and request.user.username != 'manager':
        messages.error(request, 'غير مصرح لك بالوصول لصفحات التقارير - يسمح للمدير المسؤول (admin) وأحمد محمد فقط')
        return redirect('inventory:dashboard')
    
    user_warehouse = get_user_warehouse(request.user)
    if not user_warehouse:
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
        return redirect('inventory:dashboard')

    # فلترة حسب المخزن إذا لم يكن مدير عام
    user_profile = UserProfile.objects.filter(user=request.user).first()
    if user_profile and user_profile.role != 'MANAGER':
        movements = StockMovement.objects.filter(
            warehouse=user_warehouse
        ).select_related('item', 'warehouse', 'bin_location', 'created_by')
        warehouses = [user_warehouse]
    else:
        movements = StockMovement.objects.all().select_related(
            'item', 'warehouse', 'bin_location', 'created_by'
        )
        warehouses = Warehouse.objects.filter(is_active=True)

    # تطبيق فلاتر
    warehouse_filter = request.GET.get('warehouse', '')
    
    if warehouse_filter:
        movements = movements.filter(warehouse__id=warehouse_filter)
        selected_warehouse = warehouses.filter(id=warehouse_filter).first()
    else:
        selected_warehouse = None

    # ترتيب حسب التاريخ (الأحدث أولاً)
    movements = movements.order_by('-movement_date')

    # إعداد بيانات المخازن مع إحصائياتها
    warehouses_data = []
    for warehouse in warehouses:
        warehouse_movements = movements.filter(warehouse=warehouse)
        
        # إذا تم تحديد مخزن محدد في الفلتر، لا تضيف المخازن الأخرى
        if warehouse_filter and str(warehouse.id) != warehouse_filter:
            continue
            
        warehouse_data = {
            'id': warehouse.id,
            'warehouse_name': warehouse.warehouse_name,
            'location': warehouse.city,
            'total_items': warehouse_movements.values('item').distinct().count(),
            'total_movements': warehouse_movements.count(),
            'receipts': warehouse_movements.filter(movement_type='RECEIPT').count(),
            'issues': warehouse_movements.filter(movement_type='ISSUE').count(),
            'transfers_in': warehouse_movements.filter(movement_type='TRANSFER_IN').count(),
            'transfers_out': warehouse_movements.filter(movement_type='TRANSFER_OUT').count(),
        }
        
        warehouses_data.append(warehouse_data)

    # إحصائيات عامة
    total_transfers = movements.filter(movement_type__in=['TRANSFER_IN', 'TRANSFER_OUT']).count()
    
    summary_data = {
        'total_movements': movements.count(),
        'total_receipts': movements.filter(movement_type='RECEIPT').count(),
        'total_issues': movements.filter(movement_type='ISSUE').count(),
        'total_transfers': total_transfers,
        'warehouses_count': warehouses.count() if not warehouse_filter else 1,
        'items_count': movements.values('item').distinct().count(),
    }

    context = {
        'user_warehouse': user_warehouse,
        'summary_data': summary_data,
        'warehouse_filter': warehouse_filter,
        'warehouses': warehouses_data,
        'warehouses_data': warehouses_data,
        'selected_warehouse': selected_warehouse,
    }
    
    return render(request, 'inventory/reports/consolidated_movements.html', context)


@login_required(login_url='inventory:login')
def warehouse_movements_detail(request, warehouse_id):
    """تقرير تفصيلي لحركات المخزن الفردي مع بحث متقدم"""
    # التحقق من أن المستخدم هو admin أو أحمد محمد (manager)
    if request.user.username != 'admin' and request.user.username != 'manager':
        messages.error(request, 'غير مصرح لك بالوصول لصفحات التقارير - يسمح للمدير المسؤول (admin) وأحمد محمد فقط')
        return redirect('inventory:dashboard')
    
    user_warehouse = get_user_warehouse(request.user)
    if not user_warehouse:
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
        return redirect('inventory:dashboard')

    # التحقق من صلاحية الوصول للمخزن المطلوب
    try:
        warehouse = Warehouse.objects.get(id=warehouse_id, is_active=True)
    except Warehouse.DoesNotExist:
        messages.error(request, 'المخزن غير موجود')
        return redirect('inventory:consolidated_movements_report')
    
    # التحقق من صلاحيات المستخدم
    user_profile = UserProfile.objects.filter(user=request.user).first()
    if user_profile and user_profile.role != 'MANAGER' and user_profile.warehouse != warehouse:
        messages.error(request, 'ليس لديك صلاحية الوصول لهذا المخزن')
        return redirect('inventory:consolidated_movements_report')

    # الحصول على حركات المخزن المحدد
    movements = StockMovement.objects.filter(
        warehouse=warehouse
    ).select_related('item', 'warehouse', 'bin_location', 'created_by')

    # تطبيق فلاتر البحث المتقدم
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    movement_type = request.GET.get('movement_type', '')
    
    if date_from:
        movements = movements.filter(movement_date__date__gte=date_from)
    
    if date_to:
        movements = movements.filter(movement_date__date__lte=date_to)
    
    if movement_type:
        movements = movements.filter(movement_type=movement_type)

    # ترتيب حسب التاريخ (الأحدث أولاً)
    movements = movements.order_by('-movement_date')

    # إحصائيات الحركات
    summary_data = {
        'total_movements': movements.count(),
        'total_receipts': movements.filter(movement_type='RECEIPT').count(),
        'total_issues': movements.filter(movement_type='ISSUE').count(),
        'total_transfers': movements.filter(movement_type__in=['TRANSFER_IN', 'TRANSFER_OUT']).count(),
        'items_count': movements.values('item').distinct().count(),
    }

    # تقسيم الصفحات
    paginator = Paginator(movements, 25)  # 25 حركة في كل صفحة
    page = request.GET.get('page')
    
    try:
        movements_page = paginator.page(page)
    except PageNotAnInteger:
        # إذا كانت الصفحة ليست رقماً، عرض الصفحة الأولى
        movements_page = paginator.page(1)
    except EmptyPage:
        # إذا كانت الصفحة خارج النطاق، عرض آخر صفحة
        movements_page = paginator.page(paginator.num_pages)

    context = {
        'warehouse': warehouse,
        'movements': movements_page,
        'summary_data': summary_data,
        'date_from': date_from,
        'date_to': date_to,
        'movement_type': movement_type,
        'movement_types': StockMovement.MOVEMENT_TYPES,
        'is_paginated': True if paginator.num_pages > 1 else False,
        'page_obj': movements_page,
    }
    
    return render(request, 'inventory/reports/warehouse_movements_detail.html', context)

@login_required(login_url='inventory:login')
def warehouse_manager_report(request):
    """تقرير مدير المخزن الفردي"""
    # التحقق من أن المستخدم هو admin أو أحمد محمد (manager)
    if request.user.username != 'admin' and request.user.username != 'manager':
        messages.error(request, 'غير مصرح لك بالوصول لصفحات التقارير - يسمح للمدير المسؤول (admin) وأحمد محمد فقط')
        return redirect('inventory:dashboard')
    
    user_warehouse = get_user_warehouse(request.user)
    if not user_warehouse:
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
        return redirect('inventory:dashboard')
    
    # تحديد المخزن المطلوب عرض تقريره
    user_profile = UserProfile.objects.filter(user=request.user).first()
    selected_warehouse_id = request.GET.get('warehouse_id', '')
    
    if user_profile and user_profile.role == 'MANAGER' and selected_warehouse_id:
        # مدير عام يختار مخزن محدد
        try:
            selected_warehouse = Warehouse.objects.get(id=selected_warehouse_id, is_active=True)
            warehouse_manager = UserProfile.objects.filter(
                role='WAREHOUSE_MANAGER',
                assigned_warehouse=selected_warehouse
            ).first()
        except Warehouse.DoesNotExist:
            selected_warehouse = user_warehouse
            warehouse_manager = user_profile
    else:
        # مدير مخزن يرى تقريره الخاص
        selected_warehouse = user_warehouse
        warehouse_manager = user_profile

    # الحصول على حركات المخزن المحدد
    movements = StockMovement.objects.filter(
        warehouse=selected_warehouse
    ).select_related('item', 'warehouse', 'bin_location', 'created_by')

    # تطبيق فلاتر التاريخ
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    
    if date_from:
        movements = movements.filter(movement_date__date__gte=date_from)
    
    if date_to:
        movements = movements.filter(movement_date__date__lte=date_to)

    # ترتيب حسب التاريخ (الأحدث أولاً)
    movements = movements.order_by('-movement_date')

    # إحصائيات المدير
    from django.utils import timezone
    today = timezone.now().date()
    this_month = timezone.now().replace(day=1).date()
    
    manager_stats = {
        'total_movements': movements.count(),
        'movements_today': movements.filter(movement_date__date=today).count(),
        'movements_this_month': movements.filter(movement_date__date__gte=this_month).count(),
        'receipts_count': movements.filter(movement_type='RECEIPT').count(),
        'issues_count': movements.filter(movement_type='ISSUE').count(),
        'transfers_in_count': movements.filter(movement_type='TRANSFER_IN').count(),
        'transfers_out_count': movements.filter(movement_type='TRANSFER_OUT').count(),
        'items_managed': movements.values('item').distinct().count(),
    }

    # أصناف المخزن الحالية
    current_stock = StockBalance.objects.filter(
        warehouse=selected_warehouse,
        current_quantity__gt=0
    ).select_related('item', 'bin_location')
    
    stock_summary = {
        'total_items': current_stock.count(),
        'total_quantity': current_stock.aggregate(
            total=models.Sum('current_quantity')
        )['total'] or 0,
        'low_stock_items': current_stock.filter(
            current_quantity__lte=models.F('item__minimum_stock_level')
        ).count(),
        'locations_used': current_stock.values('bin_location').distinct().count(),
    }

    # أحدث الحركات
    recent_movements = movements[:20]

    context = {
        'user_warehouse': user_warehouse,
        'selected_warehouse': selected_warehouse,
        'warehouse_manager': warehouse_manager,
        'manager_stats': manager_stats,
        'stock_summary': stock_summary,
        'recent_movements': recent_movements,
        'current_stock': current_stock,
        'date_from': date_from,
        'date_to': date_to,
        'warehouses': Warehouse.objects.filter(is_active=True) if user_profile and user_profile.role == 'MANAGER' else [user_warehouse],
        'selected_warehouse_id': selected_warehouse_id,
    }
    
    return render(request, 'inventory/reports/warehouse_manager.html', context)

# ==================================================================================
# Reports Export Views - عروض تصدير التقارير
# ==================================================================================

@login_required(login_url='inventory:login')
def export_items_quantity_excel(request):
    """تصدير تقرير الأصناف بالكميات إلى Excel"""
    # التحقق من أن المستخدم هو admin أو أحمد محمد (manager)
    if request.user.username != 'admin' and request.user.username != 'manager':
        messages.error(request, 'غير مصرح لك بالوصول لصفحات التقارير - يسمح للمدير المسؤول (admin) وأحمد محمد فقط')
        return redirect('inventory:dashboard')
    
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    from django.http import HttpResponse
    from django.utils import timezone
    import io
    
    user_warehouse = get_user_warehouse(request.user)
    if not user_warehouse:
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
        return redirect('inventory:dashboard')
    
    # Get the same data as the main report
    user_profile = UserProfile.objects.filter(user=request.user).first()
    if user_profile and user_profile.role != 'MANAGER':
        stock_balances = StockBalance.objects.filter(
            warehouse=user_warehouse,
            current_quantity__gt=0
        ).select_related('item', 'warehouse', 'bin_location')
    else:
        stock_balances = StockBalance.objects.filter(
            current_quantity__gt=0
        ).select_related('item', 'warehouse', 'bin_location')
    
    # Apply filters if any
    search_query = request.GET.get('search', '')
    warehouse_filter = request.GET.get('warehouse', '')
    category_filter = request.GET.get('category', '')
    
    if search_query:
        stock_balances = stock_balances.filter(
            models.Q(item__item_name_ar__icontains=search_query) |
            models.Q(item__item_code__icontains=search_query)
        )
    
    if warehouse_filter:
        stock_balances = stock_balances.filter(warehouse__id=warehouse_filter)
    
    if category_filter:
        stock_balances = stock_balances.filter(item__category=category_filter)
    
    # Create workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "تقرير الأصناف بالكميات"
    
    # Styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="C89A3C", end_color="C89A3C", fill_type="solid")
    center_alignment = Alignment(horizontal="center", vertical="center")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Report header
    ws.merge_cells('A1:E1')
    ws['A1'] = "تقرير الأصناف بالكميات - شركة كاماڤيرس"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    
    ws.merge_cells('A2:E2')
    ws['A2'] = f"تاريخ التقرير: {timezone.now().date()}"
    ws['A2'].alignment = center_alignment
    
    # Headers
    headers = ["كود الصنف", "اسم الصنف", "الفئة", "الكمية الإجمالية", "وحدة القياس"]
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=4, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border
    
    # Data
    row = 5
    for balance in stock_balances:
        ws.cell(row=row, column=1, value=balance.item.item_code).border = border
        ws.cell(row=row, column=2, value=balance.item.item_name_ar).border = border
        ws.cell(row=row, column=3, value=balance.item.get_category_display()).border = border
        ws.cell(row=row, column=4, value=float(balance.current_quantity)).border = border
        ws.cell(row=row, column=5, value=balance.item.get_unit_of_measure_display()).border = border
        row += 1
    
    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # Save to response
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)
    
    response = HttpResponse(
        output.getvalue(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="تقرير_الأصناف_{timezone.now().date()}.xlsx"'
    
    return response

@login_required(login_url='inventory:login')
def export_movements_excel(request):
    """تصدير تقرير حركات المخزون إلى Excel"""
    # التحقق من أن المستخدم هو admin أو أحمد محمد (manager)
    if request.user.username != 'admin' and request.user.username != 'manager':
        messages.error(request, 'غير مصرح لك بالوصول لصفحات التقارير - يسمح للمدير المسؤول (admin) وأحمد محمد فقط')
        return redirect('inventory:dashboard')
    
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    from django.http import HttpResponse
    from django.utils import timezone
    import io
    
    user_warehouse = get_user_warehouse(request.user)
    if not user_warehouse:
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
        return redirect('inventory:dashboard')
    
    # Get movements data
    user_profile = UserProfile.objects.filter(user=request.user).first()
    if user_profile and user_profile.role != 'MANAGER':
        movements = StockMovement.objects.filter(
            warehouse=user_warehouse
        ).select_related('item', 'warehouse', 'bin_location', 'created_by')
    else:
        movements = StockMovement.objects.all().select_related(
            'item', 'warehouse', 'bin_location', 'created_by'
        )
    
    # Apply filters
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    movement_type = request.GET.get('movement_type', '')
    warehouse_filter = request.GET.get('warehouse', '')
    
    if date_from:
        movements = movements.filter(movement_date__date__gte=date_from)
    
    if date_to:
        movements = movements.filter(movement_date__date__lte=date_to)
    
    if movement_type:
        movements = movements.filter(movement_type=movement_type)
    
    if warehouse_filter:
        movements = movements.filter(warehouse__id=warehouse_filter)
    
    movements = movements.order_by('-movement_date')[:1000]  # Limit to 1000 records
    
    # Create workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "تقرير حركات المخزون"
    
    # Styles
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="D62828", end_color="D62828", fill_type="solid")
    center_alignment = Alignment(horizontal="center", vertical="center")
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Report header
    ws.merge_cells('A1:G1')
    ws['A1'] = "تقرير حركات المخزون - شركة كاماڤيرس"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = center_alignment
    
    ws.merge_cells('A2:G2')
    ws['A2'] = f"تاريخ التقرير: {timezone.now().date()}"
    ws['A2'].alignment = center_alignment
    
    # Headers
    headers = ["رقم الحركة", "نوع الحركة", "الصنف", "المخزن", "الكمية", "وحدة القياس", "التاريخ"]
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=4, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = border
    
    # Data
    row = 5
    for movement in movements:
        ws.cell(row=row, column=1, value=movement.movement_number).border = border
        ws.cell(row=row, column=2, value=movement.get_movement_type_display()).border = border
        ws.cell(row=row, column=3, value=movement.item.item_name_ar).border = border
        ws.cell(row=row, column=4, value=movement.warehouse.warehouse_name).border = border
        ws.cell(row=row, column=5, value=float(movement.quantity)).border = border
        ws.cell(row=row, column=6, value=movement.unit_of_measure).border = border
        ws.cell(row=row, column=7, value=movement.movement_date.strftime('%Y-%m-%d %H:%M')).border = border
        row += 1
    
    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # Save to response
    output = io.BytesIO()
    wb.save(output)
    output.seek(0)
    
    response = HttpResponse(
        output.getvalue(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename="تقرير_الحركات_{timezone.now().date()}.xlsx"'
    
    return response

@login_required(login_url='inventory:login')
def export_manager_report_pdf(request):
    """تصدير تقرير مدير المخزن إلى PDF"""
    # التحقق من أن المستخدم هو admin أو أحمد محمد (manager)
    if request.user.username != 'admin' and request.user.username != 'manager':
        messages.error(request, 'غير مصرح لك بالوصول لصفحات التقارير - يسمح للمدير المسؤول (admin) وأحمد محمد فقط')
        return redirect('inventory:dashboard')
    
    from django.http import HttpResponse
    from django.template.loader import get_template
    from django.utils import timezone
    import io
    
    # For now, we'll use simple HTML to PDF conversion
    # In a production environment, you'd want to use libraries like reportlab or weasyprint
    
    user_warehouse = get_user_warehouse(request.user)
    if not user_warehouse:
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة')
        return redirect('inventory:dashboard')
    
    # Get the same data as warehouse_manager_report view
    user_profile = UserProfile.objects.filter(user=request.user).first()
    selected_warehouse_id = request.GET.get('warehouse_id', '')
    
    if user_profile and user_profile.role == 'MANAGER' and selected_warehouse_id:
        try:
            selected_warehouse = Warehouse.objects.get(id=selected_warehouse_id, is_active=True)
            warehouse_manager = UserProfile.objects.filter(
                role='WAREHOUSE_MANAGER',
                assigned_warehouse=selected_warehouse
            ).first()
        except Warehouse.DoesNotExist:
            selected_warehouse = user_warehouse
            warehouse_manager = user_profile
    else:
        selected_warehouse = user_warehouse
        warehouse_manager = user_profile
    
    # For now, return a simple message indicating PDF export capability
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="تقرير_مدير_المخزن_{timezone.now().date()}.pdf"'
    
    # Simple PDF content placeholder
    pdf_content = f"""
    %PDF-1.4
    1 0 obj
    <<
    /Type /Catalog
    /Pages 2 0 R
    >>
    endobj
    2 0 obj
    <<
    /Type /Pages
    /Kids [3 0 R]
    /Count 1
    >>
    endobj
    3 0 obj
    <<
    /Type /Page
    /Parent 2 0 R
    /MediaBox [0 0 612 792]
    /Contents 4 0 R
    >>
    endobj
    4 0 obj
    <<
    /Length 44
    >>
    stream
    BT
    /F1 12 Tf
    72 720 Td
    (تقرير مدير المخزن - {selected_warehouse.warehouse_name}) Tj
    ET
    endstream
    endobj
    xref
    0 5
    0000000000 65535 f 
    0000000009 00000 n 
    0000000058 00000 n 
    0000000115 00000 n 
    0000000207 00000 n 
    trailer
    <<
    /Size 5
    /Root 1 0 R
    >>
    startxref
    301
    %%EOF
    """
    
    response.write(pdf_content.encode('utf-8', errors='ignore'))
    return response
