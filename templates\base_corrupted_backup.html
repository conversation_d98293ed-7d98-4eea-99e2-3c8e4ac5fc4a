﻿<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}KamaVerse - Ù†Ø¸Ø§Ù… Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ù…Ø®Ø²ÙˆÙ†{% endblock %}</title>
    
    <!-- Google Fonts - Cairo for Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
   
    {% load static %}
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/kamaverse.css' %}">
    <link rel="stylesheet" href="{% static 'css/dashboard-new.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="dashboard-wrapper">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="{% static 'images/kamaverse-logo.png' %}" alt="KamaVerse Logo" class="logo-img">
                    <span>KamaVerse</span>
                </div>
            </div>

            <div class="user-profile">
                <div class="user-avatar">
                    <img src="{% static 'images/user-avatar.jpg' %}" alt="User" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjOUM5Qzk5Ii8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzLjk5IDcuMDEgMTUuNjIgNiAxOEMxMC4wMSAyMCAxMy45OSAyMCAxOCAxOEMxNi45OSAxNS42MiAxNC42NyAxMy45OSAxMiAxNFoiIGZpbGw9IiM5QzlDOTkiLz4KPC9zdmc+Cjwvc3ZnPgo='">
                </div>
                <div class="user-info">
                    <h4>
                        {% if user.is_authenticated %}
                            {{ user.get_full_name|default:user.username }}
                        {% else %}
                            Ø²Ø§Ø¦Ø±
                        {% endif %}
                    </h4>
                    <p>
                        {% if user.is_authenticated %}
                            {% if user.is_superuser %}
                                Ù…Ø¯ÙŠØ± Ø§Ù„Ù†Ø¸Ø§Ù…
                            {% else %}
                                Ù…Ø¯ÙŠØ± Ø§Ù„Ù…Ø®Ø²ÙˆÙ†
                            {% endif %}
                        {% else %}
                            ØºÙŠØ± Ù…Ø³Ø¬Ù„
                        {% endif %}
                    </p>
                </div>
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li class="{% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                        <a href="{% url 'inventory:dashboard' %}"><i class="fas fa-home"></i> Ø§Ù„ØµÙØ­Ø© Ø§Ù„Ø±Ø¦ÙŠØ³ÙŠØ©</a>
                    </li>
                    <li class="{% if request.resolver_match.url_name == 'items_list' %}active{% endif %}">
                        <a href="{% url 'inventory:items_list' %}"><i class="fas fa-boxes"></i> Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ø£ØµÙ†Ø§Ù</a>
                    </li>
                    <li class="{% if request.resolver_match.url_name == 'warehouses_list' or request.resolver_match.url_name == 'warehouse_detail' or request.resolver_match.url_name == 'bin_locations_list' %}active{% endif %}">
                        <a href="{% url 'inventory:warehouses_list' %}"><i class="fas fa-warehouse"></i> Ø¥Ø¯Ø§Ø±Ø© Ø§Ù„Ù…Ø®Ø§Ø²Ù†</a>
                    </li>
                    <li class="{% if request.resolver_match.url_name == 'movements_list' %}active{% endif %}">
                        <a href="{% url 'inventory:movements_list' %}"><i class="fas fa-exchange-alt"></i> Ø­Ø±ÙƒØ§Øª Ø§Ù„Ù…Ø®Ø²ÙˆÙ†</a>
                    </li>

                    <li class="{% if request.resolver_match.url_name == 'reports_dashboard' %}active{% endif %}">
                        <a href="{% url 'inventory:reports_dashboard' %}"><i class="fas fa-chart-bar"></i> Ø§Ù„ØªÙ‚Ø§Ø±ÙŠØ±</a>
                    </li>
                    <li>
                        <a href="#"><i class="fas fa-bell"></i> Ø§Ù„ØªÙ†Ø¨ÙŠÙ‡Ø§Øª</a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                {% if user.is_authenticated %}
                    <a href="{% url 'inventory:logout' %}" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i>
                        ØªØ³Ø¬ÙŠÙ„ Ø§Ù„Ø®Ø±ÙˆØ¬
                    </a>
                {% else %}
                    <a href="{% url 'inventory:login' %}" class="logout-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        ØªØ³Ø¬ÙŠÙ„ Ø§Ù„Ø¯Ø®ÙˆÙ„
                    </a>
                {% endif %}
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
        <div class="container">
            {% block breadcrumb %}
            <nav aria-label="breadcrumb" class="mt-3 mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'inventory:dashboard' %}">Ø§Ù„Ø±Ø¦ÙŠØ³ÙŠØ©</a></li>
                    {% block breadcrumb_items %}{% endblock %}
                </ol>
            </nav>
            {% endblock %}
            
            <!-- Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
            
            <!-- Page Content -->
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Scripts -->
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Notification click handler
        document.querySelector('.notifications').addEventListener('click', function() {
            // TODO: Show notifications dropdown
            console.log('Notifications clicked');
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
