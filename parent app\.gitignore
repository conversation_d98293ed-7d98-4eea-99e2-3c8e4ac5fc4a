# KamaVerse .gitignore
# نظام إدارة متكامل لشركة القماش

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Django specific
media/
staticfiles/
static_collected/

# Local development
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# KamaVerse specific
logs/*.log
backups/
temp/
uploads/
downloads/

# Environment variables
.env.local
.env.development
.env.production
.env.testing

# Database backups
*.sql
*.dump

# SSL certificates
*.pem
*.key
*.crt

# Configuration files with sensitive data
config/secrets.py
config/local.py

# Node.js (if using for frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Redis dump
dump.rdb

# Celery beat schedule
celerybeat-schedule.db

# Webpack bundles
webpack-stats.json

# Coverage reports
htmlcov/
.coverage

# Pytest
.pytest_cache/

# Documentation builds
docs/_build/

# Temporary files
*.tmp
*.temp

# Editor backups
*~
*.bak
*.orig

# Windows specific
*.exe
*.msi
*.msm
*.msp

# macOS specific
.AppleDouble
.LSOverride

# Linux specific
*~

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Visual Studio Code
.vscode/
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Local development overrides
local_settings.py
development_settings.py

# Production secrets
production_secrets.py
.env.production

# Backup files
*.backup
*.bak

# Log files
*.log
logs/

# Cache directories
.cache/
__pycache__/

# Temporary directories
tmp/
temp/

# User uploads
media/uploads/
media/documents/
media/images/

# Static files collection
staticfiles/
static_collected/

# Webpack
webpack-stats.json
bundles/

# Sass
.sass-cache/

# Compass
.compass/

# Node
node_modules/
npm-debug.log

# Bower
bower_components/

# Grunt
.grunt/

# Gulp
.gulp/

# Yarn
yarn-error.log

# Package lock files (keep requirements.txt instead)
package-lock.json
yarn.lock

# Python virtual environments
venv/
env/
.venv/
.env/

# Conda environments
.conda/

# Jupyter
.jupyter/
.ipynb_checkpoints/

# PyCharm
.idea/

# Spyder
.spyderproject/
.spyproject/

# Rope
.ropeproject/

# Django migrations (uncomment if you want to ignore migrations)
# */migrations/*.py
# !*/migrations/__init__.py

# Local database
*.sqlite3
*.db

# Redis
dump.rdb

# Elasticsearch
elasticsearch/

# Memcached
memcached/

# RabbitMQ
rabbitmq/

# Docker
.dockerignore
docker-compose.override.yml

# Kubernetes
*.yaml.local
*.yml.local

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Ansible
*.retry

# Vagrant
.vagrant/

# Local configuration
local.py
local_settings.py
.env.local

# Security
*.pem
*.key
*.crt
*.p12
*.pfx

# Backup
*.backup
*.bak
*.old

# Archive
*.zip
*.tar.gz
*.rar
*.7z

# Temporary
*.tmp
*.temp
~*

# System
.DS_Store
Thumbs.db
desktop.ini
