{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/kamaverse.css' %}">
<style>
    .transfer-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }
    
    .transfer-card:hover {
        box-shadow: 0 4px 12px rgba(214, 40, 40, 0.1);
        border-color: #D62828;
    }
    
    .status-badge {
        font-size: 0.85rem;
        padding: 4px 12px;
        border-radius: 20px;
        font-weight: 500;
    }
    
    .status-pending { background-color: #fff3cd; color: #856404; }
    .status-approved { background-color: #d1ecf1; color: #0c5460; }
    .status-completed { background-color: #d4edda; color: #155724; }
    .status-rejected { background-color: #f8d7da; color: #721c24; }
    .status-cancelled { background-color: #e2e3e5; color: #383d41; }
    
    .warehouse-arrow {
        color: #C89A3C;
        font-size: 1.2rem;
        margin: 0 10px;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #D62828 0%, #B91C1C 100%);
        color: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .stat-item {
        text-align: center;
        padding: 15px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #C89A3C;
    }
    
    .filter-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="text-primary mb-1">
                <i class="fas fa-exchange-alt me-2"></i>{{ page_title }}
            </h2>
            <p class="text-muted mb-0">إدارة عمليات نقل المخزون بين المخازن</p>
        </div>
        <a href="{% url 'inventory:stock_transfer_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>إنشاء طلب نقل جديد
        </a>
    </div>

    <!-- Statistics -->
    <div class="stats-card">
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">{{ total_transfers }}</div>
                <div>إجمالي العمليات</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ pending_transfers }}</div>
                <div>في الانتظار</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ approved_transfers }}</div>
                <div>معتمدة</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ completed_transfers }}</div>
                <div>مكتملة</div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">فلترة حسب الحالة</label>
                <select name="status" class="form-select">
                    <option value="">جميع الحالات</option>
                    <option value="PENDING" {% if status_filter == 'PENDING' %}selected{% endif %}>في الانتظار</option>
                    <option value="APPROVED" {% if status_filter == 'APPROVED' %}selected{% endif %}>معتمد</option>
                    <option value="COMPLETED" {% if status_filter == 'COMPLETED' %}selected{% endif %}>مكتمل</option>
                    <option value="REJECTED" {% if status_filter == 'REJECTED' %}selected{% endif %}>مرفوض</option>
                    <option value="CANCELLED" {% if status_filter == 'CANCELLED' %}selected{% endif %}>ملغي</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">فلترة حسب المخزن</label>
                <select name="warehouse" class="form-select">
                    <option value="">جميع المخازن</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.id }}" {% if warehouse_filter == warehouse.id|stringformat:"s" %}selected{% endif %}>
                        {{ warehouse.warehouse_name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary me-2">
                    <i class="fas fa-filter me-1"></i>تطبيق الفلتر
                </button>
                <a href="{% url 'inventory:stock_transfer_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>إلغاء
                </a>
            </div>
        </form>
    </div>

    <!-- Transfer List -->
    {% if transfers %}
        {% for transfer in transfers %}
        <div class="transfer-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <h6 class="mb-1 text-primary">{{ transfer.transfer_number }}</h6>
                        <small class="text-muted">{{ transfer.transfer_date|date:"Y/m/d H:i" }}</small>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <span class="fw-bold">{{ transfer.source_warehouse.warehouse_name }}</span>
                            <i class="fas fa-arrow-left warehouse-arrow"></i>
                            <span class="fw-bold">
                                {% if transfer.destination_warehouse %}
                                    {{ transfer.destination_warehouse.warehouse_name }}
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </span>
                        </div>
                        <small class="text-muted">{{ transfer.get_total_items_count }} صنف</small>
                    </div>

                    <div class="col-md-2 text-center">
                        <div class="fw-bold text-primary">{{ transfer.get_total_items_count }}</div>
                        <small class="text-muted">عدد الأصناف</small>
                    </div>
                    
                    <div class="col-md-2 text-center">
                        <span class="status-badge status-{{ transfer.status|lower }}">
                            {{ transfer.get_status_display }}
                        </span>
                    </div>
                    
                    <div class="col-md-1 text-end">
                        <a href="{% url 'inventory:stock_transfer_detail' transfer.id %}" 
                           class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i>
                        </a>
                    </div>
                </div>
                
                {% if transfer.reason %}
                <div class="row mt-2">
                    <div class="col-12">
                        <small class="text-muted">
                            <i class="fas fa-comment me-1"></i>{{ transfer.reason }}
                        </small>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}

        <!-- Pagination -->
        {% if transfers.has_other_pages %}
        <nav aria-label="تنقل الصفحات">
            <ul class="pagination justify-content-center">
                {% if transfers.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ transfers.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if warehouse_filter %}&warehouse={{ warehouse_filter }}{% endif %}">السابق</a>
                    </li>
                {% endif %}
                
                {% for num in transfers.paginator.page_range %}
                    {% if transfers.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% else %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if warehouse_filter %}&warehouse={{ warehouse_filter }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if transfers.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ transfers.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if warehouse_filter %}&warehouse={{ warehouse_filter }}{% endif %}">التالي</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد عمليات نقل مخزون حتى الآن</h5>
            <p class="text-muted">ابدأ بإنشاء طلب نقل جديد</p>
            <a href="{% url 'inventory:stock_transfer_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إنشاء طلب نقل جديد
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
