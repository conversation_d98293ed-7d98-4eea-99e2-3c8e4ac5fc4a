#!/usr/bin/env python
"""
Simple script to fix the models.py file directly
"""

with open('e:/New folder (10)/hive/test26-pt2/kamach_test01/inventory/models.py', 'r', encoding='utf-8') as file:
    lines = file.readlines()

# Skip line 2579-2581 which contain the problematic indentation
fixed_lines = []
skip_lines = [2578, 2579, 2580, 2581]

for i, line in enumerate(lines):
    if i not in skip_lines:
        fixed_lines.append(line)

# Write the file back
with open('e:/New folder (10)/hive/test26-pt2/kamach_test01/inventory/models.py', 'w', encoding='utf-8') as file:
    file.writelines(fixed_lines)

print("✅ Fixed indentation issue in models.py")