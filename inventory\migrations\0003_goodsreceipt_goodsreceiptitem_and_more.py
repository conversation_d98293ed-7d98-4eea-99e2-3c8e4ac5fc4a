# Generated by Django 5.2.5 on 2025-08-21 09:54

import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0002_userprofile'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='GoodsReceipt',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('receipt_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الإذن')),
                ('receipt_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإذن')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('CONFIRMED', 'مؤكد'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي')], default='DRAFT', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('total_items', models.PositiveIntegerField(default=0, verbose_name='عدد الأصناف')),
                ('total_quantity', models.DecimalField(decimal_places=3, default=Decimal('0.000'), max_digits=15, verbose_name='إجمالي الكمية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_receipts', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='goods_receipts', to='inventory.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'إذن استلام',
                'verbose_name_plural': 'إيصالات الاستلام',
                'ordering': ['-receipt_date'],
            },
        ),
        migrations.CreateModel(
            name='GoodsReceiptItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=15, verbose_name='الكمية')),
                ('batch_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الدفعة')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='تاريخ انتهاء الصلاحية')),
                ('unit_cost', models.DecimalField(blank=True, decimal_places=3, max_digits=15, null=True, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(blank=True, decimal_places=3, max_digits=15, null=True, verbose_name='إجمالي التكلفة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('bin_location', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='receipt_items', to='inventory.binlocation', verbose_name='موقع التخزين')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='receipt_items', to='inventory.itemmaster', verbose_name='الصنف')),
                ('receipt', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='receipt_items', to='inventory.goodsreceipt', verbose_name='إذن الاستلام')),
            ],
            options={
                'verbose_name': 'صنف إذن الاستلام',
                'verbose_name_plural': 'أصناف إيصالات الاستلام',
                'ordering': ['item__item_name_ar'],
            },
        ),
        migrations.AddIndex(
            model_name='goodsreceipt',
            index=models.Index(fields=['receipt_number'], name='inventory_g_receipt_88576c_idx'),
        ),
        migrations.AddIndex(
            model_name='goodsreceipt',
            index=models.Index(fields=['warehouse'], name='inventory_g_warehou_9acf95_idx'),
        ),
        migrations.AddIndex(
            model_name='goodsreceipt',
            index=models.Index(fields=['status'], name='inventory_g_status_6b2531_idx'),
        ),
        migrations.AddIndex(
            model_name='goodsreceipt',
            index=models.Index(fields=['receipt_date'], name='inventory_g_receipt_b44bfe_idx'),
        ),
        migrations.AddIndex(
            model_name='goodsreceiptitem',
            index=models.Index(fields=['receipt'], name='inventory_g_receipt_e4e31c_idx'),
        ),
        migrations.AddIndex(
            model_name='goodsreceiptitem',
            index=models.Index(fields=['item'], name='inventory_g_item_id_d16f55_idx'),
        ),
        migrations.AddIndex(
            model_name='goodsreceiptitem',
            index=models.Index(fields=['bin_location'], name='inventory_g_bin_loc_0d776e_idx'),
        ),
        migrations.AddIndex(
            model_name='goodsreceiptitem',
            index=models.Index(fields=['batch_number'], name='inventory_g_batch_n_2a6aca_idx'),
        ),
    ]
