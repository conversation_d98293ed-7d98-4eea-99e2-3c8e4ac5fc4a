from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """
    فلتر لاسترجاع قيمة من قاموس باستخدام المفتاح
    يستخدم في قوالب الأصناف والمخازن
    """
    if dictionary is None:
        return None
    
    if hasattr(dictionary, 'get'):
        return dictionary.get(key)
    
    try:
        return dictionary[key]
    except (KeyError, IndexError, TypeError):
        return None

@register.filter
def add(value, arg):
    """
    إضافة رقم إلى آخر
    """
    try:
        return int(value) + int(arg)
    except (ValueError, TypeError):
        return value