"""
KeyAuditLog - سجل العمليات
تسجيل جميع العمليات المهمة في النظام
"""

from django.db import models
import uuid
import json


class KeyAuditLog(models.Model):
    """
    نموذج سجل العمليات
    """
    
    OPERATION_TYPES = [
        ('CREATE', 'إنشاء'),
        ('UPDATE', 'تحديث'),
        ('DELETE', 'حذف'),
        ('LOGIN', 'تسجيل دخول'),
        ('LOGOUT', 'تسجيل خروج'),
        ('APPROVE', 'موافقة'),
        ('REJECT', 'رفض'),
        ('EXPORT', 'تصدير'),
        ('IMPORT', 'استيراد'),
        ('VIEW', 'عرض'),
        ('PRINT', 'طباعة'),
        ('OTHER', 'أخرى'),
    ]
    
    MODULES = [
        ('CORE', 'النواة الأساسية'),
        ('IMPORT_MODULE', 'موديول الاستيراد'),
        ('STOCK_MODULE', 'موديول المخزون'),
        ('FINANCE_MODULE', 'موديول المالية'),
        ('SALES_MODULE', 'موديول المبيعات'),
        ('CRM_MODULE', 'موديول علاقات العملاء'),
        ('HR_MODULE', 'موديول الموارد البشرية'),
        ('LOGISTICS_MODULE', 'موديول اللوجستيات'),
        ('REPORTING_MODULE', 'موديول التقارير'),
        ('USERS_MODULE', 'موديول إدارة المستخدمين'),
        ('KAMACHAT', 'تطبيق المحادثة'),
        ('HAWK', 'تطبيق الإدارة العليا'),
        ('SYSTEM', 'النظام'),
    ]
    
    # Primary key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Operation Information
    operation_type = models.CharField(
        max_length=20,
        choices=OPERATION_TYPES,
        verbose_name='نوع العملية'
    )
    
    module = models.CharField(
        max_length=30,
        choices=MODULES,
        verbose_name='الموديول'
    )
    
    table_name = models.CharField(
        max_length=100,
        verbose_name='اسم الجدول',
        blank=True,
        null=True
    )
    
    record_id = models.CharField(
        max_length=100,
        verbose_name='معرف السجل',
        blank=True,
        null=True
    )
    
    # User Information
    user = models.ForeignKey(
        'core.KeyUser',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='audit_logs',
        verbose_name='المستخدم'
    )
    
    ip_address = models.GenericIPAddressField(
        verbose_name='عنوان IP'
    )
    
    user_agent = models.TextField(
        verbose_name='معلومات المتصفح',
        blank=True,
        null=True
    )
    
    # Operation Details
    operation_description = models.TextField(
        verbose_name='وصف العملية'
    )
    
    old_data = models.JSONField(
        verbose_name='البيانات القديمة',
        blank=True,
        null=True,
        default=dict
    )
    
    new_data = models.JSONField(
        verbose_name='البيانات الجديدة',
        blank=True,
        null=True,
        default=dict
    )
    
    # Additional Information
    success = models.BooleanField(
        default=True,
        verbose_name='نجحت العملية'
    )
    
    error_message = models.TextField(
        verbose_name='رسالة الخطأ',
        blank=True,
        null=True
    )
    
    # Timestamp
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ العملية'
    )
    
    class Meta:
        verbose_name = 'سجل عملية'
        verbose_name_plural = 'سجل العمليات'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['operation_type']),
            models.Index(fields=['module']),
            models.Index(fields=['table_name']),
            models.Index(fields=['created_at']),
            models.Index(fields=['ip_address']),
        ]
    
    def __str__(self):
        user_name = self.user.arabic_name if self.user else 'مجهول'
        return f"{user_name} - {self.get_operation_type_display()} - {self.created_at}"
    
    @classmethod
    def log_operation(cls, user, operation_type, module, description, 
                     table_name=None, record_id=None, old_data=None, 
                     new_data=None, ip_address=None, user_agent=None, 
                     success=True, error_message=None):
        """
        تسجيل عملية في سجل العمليات
        """
        return cls.objects.create(
            user=user,
            operation_type=operation_type,
            module=module,
            table_name=table_name,
            record_id=str(record_id) if record_id else None,
            operation_description=description,
            old_data=old_data or {},
            new_data=new_data or {},
            ip_address=ip_address or '127.0.0.1',
            user_agent=user_agent or '',
            success=success,
            error_message=error_message
        )
