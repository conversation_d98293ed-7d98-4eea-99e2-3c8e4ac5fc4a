from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.contrib.auth.models import User
from django.http import JsonResponse, HttpResponse
from .models import UserProfile, UserPermission, Warehouse

@login_required(login_url='inventory:login')
def users_list(request):
    """عرض قائمة المستخدمين والمخازن المخصصة لهم"""
    # التحقق من صلاحيات المستخدم - فقط المدير المسؤول (admin) يمكنه الوصول لهذه الصفحة
    if request.user.username != 'admin':
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة. فقط مدير المستخدمين يمكنه الوصول.')
        return redirect('inventory:dashboard')
    
    # جلب كافة المستخدمين مع الملفات الشخصية والمخازن المخصصة لهم
    profiles = UserProfile.objects.all().select_related('user', 'assigned_warehouse')
    
    # تنظيم البيانات حسب دور المستخدم
    managers = []
    warehouse_managers = []
    employees = []
    
    for profile in profiles:
        user_data = {
            'id': profile.id,
            'username': profile.user.username,
            'full_name': profile.user.get_full_name() or profile.user.username,
            'role': profile.get_role_display(),
            'role_code': profile.role,
            'warehouse': profile.assigned_warehouse.warehouse_name if profile.assigned_warehouse else 'جميع المخازن',
            'warehouse_id': profile.assigned_warehouse.id if profile.assigned_warehouse else None,
            'is_active': profile.is_active,
            'department': profile.department or '-',
            'phone': profile.phone_number or '-',
        }
        
        if profile.role == 'MANAGER':
            managers.append(user_data)
        elif profile.role == 'WAREHOUSE_MANAGER':
            warehouse_managers.append(user_data)
        else:
            employees.append(user_data)
    
    context = {
        'page_title': 'إدارة المستخدمين',
        'managers': managers,
        'warehouse_managers': warehouse_managers,
        'employees': employees,
        'total_users': len(profiles),
    }
    
    return render(request, 'inventory/users_list.html', context)


@login_required(login_url='inventory:login')
def user_detail(request, user_id):
    """عرض تفاصيل المستخدم وصلاحياته"""
    # التحقق من صلاحيات المدير
    if request.user.username != 'admin':
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة. فقط مدير المستخدمين يمكنه الوصول.')
        return redirect('inventory:dashboard')
    
    # جلب بيانات المستخدم
    user_profile = get_object_or_404(UserProfile, id=user_id)
    
    # الحصول على الصلاحيات الحالية
    current_permissions = UserPermission.objects.filter(user_profile=user_profile)
    
    # الحصول على جميع المخازن للاختيار
    warehouses = Warehouse.objects.filter(is_active=True)
    
    # إعداد قائمة الصلاحيات المتاحة
    available_permissions = UserPermission.PERMISSION_TYPES
    
    # قائمة الصلاحيات الممنوحة
    granted_permissions = {perm.permission_type: perm.is_granted for perm in current_permissions}
    
    context = {
        'page_title': 'إدارة صلاحيات المستخدم',
        'user_profile': user_profile,
        'available_permissions': available_permissions,
        'granted_permissions': granted_permissions,
        'warehouses': warehouses,
        'role_choices': UserProfile.USER_ROLES,
    }
    
    return render(request, 'inventory/user_detail.html', context)


@login_required(login_url='inventory:login')
def update_user_permissions(request, user_id):
    """تحديث صلاحيات المستخدم"""
    # التحقق من صلاحيات المدير
    if request.user.username != 'admin':
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة. فقط مدير المستخدمين يمكنه الوصول.')
        return redirect('inventory:dashboard')
    
    # جلب بيانات المستخدم
    user_profile = get_object_or_404(UserProfile, id=user_id)
    
    if request.method == 'POST':
        # تحديث دور المستخدم
        new_role = request.POST.get('role')
        if new_role and new_role in dict(UserProfile.USER_ROLES):
            user_profile.role = new_role
            # إذا كان الدور مسؤول مخزن
            if new_role == 'WAREHOUSE_MANAGER':
                warehouse_id = request.POST.get('warehouse')
                if warehouse_id:
                    try:
                        warehouse = Warehouse.objects.get(id=warehouse_id)
                        user_profile.assigned_warehouse = warehouse
                    except Warehouse.DoesNotExist:
                        messages.error(request, 'المخزن المحدد غير موجود')
            else:
                # إذا لم يكن مسؤول مخزن، ففي حالة مدير يكون مسؤول عن جميع المخازن
                user_profile.assigned_warehouse = None
            
            # تحديث حالة النشاط
            is_active = request.POST.get('is_active') == 'on'
            user_profile.is_active = is_active
            user_profile.save()
        
        # تحديث الصلاحيات
        for permission_type, _ in UserPermission.PERMISSION_TYPES:
            is_granted = request.POST.get(f'perm_{permission_type}') == 'on'
            permission, created = UserPermission.objects.get_or_create(
                user_profile=user_profile,
                permission_type=permission_type,
                defaults={'is_granted': is_granted, 'granted_by': request.user}
            )
            if not created:
                permission.is_granted = is_granted
                permission.granted_by = request.user
                permission.save()
        
        # التحقق مما إذا كان الطلب من Ajax
        is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'
        if is_ajax:
            return HttpResponse(status=200)
        else:
            messages.success(request, f'تم تحديث صلاحيات المستخدم {user_profile.user.username} بنجاح')
            return redirect('inventory:user_detail', user_id=user_id)
    
    messages.error(request, 'طريقة طلب غير صحيحة')
    return redirect('inventory:user_detail', user_id=user_id)


@login_required(login_url='inventory:login')
def add_user(request):
    """إضافة مستخدم جديد"""
    # التحقق من صلاحيات المدير
    if request.user.username != 'admin':
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة. فقط مدير المستخدمين يمكنه الوصول.')
        return redirect('inventory:dashboard')
    
    # الحصول على جميع المخازن للاختيار
    warehouses = Warehouse.objects.filter(is_active=True)
    
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        role = request.POST.get('role')
        warehouse_id = request.POST.get('warehouse')
        department = request.POST.get('department')
        phone_number = request.POST.get('phone_number')
        
        # التحقق من البيانات المطلوبة
        if not (username and password and role):
            messages.error(request, 'يرجى ملء جميع الحقول المطلوبة')
            return redirect('inventory:add_user')
        
        # التحقق من عدم وجود مستخدم بنفس الاسم
        if User.objects.filter(username=username).exists():
            messages.error(request, 'اسم المستخدم موجود بالفعل')
            return redirect('inventory:add_user')
        
        try:
            # إنشاء المستخدم
            user = User.objects.create_user(
                username=username,
                password=password,
                first_name=first_name,
                last_name=last_name
            )
            
            # إنشاء ملف المستخدم
            user_profile = UserProfile(user=user, role=role, department=department, phone_number=phone_number)
            
            # تعيين المخزن إذا كان مسؤول مخزن
            if role == 'WAREHOUSE_MANAGER' and warehouse_id:
                try:
                    warehouse = Warehouse.objects.get(id=warehouse_id)
                    user_profile.assigned_warehouse = warehouse
                except Warehouse.DoesNotExist:
                    pass  # سيتم تعيين assigned_warehouse كقيمة فارغة
            
            user_profile.save()
            
            # إذا كان الدور مدير نفرض صلاحيات افتراضية
            if role == 'MANAGER':
                # منح صلاحيات المدير الافتراضية
                default_manager_permissions = [
                    'VIEW_INVENTORY', 'ADD_ITEMS', 'EDIT_ITEMS',
                    'VIEW_WAREHOUSE', 'GOODS_RECEIPT', 'GOODS_ISSUE', 'STOCK_TRANSFER',
                    'VIEW_REPORTS', 'EXPORT_REPORTS'
                ]
                
                for perm in default_manager_permissions:
                    UserPermission.objects.create(
                        user_profile=user_profile,
                        permission_type=perm,
                        is_granted=True,
                        granted_by=request.user
                    )
            
            messages.success(request, f'تم إنشاء المستخدم {username} بنجاح')
            return redirect('inventory:user_detail', user_id=user_profile.id)
            
        except Exception as e:
            messages.error(request, f'حدث خطأ أثناء إنشاء المستخدم: {str(e)}')
            return redirect('inventory:add_user')
    
    context = {
        'page_title': 'إضافة مستخدم جديد',
        'role_choices': UserProfile.USER_ROLES,
        'warehouses': warehouses,
    }
    
    return render(request, 'inventory/user_add.html', context)


@login_required(login_url='inventory:login')
def delete_user(request, user_id):
    """حذف مستخدم"""
    # التحقق من صلاحيات المدير
    if request.user.username != 'admin':
        messages.error(request, 'غير مصرح لك بالوصول لهذه الصفحة. فقط مدير المستخدمين يمكنه الوصول.')
        return redirect('inventory:dashboard')
    
    # جلب بيانات المستخدم
    user_profile = get_object_or_404(UserProfile, id=user_id)
    
    # لا يمكن حذف مدير النظام
    if user_profile.user.username == 'admin':
        messages.error(request, 'لا يمكن حذف مدير النظام')
        return redirect('inventory:users_list')
    
    if request.method == 'POST':
        # حذف صلاحيات المستخدم أولاً
        UserPermission.objects.filter(user_profile=user_profile).delete()
        
        # حذف مستخدم جانجو
        user = user_profile.user
        username = user.username
        user.delete()  # سيتم حذف ملف المستخدم تلقائياً بسبب CASCADE
        
        messages.success(request, f'تم حذف المستخدم {username} بنجاح')
        return redirect('inventory:users_list')
    
    context = {
        'page_title': 'حذف مستخدم',
        'user_profile': user_profile,
    }
    
    return render(request, 'inventory/user_delete_confirm.html', context)