"""
KeyInventory - حركة المخزون
تسجيل جميع حركات المخزون (وارد/صادر)
"""

from django.db import models
import uuid


class KeyInventoryMovement(models.Model):
    """
    نموذج حركات المخزون
    """
    
    MOVEMENT_TYPES = [
        ('IN', 'وارد'),
        ('OUT', 'صادر'),
        ('TRANSFER', 'تحويل'),
        ('ADJUSTMENT', 'تسوية'),
        ('RETURN', 'مرتجع'),
    ]
    
    MOVEMENT_REASONS = [
        ('PURCHASE', 'شراء'),
        ('SALE', 'بيع'),
        ('PRODUCTION', 'إنتاج'),
        ('CONSUMPTION', 'استهلاك'),
        ('DAMAGE', 'تلف'),
        ('EXPIRY', 'انتهاء صلاحية'),
        ('THEFT', 'سرقة'),
        ('CORRECTION', 'تصحيح'),
        ('SAMPLE', 'عينة'),
        ('OTHER', 'أخرى'),
    ]
    
    # Primary key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Movement Information
    movement_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='رقم الحركة'
    )
    
    movement_type = models.CharField(
        max_length=20,
        choices=MOVEMENT_TYPES,
        verbose_name='نوع الحركة'
    )
    
    movement_reason = models.CharField(
        max_length=20,
        choices=MOVEMENT_REASONS,
        verbose_name='سبب الحركة'
    )
    
    # Product and Quantity
    product = models.ForeignKey(
        'core.KeyProduct',
        on_delete=models.CASCADE,
        related_name='inventory_movements',
        verbose_name='المنتج'
    )
    
    quantity = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        verbose_name='الكمية'
    )
    
    unit_cost = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        default=0,
        verbose_name='تكلفة الوحدة'
    )
    
    total_cost = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name='التكلفة الإجمالية'
    )
    
    # Location Information
    warehouse = models.CharField(
        max_length=100,
        verbose_name='المخزن'
    )
    
    location = models.CharField(
        max_length=100,
        verbose_name='الموقع داخل المخزن',
        blank=True,
        null=True
    )
    
    # Related Information
    reference_document = models.CharField(
        max_length=100,
        verbose_name='المستند المرجعي',
        blank=True,
        null=True
    )
    
    related_transaction = models.ForeignKey(
        'core.KeyTransaction',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='inventory_movements',
        verbose_name='المعاملة المرتبطة'
    )

    # Date and User
    movement_date = models.DateField(
        verbose_name='تاريخ الحركة'
    )

    responsible_user = models.ForeignKey(
        'core.KeyUser',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='inventory_movements',
        verbose_name='المستخدم المسؤول'
    )
    
    # Additional Information
    notes = models.TextField(
        verbose_name='ملاحظات',
        blank=True,
        null=True
    )
    
    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    
    class Meta:
        verbose_name = 'حركة مخزون'
        verbose_name_plural = 'حركات المخزون'
        ordering = ['-movement_date', '-created_at']
        indexes = [
            models.Index(fields=['movement_number']),
            models.Index(fields=['product']),
            models.Index(fields=['movement_type']),
            models.Index(fields=['movement_date']),
            models.Index(fields=['warehouse']),
        ]
    
    def __str__(self):
        return f"{self.movement_number} - {self.product.product_name_ar}"
    
    def save(self, *args, **kwargs):
        # Calculate total cost
        self.total_cost = self.quantity * self.unit_cost
        super().save(*args, **kwargs)
