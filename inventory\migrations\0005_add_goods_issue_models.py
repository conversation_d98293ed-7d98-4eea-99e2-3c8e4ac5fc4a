# Generated by Django 5.2.5 on 2025-08-21 16:43

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0004_remove_adjustment_types'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='GoodsIssue',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('issue_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الإذن')),
                ('issue_type', models.CharField(choices=[('SALE', 'بيع'), ('DAMAGE', 'تلف')], max_length=20, verbose_name='نوع الصرف')),
                ('issue_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإذن')),
                ('status', models.CharField(choices=[('DRAFT', 'مسودة'), ('CONFIRMED', 'مؤكد'), ('COMPLETED', 'مكتمل'), ('CANCELLED', 'ملغي')], default='DRAFT', max_length=20, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('total_items', models.PositiveIntegerField(default=0, verbose_name='عدد الأصناف')),
                ('total_quantity', models.DecimalField(decimal_places=3, default=0, max_digits=15, verbose_name='إجمالي الكمية')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_issues', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('warehouse', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='goods_issues', to='inventory.warehouse', verbose_name='المخزن')),
            ],
            options={
                'verbose_name': 'إذن صرف',
                'verbose_name_plural': 'أذون الصرف',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoodsIssueItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=12, validators=[django.core.validators.MinValueValidator(0.001)], verbose_name='الكمية')),
                ('unit_of_measure', models.CharField(max_length=20, verbose_name='وحدة القياس')),
                ('unit_cost', models.DecimalField(blank=True, decimal_places=4, max_digits=12, null=True, verbose_name='تكلفة الوحدة')),
                ('total_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='إجمالي التكلفة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('bin_location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='issue_items', to='inventory.binlocation', verbose_name='موقع التخزين')),
                ('issue', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='issue_items', to='inventory.goodsissue', verbose_name='إذن الصرف')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='issue_items', to='inventory.itemmaster', verbose_name='الصنف')),
            ],
            options={
                'verbose_name': 'صنف إذن الصرف',
                'verbose_name_plural': 'أصناف أذون الصرف',
                'ordering': ['created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='goodsissue',
            index=models.Index(fields=['issue_number'], name='inventory_g_issue_n_ab24ac_idx'),
        ),
        migrations.AddIndex(
            model_name='goodsissue',
            index=models.Index(fields=['issue_type'], name='inventory_g_issue_t_625036_idx'),
        ),
        migrations.AddIndex(
            model_name='goodsissue',
            index=models.Index(fields=['issue_date'], name='inventory_g_issue_d_b4146d_idx'),
        ),
        migrations.AddIndex(
            model_name='goodsissue',
            index=models.Index(fields=['warehouse'], name='inventory_g_warehou_708707_idx'),
        ),
        migrations.AddIndex(
            model_name='goodsissue',
            index=models.Index(fields=['status'], name='inventory_g_status_02fad9_idx'),
        ),
        migrations.AddIndex(
            model_name='goodsissue',
            index=models.Index(fields=['created_at'], name='inventory_g_created_df16e5_idx'),
        ),
    ]
