# الجداول المرتبطة (Key Tables) - م<PERSON><PERSON><PERSON><PERSON> KamaVerse

## نظرة عامة

الجداول المرتبطة هي الجداول الأساسية التي تربط بين جميع موديولات النظام وتضمن التكامل الكامل بينها. جميع هذه الجداول تبدأ بكلمة "Key" لسهولة التعرف عليها وإدارتها.

---

## 1. KeyUsers - إدارة المستخدمين والصلاحيات

**الوظيفة:** إدارة جميع مستخدمي النظام وصلاحياتهم المفصلة  
**الموديولات المرتبطة:** جميع الموديولات  

### الحقول الأساسية:
- معر<PERSON> المستخدم (user_id)
- اسم المستخدم (username)
- كلمة المرور المشفرة (password_hash)
- البيانات الشخصية (personal_info)
- مستوى المستخدم (user_level)
- القسم التابع له (department)
- تاريخ الإنشاء والتعديل
- حالة النشاط (is_active)

### جداول فرعية:
- KeyUserPermissions: الصلاحيات التفصيلية لكل مستخدم
- KeyUserSessions: جلسات المستخدمين النشطة
- KeyUserAudit: سجل أنشطة المستخدمين

---

## 2. KeyCompanies - إدارة الشركات

**الوظيفة:** إدارة بيانات جميع الشركات (موردين وعملاء)  
**الموديولات المرتبطة:** Import, Sales, CRM, Finance, Logistics  

### الحقول الأساسية:
- معرف الشركة (company_id)
- اسم الشركة (company_name)
- نوع الشركة (supplier/customer/both)
- بيانات الاتصال (contact_info)
- العنوان التفصيلي (address)
- البيانات الضريبية (tax_info)
- شروط الدفع (payment_terms)
- تقييم الشركة (rating)

### العلاقات:
- مع Import: الموردين الخارجيين
- مع Sales: العملاء والمشترين
- مع CRM: إدارة العلاقات
- مع Finance: الحسابات المالية
- مع Logistics: عناوين الشحن

---

## 3. KeyProducts - إدارة المنتجات والمواد الخام

**الوظيفة:** إدارة جميع المواد الكيماوية والمنتجات  
**الموديولات المرتبطة:** Import, Stock, Sales, Finance  

### الحقول الأساسية:
- معرف المنتج (product_id)
- اسم المنتج (product_name)
- الكود الداخلي (internal_code)
- الكود الدولي (international_code)
- فئة المنتج (category)
- وحدة القياس (unit_of_measure)
- درجة الخطورة (hazard_level)
- شروط التخزين (storage_conditions)
- مدة الصلاحية (shelf_life)

### العلاقات:
- مع Import: المواد المستوردة
- مع Stock: كميات المخزون
- مع Sales: المنتجات المباعة
- مع Finance: أسعار وتكاليف المنتجات

---

## 4. KeyTransactions - المعاملات المالية

**الوظيفة:** تسجيل جميع المعاملات المالية في النظام  
**الموديولات المرتبطة:** Finance, Sales, Import, HR  

### الحقول الأساسية:
- معرف المعاملة (transaction_id)
- نوع المعاملة (transaction_type)
- المبلغ (amount)
- العملة (currency)
- تاريخ المعاملة (transaction_date)
- الطرف الآخر (counterparty)
- الحساب المصرفي (bank_account)
- حالة المعاملة (status)
- المرجع (reference)

### العلاقات:
- مع Finance: القيود المحاسبية
- مع Sales: مدفوعات العملاء
- مع Import: مدفوعات الموردين
- مع HR: رواتب الموظفين

---

## 5. KeyDocuments - إدارة المستندات

**الوظيفة:** إدارة جميع المستندات والملفات في النظام  
**الموديولات المرتبطة:** جميع الموديولات  

### الحقول الأساسية:
- معرف المستند (document_id)
- اسم المستند (document_name)
- نوع المستند (document_type)
- مسار الملف (file_path)
- حجم الملف (file_size)
- تاريخ الإنشاء (created_date)
- المستخدم المنشئ (created_by)
- مستوى السرية (confidentiality_level)

### العلاقات:
- مع Import: مستندات الشحن والجمارك
- مع Sales: عقود وفواتير البيع
- مع HR: ملفات الموظفين
- مع Finance: المستندات المالية

---

## 6. KeyApprovals - نظام الموافقات

**الوظيفة:** إدارة جميع الموافقات المطلوبة في النظام  
**الموديولات المرتبطة:** جميع الموديولات + Hawk  

### الحقول الأساسية:
- معرف الموافقة (approval_id)
- نوع الموافقة (approval_type)
- الطلب المرتبط (related_request)
- المستخدم الطالب (requester)
- المستخدم الموافق (approver)
- حالة الموافقة (status)
- تاريخ الطلب (request_date)
- تاريخ الموافقة (approval_date)
- ملاحظات (notes)

### العلاقات:
- مع Import: موافقات الشراء
- مع Sales: موافقات التسعير
- مع Finance: موافقات المدفوعات
- مع HR: موافقات الإجازات
- مع Hawk: مراقبة الموافقات

---

## 7. KeyShipments - إدارة الشحنات

**الوظيفة:** تتبع جميع الشحنات الواردة والصادرة  
**الموديولات المرتبطة:** Import, Logistics, Stock  

### الحقول الأساسية:
- معرف الشحنة (shipment_id)
- نوع الشحنة (inbound/outbound)
- المرسل (sender)
- المستقبل (receiver)
- وسيلة النقل (transport_method)
- تاريخ الإرسال (dispatch_date)
- تاريخ الوصول المتوقع (expected_arrival)
- حالة الشحنة (status)
- تكلفة الشحن (shipping_cost)

---

## 8. KeyInventory - حركة المخزون

**الوظيفة:** تسجيل جميع حركات المخزون (وارد/صادر)  
**الموديولات المرتبطة:** Stock, Sales, Import  

### الحقول الأساسية:
- معرف الحركة (movement_id)
- نوع الحركة (in/out/transfer)
- المنتج (product_id)
- الكمية (quantity)
- المخزن (warehouse)
- الموقع داخل المخزن (location)
- تاريخ الحركة (movement_date)
- المرجع (reference)
- المستخدم المسؤول (responsible_user)

---

## 9. KeyNotifications - نظام الإشعارات

**الوظيفة:** إدارة جميع الإشعارات في النظام  
**الموديولات المرتبطة:** جميع الموديولات + Kamachat  

### الحقول الأساسية:
- معرف الإشعار (notification_id)
- نوع الإشعار (notification_type)
- المستقبل (recipient)
- العنوان (title)
- المحتوى (content)
- مستوى الأهمية (priority)
- حالة القراءة (read_status)
- تاريخ الإنشاء (created_date)

---

## 10. KeyAuditLog - سجل العمليات

**الوظيفة:** تسجيل جميع العمليات المهمة في النظام  
**الموديولات المرتبطة:** جميع الموديولات + Hawk  

### الحقول الأساسية:
- معرف السجل (log_id)
- المستخدم (user_id)
- نوع العملية (operation_type)
- الموديول (module)
- تفاصيل العملية (operation_details)
- البيانات القديمة (old_data)
- البيانات الجديدة (new_data)
- تاريخ العملية (operation_date)
- عنوان IP (ip_address)

---

## ملاحظات مهمة

1. **التكامل:** جميع هذه الجداول مترابطة ومتكاملة
2. **الأمان:** تطبيق أعلى معايير الأمان على هذه الجداول
3. **الأداء:** فهرسة مناسبة لضمان الأداء السريع
4. **النسخ الاحتياطي:** نسخ احتياطية منتظمة لهذه الجداول الحيوية
5. **المراقبة:** مراقبة مستمرة لأداء هذه الجداول

---

**آخر تحديث:** 2025-08-15  
**المسؤول:** فريق التطوير KamaVerse
