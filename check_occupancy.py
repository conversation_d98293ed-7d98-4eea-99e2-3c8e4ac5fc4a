#!/usr/bin/env python
"""
التحقق من نسب الإشغال المحدثة
Check Updated Occupancy Percentages
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

from inventory.models import Warehouse

def check_occupancy():
    """عرض نسب الإشغال الحالية لجميع المخازن"""
    print("📊 نسب الإشغال الحالية:")
    
    warehouses = Warehouse.objects.filter(is_active=True)
    for warehouse in warehouses:
        occupancy = warehouse.get_occupancy_percentage()
        free = 100 - occupancy
        
        print(f"- {warehouse.warehouse_name}:")
        print(f"  • نسبة الإشغال: {occupancy:.1f}%")
        print(f"  • نسبة الفراغ: {free:.1f}%")
        print(f"  • السعة الكلية: {warehouse.total_capacity}")
        print(f"  • الإشغال الحالي: {warehouse.current_occupancy}")
        print()

if __name__ == '__main__':
    check_occupancy()