from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from inventory.models import UserProfile, UserPermission

class Command(BaseCommand):
    help = 'تعديل صلاحيات المستخدم أحمد محمد (username: manager) ليكون مدير مخازن مع صلاحيات كاملة'

    def handle(self, *args, **options):
        self.stdout.write('تعديل صلاحيات المستخدم أحمد محمد...')
        
        try:
            # الحصول على المستخدم أحمد محمد
            user = User.objects.get(username='manager')
            
            # الحصول على ملفه الشخصي
            profile = UserProfile.objects.get(user=user)
            
            # تعديل الدور إلى مدير مخازن
            profile.role = 'MANAGER'
            profile.assigned_warehouse = None  # إزالة تعيين المخزن ليكون مسؤول عن جميع المخازن
            profile.is_active = True
            profile.save()
            
            self.stdout.write(self.style.SUCCESS(f'تم تحديث دور المستخدم إلى {profile.get_role_display()}'))
            
            # منح كافة الصلاحيات المتاحة باستثناء صلاحيات إدارة المستخدمين (محصورة بالمدير admin)
            permissions_to_grant = [
                'VIEW_INVENTORY', 'ADD_ITEMS', 'EDIT_ITEMS', 'DELETE_ITEMS',
                'VIEW_WAREHOUSE', 'ADD_WAREHOUSE', 'EDIT_WAREHOUSE', 'DELETE_WAREHOUSE',
                'GOODS_RECEIPT', 'GOODS_ISSUE', 'STOCK_TRANSFER',
                'VIEW_REPORTS', 'EXPORT_REPORTS', 'VIEW_ALL_REPORTS'
            ]
            
            for perm_type in permissions_to_grant:
                permission, created = UserPermission.objects.update_or_create(
                    user_profile=profile,
                    permission_type=perm_type,
                    defaults={'is_granted': True}
                )
                
                action = 'تم إنشاء' if created else 'تم تحديث'
                self.stdout.write(f'{action} صلاحية: {perm_type}')
            
            self.stdout.write(self.style.SUCCESS('تم منح كافة الصلاحيات المطلوبة للمستخدم أحمد محمد'))
            
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR('لم يتم العثور على المستخدم أحمد محمد (username: manager)'))
        except UserProfile.DoesNotExist:
            self.stdout.write(self.style.ERROR('لم يتم العثور على الملف الشخصي للمستخدم'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'حدث خطأ: {e}'))