"""
Fix for the stock transfer inventory management system
This script corrects the flow to ensure:
1. Quantities decrease from the manager-selected warehouse
2. Quantities increase in the requesting warehouse 
3. Existing items have their quantities updated instead of creating new entries

Execute this script to apply the changes to the system.
"""

import os
import sys
import django
import re

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

def fix_stock_transfer_model():
    """Fix the StockTransferItem model's update_stock_on_approval method"""
    
    models_path = "e:/New folder (10)/hive/test26-pt2/kamach_test01/inventory/models.py"
    
    # Read the file
    with open(models_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Fix the duplicate update_stock_on_completion methods
    pattern1 = r'def update_stock_on_completion\(\s*self\s*\):\s*"""تحديث أرصدة المخزون عند إكمال النقل - لا حاجة لها حاليًا لأنها تمت بالفعل عند الاعتماد"""\s*# تم نقل منطق التحديث إلى update_stock_on_approval\s*return True\s*notes=.*?\)\s*\s*return True'
    replacement1 = 'def update_stock_on_completion(self):\n        """تحديث أرصدة المخزون عند إكمال النقل - لا حاجة لها حاليًا لأنها تمت بالفعل عند الاعتماد"""\n        # تم نقل منطق التحديث إلى update_stock_on_approval\n        return True'
    
    # Apply the fixes
    content = re.sub(pattern1, replacement1, content, flags=re.DOTALL)
    
    # Remove duplicate update_stock_on_completion method
    pattern2 = r'def update_stock_on_completion\(\s*self\s*\):\s*"""تحديث أرصدة المخزون عند إكمال النقل"""\s*.*?return True\s*\s*def update_stock_on_approval'
    replacement2 = 'def update_stock_on_approval'
    
    content = re.sub(pattern2, replacement2, content, flags=re.DOTALL)
    
    # Write the updated content back to the file
    with open(models_path, 'w', encoding='utf-8') as file:
        file.write(content)
    
    print("✅ StockTransferItem model fixed successfully!")

if __name__ == "__main__":
    fix_stock_transfer_model()
    print("\n🎉 All fixes have been applied!")