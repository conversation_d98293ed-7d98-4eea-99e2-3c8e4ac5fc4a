<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }} - <PERSON><PERSON>Verse</title>
    
    <!-- Google Fonts - Cairo for Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
        }
        
        .print-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .company-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #D62828;
            padding-bottom: 20px;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: 700;
            color: #D62828;
            margin-bottom: 10px;
        }
        
        .company-name-en {
            font-size: 18px;
            color: #666;
            margin-bottom: 15px;
        }
        
        .document-title {
            font-size: 24px;
            font-weight: 700;
            color: #D62828;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .receipt-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .info-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .info-title {
            font-size: 16px;
            font-weight: 600;
            color: #D62828;
            margin-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 8px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }
        
        .info-label {
            font-weight: 500;
            color: #666;
        }
        
        .info-value {
            font-weight: 600;
            color: #333;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
            border: 2px solid #D62828;
        }
        
        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: right;
            border: 1px solid #dee2e6;
        }
        
        .items-table th {
            background: #D62828;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }
        
        .items-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .items-table td {
            font-size: 13px;
        }
        
        .totals-section {
            background: #D62828;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 30px 0;
        }
        
        .totals-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            text-align: center;
        }
        
        .total-item {
            padding: 10px;
        }
        
        .total-value {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .total-label {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .signatures-section {
            margin-top: 50px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 40px;
        }
        
        .signature-box {
            text-align: center;
            border-top: 2px solid #333;
            padding-top: 10px;
        }
        
        .signature-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 30px;
        }
        
        .notes-section {
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .notes-title {
            font-weight: 600;
            color: #D62828;
            margin-bottom: 10px;
        }
        
        .print-actions {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .print-btn {
            background: #D62828;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        
        .print-btn:hover {
            background: #B71C1C;
        }
        
        @media print {
            .print-actions {
                display: none;
            }
            
            body {
                font-size: 12px;
            }
            
            .print-container {
                max-width: none;
                margin: 0;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <!-- رأس الشركة -->
        <div class="company-header">
            <h1 class="company-name">شركة القماش للاستيراد والتصدير</h1>
            <p class="company-name-en">Elkamash Import & Export Company</p>
        </div>

        <!-- عنوان المستند -->
        <div class="document-title">
            إذن استلام رقم: {{ receipt.receipt_number }}
        </div>

        <!-- معلومات الإذن -->
        <div class="receipt-info">
            <div class="info-section">
                <h3 class="info-title">معلومات الإذن</h3>
                <div class="info-item">
                    <span class="info-label">رقم الإذن:</span>
                    <span class="info-value">{{ receipt.receipt_number }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">تاريخ الإذن:</span>
                    <span class="info-value">{{ receipt.receipt_date|date:"Y-m-d H:i" }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الحالة:</span>
                    <span class="info-value">{{ receipt.get_status_display }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">المسؤول:</span>
                    <span class="info-value">{{ receipt.created_by.get_full_name }}</span>
                </div>
            </div>

            <div class="info-section">
                <h3 class="info-title">معلومات المخزن</h3>
                <div class="info-item">
                    <span class="info-label">اسم المخزن:</span>
                    <span class="info-value">{{ receipt.warehouse.warehouse_name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">كود المخزن:</span>
                    <span class="info-value">{{ receipt.warehouse.warehouse_code }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">الموقع:</span>
                    <span class="info-value">{{ receipt.warehouse.location }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">المسؤول:</span>
                    <span class="info-value">{{ receipt.warehouse.manager_name|default:"-" }}</span>
                </div>
            </div>
        </div>

        <!-- جدول الأصناف -->
        <table class="items-table">
            <thead>
                <tr>
                    <th>م</th>
                    <th>كود الصنف</th>
                    <th>اسم الصنف</th>
                    <th>الكمية</th>
                    <th>الوحدة</th>
                    <th>موقع التخزين</th>
                    <th>رقم الدفعة</th>
                    <th>تكلفة الوحدة</th>
                    <th>إجمالي التكلفة</th>
                </tr>
            </thead>
            <tbody>
                {% for item in receipt.receipt_items.all %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ item.item.item_code }}</td>
                    <td>{{ item.item.item_name_ar }}</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ item.item.unit_of_measure }}</td>
                    <td>{{ item.bin_location.bin_code }}</td>
                    <td>{{ item.batch_number|default:"-" }}</td>
                    <td>{{ item.unit_cost|default:"-" }}</td>
                    <td>{{ item.total_cost|default:"-" }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="9" style="text-align: center; color: #666;">
                        لا توجد أصناف في هذا الإذن
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- المجاميع -->
        <div class="totals-section">
            <div class="totals-grid">
                <div class="total-item">
                    <div class="total-value">{{ receipt.total_items }}</div>
                    <div class="total-label">عدد الأصناف</div>
                </div>
                <div class="total-item">
                    <div class="total-value">{{ receipt.total_quantity }}</div>
                    <div class="total-label">إجمالي الكمية</div>
                </div>
            </div>
        </div>

        <!-- الملاحظات -->
        {% if receipt.notes %}
        <div class="notes-section">
            <h3 class="notes-title">ملاحظات:</h3>
            <p>{{ receipt.notes }}</p>
        </div>
        {% endif %}

        <!-- التوقيعات -->
        <div class="signatures-section">
            <div class="signature-box">
                <div class="signature-title">مسؤول المخزن</div>
                <div style="height: 50px;"></div>
                <div>التوقيع: ________________</div>
            </div>
            <div class="signature-box">
                <div class="signature-title">مدير المخازن</div>
                <div style="height: 50px;"></div>
                <div>التوقيع: ________________</div>
            </div>
            <div class="signature-box">
                <div class="signature-title">المحاسب</div>
                <div style="height: 50px;"></div>
                <div>التوقيع: ________________</div>
            </div>
        </div>

        <!-- أزرار الطباعة -->
        <div class="print-actions">
            <button class="print-btn" onclick="window.print()">
                طباعة الإذن
            </button>
            <button class="print-btn" onclick="window.close()">
                إغلاق
            </button>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند فتح الصفحة (اختياري)
        // window.onload = function() { window.print(); }
    </script>
</body>
</html>
