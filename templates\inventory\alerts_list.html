{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }} - KamaVerse{% endblock %}

{% block extra_css %}
<style>
    /* Header styling to match main page */
    .content-header {
        background: linear-gradient(135deg, var(--brand-red) 0%, var(--brand-red-dark) 100%);
        color: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(214, 40, 40, 0.15);
    }
    
    .header-info h1.page-title {
        color: white;
        font-size: 36px;
        font-weight: 700;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .header-info .page-description {
        color: white;
        font-size: 20px;
        font-weight: 400;
        margin: 0;
        opacity: 0.9;
    }
    
    .header-info h1.page-title i {
        color: #C89A3C;
    }
    
    /* Search and filter styling */
    .search-filter-bar {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .alert-search-form .form-control,
    .alert-search-form .form-select {
        padding: 12px;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
    }
    
    .alert-search-form .form-control:focus,
    .alert-search-form .form-select:focus {
        border-color: var(--brand-red);
        box-shadow: 0 0 0 0.2rem rgba(214, 40, 40, 0.25);
    }
    
    .alert-search-form .btn-primary {
        background-color: var(--brand-red);
        border-color: var(--brand-red);
        padding: 12px;
    }
    
    .alert-search-form .btn-primary:hover {
        background-color: var(--brand-red-dark);
        border-color: var(--brand-red-dark);
    }
    
    /* Alerts styling */
    .alerts-container {
        margin-top: 20px;
    }
    
    .alert-tabs {
        display: flex;
        background: white;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 20px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .alert-tab {
        flex: 1;
        text-align: center;
        padding: 15px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .alert-tab.active {
        background: var(--brand-red);
        color: white;
    }
    
    .alert-count {
        display: inline-block;
        background: var(--brand-red);
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        font-size: 12px;
        line-height: 24px;
        text-align: center;
        margin-right: 5px;
    }
    
    .alert-tab.active .alert-count {
        background: white;
        color: var(--brand-red);
    }
    
    .alert-list {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .alert-item {
        padding: 15px;
        border-bottom: 1px solid #eee;
        display: flex;
        align-items: flex-start;
        transition: all 0.3s ease;
    }
    
    .alert-item:hover {
        background: #f9f9f9;
    }
    
    .alert-item:last-child {
        border-bottom: none;
    }
    
    .alert-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 15px;
        flex-shrink: 0;
    }
    
    .alert-icon.item {
        background: #e3f2fd;
        color: #1976d2;
    }
    
    .alert-icon.stock {
        background: #fff3e0;
        color: #ff9800;
    }
    
    .alert-icon.receipt {
        background: #e8f5e9;
        color: #4caf50;
    }
    
    .alert-icon.issue {
        background: #ffebee;
        color: #f44336;
    }
    
    .alert-icon.transfer {
        background: #e8eaf6;
        color: #3f51b5;
    }
    
    .alert-content {
        flex: 1;
    }
    
    .alert-title {
        font-weight: 600;
        margin-bottom: 5px;
        color: #333;
    }
    
    .alert-message {
        color: #666;
        font-size: 14px;
    }
    
    .alert-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        font-size: 12px;
        color: #999;
    }
    
    .alert-date {
        font-style: italic;
    }
    
    .alert-actions {
        text-align: left;
    }
    
    .mark-read-btn {
        background: none;
        border: none;
        color: var(--brand-red);
        cursor: pointer;
        font-weight: 600;
        padding: 5px 10px;
        font-size: 12px;
    }
    
    .mark-read-btn:hover {
        text-decoration: underline;
    }
    
    .no-alerts {
        text-align: center;
        padding: 40px 0;
        color: #999;
    }
    
    .no-alerts i {
        font-size: 48px;
        margin-bottom: 15px;
        color: #ddd;
    }
    
    .alert-badge {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 600;
        margin-right: 5px;
    }
    
    .alert-badge.low {
        background: #ffebee;
        color: #f44336;
    }
    
    .alert-badge.medium {
        background: #fff3e0;
        color: #ff9800;
    }
    
    .alert-badge.high {
        background: #e8f5e9;
        color: #4caf50;
    }
    
    .alert-status {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-left: 5px;
    }
    
    .alert-status.unread {
        background: var(--brand-red);
    }
    
    .alert-status.read {
        background: #ccc;
    }

    .tab-content {
        display: none;
    }
    
    .tab-content.active {
        display: block;
    }

    /* Pagination Styling */
    .pagination-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }

    .pagination {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .btn-pagination {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 16px;
        border-radius: 50px;
        background-color: white;
        color: #333;
        border: 1px solid #e0e0e0;
        text-decoration: none;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .btn-pagination:hover {
        background-color: #f5f5f5;
        border-color: #d0d0d0;
    }

    .page-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        text-decoration: none;
        color: #333;
        transition: all 0.3s ease;
    }

    .page-number:hover {
        background-color: #f5f5f5;
    }

    .page-number.active {
        background-color: var(--brand-red);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="main-content">
    <div class="content-header">
        <div class="header-info">
            <h1 class="page-title">
                <i class="fas fa-bell"></i>
                {{ page_title }}
            </h1>
            <p class="page-description">عرض وإدارة تنبيهات النظام</p>
        </div>
    </div>

    <div class="alerts-container">
        <!-- Search and Filter Bar -->
        <div class="search-filter-bar">
            <form method="get" class="alert-search-form">
                <div class="row">
                    <div class="col-md-5">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="ابحث عن التنبيهات..." value="{{ search_query }}">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <select name="alert_type" class="form-select">
                            <option value="">جميع أنواع التنبيهات</option>
                            {% for alert_type, alert_name in alert_type_choices %}
                            <option value="{{ alert_type }}" {% if alert_type_filter == alert_type %}selected{% endif %}>
                                {{ alert_name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">بحث</button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Alert Tabs -->
        <div class="alert-tabs">
            <div class="alert-tab active" data-tab="unread">
                <span class="alert-count">{{ unread_count }}</span>
                تنبيهات جديدة
            </div>
            <div class="alert-tab" data-tab="read">
                تنبيهات مقروءة
            </div>
        </div>

        <!-- Unread Alerts -->
        <div class="tab-content active" id="unread">
            <div class="alert-list">
                {% if unread_alerts %}
                    {% for alert in unread_alerts %}
                        <div class="alert-item">
                            <!-- Alert Icon based on type -->
                            <div class="alert-icon 
                                {% if 'ITEM' in alert.alert_type %}item{% endif %}
                                {% if 'STOCK' in alert.alert_type %}stock{% endif %}
                                {% if 'RECEIPT' in alert.alert_type %}receipt{% endif %}
                                {% if 'ISSUE' in alert.alert_type %}issue{% endif %}
                                {% if 'TRANSFER' in alert.alert_type %}transfer{% endif %}">
                                {% if 'ITEM_ADDED' == alert.alert_type %}
                                    <i class="fas fa-plus"></i>
                                {% elif 'ITEM_MODIFIED' == alert.alert_type %}
                                    <i class="fas fa-edit"></i>
                                {% elif 'ITEM_DELETED' == alert.alert_type %}
                                    <i class="fas fa-trash-alt"></i>
                                {% elif 'LOW_STOCK' == alert.alert_type %}
                                    <i class="fas fa-exclamation-triangle"></i>
                                {% elif 'RECEIPT_CREATED' == alert.alert_type %}
                                    <i class="fas fa-file-invoice"></i>
                                {% elif 'ISSUE_CREATED' == alert.alert_type %}
                                    <i class="fas fa-file-export"></i>
                                {% elif 'TRANSFER_REQUESTED' == alert.alert_type or 'TRANSFER_APPROVED' == alert.alert_type %}
                                    <i class="fas fa-exchange-alt"></i>
                                {% else %}
                                    <i class="fas fa-bell"></i>
                                {% endif %}
                            </div>

                            <!-- Alert Content -->
                            <div class="alert-content">
                                <div class="alert-title">{{ alert.title }}</div>
                                <div class="alert-message">{{ alert.message }}</div>
                                <div class="alert-meta">
                                    <div class="alert-date">{{ alert.created_at|date:"Y-m-d H:i" }}</div>
                                    <div class="alert-actions">
                                        <form method="post" action="{% url 'inventory:mark_alert_read' alert.id %}">
                                            {% csrf_token %}
                                            <button type="submit" class="mark-read-btn">تمييز كمقروء</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="no-alerts">
                        <i class="fas fa-check-circle"></i>
                        <h3>لا توجد تنبيهات جديدة</h3>
                        <p>أنت محدث بكل شيء!</p>
                    </div>
                {% endif %}
            </div>

            <!-- Pagination for Unread Alerts -->
            {% if unread_alerts.paginator.num_pages > 1 %}
            <div class="pagination-container">
                <div class="pagination">
                    {% if unread_alerts.has_previous %}
                    <a href="?unread_page={{ unread_alerts.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if alert_type_filter %}&alert_type={{ alert_type_filter }}{% endif %}" class="btn-pagination">
                        <i class="fas fa-chevron-left"></i> السابق
                    </a>
                    {% endif %}
                    
                    {% for num in unread_alerts.paginator.page_range %}
                        {% if num == unread_alerts.number %}
                        <span class="page-number active">{{ num }}</span>
                        {% elif num > unread_alerts.number|add:"-2" and num < unread_alerts.number|add:"3" %}
                        <a href="?unread_page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if alert_type_filter %}&alert_type={{ alert_type_filter }}{% endif %}" class="page-number">{{ num }}</a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if unread_alerts.has_next %}
                    <a href="?unread_page={{ unread_alerts.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if alert_type_filter %}&alert_type={{ alert_type_filter }}{% endif %}" class="btn-pagination">
                        التالي <i class="fas fa-chevron-right"></i>
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Read Alerts -->
        <div class="tab-content" id="read">
            <div class="alert-list">
                {% if read_alerts %}
                    {% for alert in read_alerts %}
                        <div class="alert-item">
                            <!-- Alert Icon based on type -->
                            <div class="alert-icon 
                                {% if 'ITEM' in alert.alert_type %}item{% endif %}
                                {% if 'STOCK' in alert.alert_type %}stock{% endif %}
                                {% if 'RECEIPT' in alert.alert_type %}receipt{% endif %}
                                {% if 'ISSUE' in alert.alert_type %}issue{% endif %}
                                {% if 'TRANSFER' in alert.alert_type %}transfer{% endif %}">
                                {% if 'ITEM_ADDED' == alert.alert_type %}
                                    <i class="fas fa-plus"></i>
                                {% elif 'ITEM_MODIFIED' == alert.alert_type %}
                                    <i class="fas fa-edit"></i>
                                {% elif 'ITEM_DELETED' == alert.alert_type %}
                                    <i class="fas fa-trash-alt"></i>
                                {% elif 'LOW_STOCK' == alert.alert_type %}
                                    <i class="fas fa-exclamation-triangle"></i>
                                {% elif 'RECEIPT_CREATED' == alert.alert_type %}
                                    <i class="fas fa-file-invoice"></i>
                                {% elif 'ISSUE_CREATED' == alert.alert_type %}
                                    <i class="fas fa-file-export"></i>
                                {% elif 'TRANSFER_REQUESTED' == alert.alert_type or 'TRANSFER_APPROVED' == alert.alert_type %}
                                    <i class="fas fa-exchange-alt"></i>
                                {% else %}
                                    <i class="fas fa-bell"></i>
                                {% endif %}
                            </div>

                            <!-- Alert Content -->
                            <div class="alert-content">
                                <div class="alert-title">{{ alert.title }}</div>
                                <div class="alert-message">{{ alert.message }}</div>
                                <div class="alert-meta">
                                    <div class="alert-date">{{ alert.created_at|date:"Y-m-d H:i" }}</div>
                                    <div class="alert-actions">
                                        <span class="text-muted">تم القراءة</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="no-alerts">
                        <i class="fas fa-history"></i>
                        <h3>لا توجد تنبيهات مقروءة</h3>
                        <p>سيظهر هنا سجل التنبيهات التي قمت بقراءتها</p>
                    </div>
                {% endif %}
            </div>

            <!-- Pagination for Read Alerts -->
            {% if read_alerts.paginator.num_pages > 1 %}
            <div class="pagination-container">
                <div class="pagination">
                    {% if read_alerts.has_previous %}
                    <a href="?read_page={{ read_alerts.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if alert_type_filter %}&alert_type={{ alert_type_filter }}{% endif %}" class="btn-pagination">
                        <i class="fas fa-chevron-left"></i> السابق
                    </a>
                    {% endif %}
                    
                    {% for num in read_alerts.paginator.page_range %}
                        {% if num == read_alerts.number %}
                        <span class="page-number active">{{ num }}</span>
                        {% elif num > read_alerts.number|add:"-2" and num < read_alerts.number|add:"3" %}
                        <a href="?read_page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if alert_type_filter %}&alert_type={{ alert_type_filter }}{% endif %}" class="page-number">{{ num }}</a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if read_alerts.has_next %}
                    <a href="?read_page={{ read_alerts.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if alert_type_filter %}&alert_type={{ alert_type_filter }}{% endif %}" class="btn-pagination">
                        التالي <i class="fas fa-chevron-right"></i>
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Tab switching functionality
    document.addEventListener('DOMContentLoaded', function() {
        const tabs = document.querySelectorAll('.alert-tab');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                tabs.forEach(t => t.classList.remove('active'));
                
                // Add active class to clicked tab
                this.classList.add('active');
                
                // Hide all tab content
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                
                // Show selected tab content
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId).classList.add('active');
            });
        });
    });
</script>
{% endblock %}