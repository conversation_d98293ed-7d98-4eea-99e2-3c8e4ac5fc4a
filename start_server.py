#!/usr/bin/env python
"""
تشغيل خادم Django
Start Django Server
"""

import os
import sys
import subprocess

def main():
    """تشغيل الخادم"""
    print("🚀 بدء تشغيل خادم Django...")
    
    try:
        # تشغيل الخادم
        result = subprocess.run([
            sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000'
        ], check=True)
        
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        return 1
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم بواسطة المستخدم")
        return 0
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
