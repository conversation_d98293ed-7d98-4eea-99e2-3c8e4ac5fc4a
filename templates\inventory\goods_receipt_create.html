{% extends 'base.html' %}
{% load static %}

{% block title %}إنشاء إذن استلام جديد - <PERSON><PERSON>erse{% endblock %}

{% block extra_css %}
<style>
    /* الحاوي الرئيسي */
    .receipt-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 30px;
    }

    /* رأس الصفحة */
    .receipt-header {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        padding: 25px 30px;
        text-align: center;
    }

    .receipt-header h1 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: bold;
        color: white;
    }

    .receipt-header p {
        margin: 8px 0 0 0;
        opacity: 0.9;
        font-size: 14px;
        color: white;
    }

    /* جسم الصفحة */
    .receipt-body {
        padding: 30px;
    }

    /* القسم العلوي - معلومات الإذن */
    .receipt-info {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 30px;
        border: 1px solid #e9ecef;
    }

    .info-title {
        color: #dc3545;
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .info-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 15px;
    }

    .info-group {
        display: flex;
        flex-direction: column;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
        font-size: 13px;
    }

    .info-value {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 10px 12px;
        font-size: 14px;
        color: #333;
    }

    .info-value.readonly {
        background: #e9ecef;
        color: #6c757d;
    }

    /* التبويبات */
    .tabs-container {
        margin-bottom: 30px;
    }

    .nav-tabs {
        border-bottom: 2px solid #dc3545;
        margin-bottom: 25px;
        display: flex;
        list-style: none;
        padding: 0;
    }

    .nav-tabs .nav-item {
        margin-left: 5px;
    }

    .nav-tabs .nav-link {
        border: none;
        color: #6c757d;
        font-weight: 600;
        padding: 12px 25px;
        border-radius: 8px 8px 0 0;
        transition: all 0.3s ease;
        cursor: pointer;
        text-decoration: none;
        display: block;
    }

    .nav-tabs .nav-link.active {
        background: #dc3545;
        color: white;
        border-bottom: 2px solid #dc3545;
    }

    .nav-tabs .nav-link:hover:not(.active) {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    /* محتوى التبويبات */
    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
    }

    /* جدول الأصناف */
    .items-table-container {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .items-table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .items-table th {
        background: #dc3545;
        color: white;
        padding: 15px 12px;
        text-align: center;
        font-weight: 600;
        font-size: 14px;
    }

    .items-table td {
        padding: 12px;
        border-bottom: 1px solid #dee2e6;
        text-align: center;
    }

    .items-table tbody tr:hover {
        background: #f8f9fa;
    }

    .items-table input, .items-table select {
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 8px 10px;
        width: 100%;
        font-size: 13px;
    }

    .items-table input:focus, .items-table select:focus {
        border-color: #dc3545;
        outline: none;
        box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
    }

    .btn-add-row {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 600;
        margin-top: 15px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-add-row:hover {
        background: linear-gradient(135deg, #20c997, #17a2b8);
        transform: translateY(-2px);
    }

    .btn-remove {
        background: #dc3545;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
    }

    .btn-remove:hover {
        background: #c82333;
    }

    /* منطقة رفع Excel */
    .excel-upload-area {
        border: 2px dashed #dc3545;
        border-radius: 10px;
        padding: 40px;
        text-align: center;
        background: #fff5f5;
        transition: all 0.3s ease;
        cursor: pointer;
        margin-bottom: 20px;
    }

    .excel-upload-area:hover {
        border-color: #c82333;
        background: #ffe6e6;
    }

    .excel-upload-area.dragover {
        border-color: #28a745;
        background: #f0fff4;
    }

    .upload-icon {
        font-size: 48px;
        color: #dc3545;
        margin-bottom: 15px;
    }

    .upload-text {
        font-size: 16px;
        color: #495057;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .upload-hint {
        font-size: 12px;
        color: #6c757d;
    }

    .download-template {
        background: #17a2b8;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 5px;
        font-size: 12px;
        margin-top: 10px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
    }

    .download-template:hover {
        background: #138496;
        color: white;
        text-decoration: none;
    }

    /* القسم السفلي - المجاميع */
    .totals-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 30px;
        border: 1px solid #e9ecef;
    }

    .totals-title {
        color: #dc3545;
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .totals-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .total-item {
        background: white;
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        border: 1px solid #dee2e6;
    }

    .total-label {
        font-size: 12px;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .total-value {
        font-size: 18px;
        font-weight: bold;
        color: #dc3545;
    }

    /* أزرار الحفظ */
    .action-buttons {
        text-align: center;
        padding-top: 20px;
        border-top: 1px solid #dee2e6;
    }

    .btn-save {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        border: none;
        padding: 15px 40px;
        border-radius: 8px;
        font-weight: bold;
        font-size: 16px;
        margin-left: 15px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .btn-save:hover {
        background: linear-gradient(135deg, #c82333, #bd2130);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }

    .btn-cancel {
        background: #6c757d;
        color: white;
        border: none;
        padding: 15px 40px;
        border-radius: 8px;
        font-weight: bold;
        font-size: 16px;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }

    .btn-cancel:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .info-row {
            grid-template-columns: 1fr;
        }

        .totals-row {
            grid-template-columns: 1fr;
        }

        .action-buttons {
            text-align: center;
        }

        .btn-save, .btn-cancel {
            width: 100%;
            margin: 5px 0;
        }

        .nav-tabs {
            flex-direction: column;
        }

        .nav-tabs .nav-item {
            margin: 2px 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- الحاوي الرئيسي -->
<div class="receipt-container">
    <!-- رأس الصفحة -->
    <div class="receipt-header">
        <h1><i class="fas fa-arrow-down me-3"></i>إنشاء إذن استلام جديد</h1>
        <p>إضافة مواد جديدة إلى المخزون مع إمكانية الإدخال اليدوي أو رفع ملف Excel</p>
    </div>

    <!-- جسم الصفحة -->
    <div class="receipt-body">
        <form method="post" id="receiptForm" enctype="multipart/form-data">
            {% csrf_token %}

            <!-- القسم العلوي - معلومات الإذن -->
            <div class="receipt-info">
                <div class="info-title">
                    <i class="fas fa-info-circle"></i>
                    معلومات الإذن
                </div>

                <div class="info-row">
                    <div class="info-group">
                        <label class="info-label">رقم الإذن</label>
                        <input type="text" class="info-value readonly" value="GR-2025-08-0001" readonly>
                    </div>

                    <div class="info-group">
                        <label class="info-label">المخزن المسؤول *</label>
                        <select name="warehouse" class="info-value" required id="warehouseSelect">
                            <option value="">اختر المخزن</option>
                            {% for warehouse in warehouses %}
                                <option value="{{ warehouse.id }}">{{ warehouse.warehouse_name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="info-group">
                        <label class="info-label">التاريخ</label>
                        <input type="text" class="info-value readonly" value="{% now 'Y-m-d H:i' %}" readonly>
                    </div>

                    <div class="info-group">
                        <label class="info-label">المسؤول</label>
                        <input type="text" class="info-value readonly" value="{{ user.get_full_name|default:user.username }}" readonly>
                    </div>
                </div>

                <div class="info-row">
                    <div class="info-group" style="grid-column: 1 / -1;">
                        <label class="info-label">ملاحظات</label>
                        <textarea name="notes" class="info-value" rows="2" placeholder="ملاحظات إضافية (اختياري)"></textarea>
                    </div>
                </div>
            </div>

            <!-- التبويبات -->
            <div class="tabs-container">
                <ul class="nav-tabs">
                    <li class="nav-item">
                        <a class="nav-link active" onclick="switchTab('manual')" id="manual-tab">
                            <i class="fas fa-keyboard me-2"></i>
                            الإدخال اليدوي
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" onclick="switchTab('excel')" id="excel-tab">
                            <i class="fas fa-file-excel me-2"></i>
                            رفع Excel
                        </a>
                    </li>
                </ul>

                <!-- تبويب الإدخال اليدوي -->
                <div id="manual-content" class="tab-content active">
                    <div class="items-table-container">
                        <table class="items-table" id="itemsTable">
                            <thead>
                                <tr>
                                    <th>كود الصنف *</th>
                                    <th>اسم الصنف *</th>
                                    <th>الكمية *</th>
                                    <th>الوحدة *</th>
                                    <th>موقع التخزين (اختياري)</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="itemsTableBody">
                                <!-- سيتم إضافة الصفوف هنا بـ JavaScript -->
                            </tbody>
                        </table>

                        <button type="button" class="btn-add-row" onclick="addItemRow()">
                            <i class="fas fa-plus me-2"></i>
                            إضافة صف جديد
                        </button>
                    </div>
                </div>

                <!-- تبويب رفع Excel -->
                <div id="excel-content" class="tab-content">
                    <div class="excel-upload-area" onclick="document.getElementById('excelFile').click()"
                         ondrop="handleDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">اسحب وأفلت ملف Excel هنا أو انقر للاختيار</div>
                        <div class="upload-hint">يدعم ملفات .xlsx و .xls فقط</div>
                        <input type="file" id="excelFile" name="excel_file" accept=".xlsx,.xls" style="display: none;" onchange="handleFileSelect(event)">
                        <br>
                        <a href="{% url 'inventory:download_excel_template' %}" class="download-template">
                            <i class="fas fa-download me-1"></i>
                            تحميل قالب Excel
                        </a>
                    </div>

                    <div id="excel-preview" style="display: none;">
                        <h4>معاينة البيانات من ملف Excel:</h4>
                        <div class="items-table-container">
                            <table class="items-table" id="excelPreviewTable">
                                <thead>
                                    <tr>
                                        <th>كود الصنف</th>
                                        <th>اسم الصنف</th>
                                        <th>الكمية</th>
                                        <th>الوحدة</th>
                                        <th>موقع التخزين</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody id="excelPreviewBody">
                                    <!-- سيتم إضافة البيانات هنا -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- القسم السفلي - المجاميع -->
            <div class="totals-section">
                <div class="totals-title">
                    <i class="fas fa-calculator"></i>
                    المجاميع
                </div>

                <div class="totals-row">
                    <div class="total-item">
                        <div class="total-label">عدد الأصناف</div>
                        <div class="total-value" id="totalItems">0</div>
                    </div>

                    <div class="total-item">
                        <div class="total-label">إجمالي الكميات</div>
                        <div class="total-value" id="totalQuantity">0.000</div>
                    </div>

                    <div class="total-item">
                        <div class="total-label">عدد المواقع</div>
                        <div class="total-value" id="totalLocations">0</div>
                    </div>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="action-buttons">
                <button type="submit" class="btn-save">
                    <i class="fas fa-save me-2"></i>
                    حفظ الإذن
                </button>
                <a href="{% url 'inventory:movements_list' %}" class="btn-cancel">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </a>
            </div>

            <!-- حقول مخفية -->
            <input type="hidden" name="item_count" id="itemCount" value="0">
            <input type="hidden" name="data_source" id="dataSource" value="manual">
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// متغيرات عامة
let itemRowCount = 1;

// دالة لإنشاء خيارات الأصناف
function getItemsOptions() {
    return `
        {% for item in items %}
        <option value="{{ item.id }}" data-code="{{ item.item_code }}">{{ item.item_name_ar }} ({{ item.item_code }})</option>
        {% endfor %}
    `;
}
let binLocations = {};
let excelData = [];

// وظائف التبويبات
function switchTab(tabName) {
    // إخفاء جميع التبويبات
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // إزالة الفئة النشطة من جميع الروابط
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // إظهار التبويب المحدد
    document.getElementById(tabName + '-content').classList.add('active');
    document.getElementById(tabName + '-tab').classList.add('active');

    // تحديث مصدر البيانات
    document.getElementById('dataSource').value = tabName;
}

// وظائف الإدخال اليدوي
function addItemRow() {
    // إزالة شرط اختيار المخزن - يمكن إضافة الصفوف بدون اختيار المخزن
    // const warehouseId = document.getElementById('warehouseSelect').value;
    // if (!warehouseId) {
    //     alert('يرجى اختيار المخزن أولاً');
    //     return;
    // }

    const tbody = document.getElementById('itemsTableBody');
    const row = document.createElement('tr');
    row.innerHTML = `
        <td>
            <input type="text" name="item_code_${itemRowCount}" class="form-control"
                   required placeholder="سيتم ملؤه تلقائياً" readonly>
        </td>
        <td>
            <select name="item_name_${itemRowCount}" class="form-control"
                    required onchange="updateItemCode(this, ${itemRowCount}); updateTotals();">
                <option value="">اختر اسم الصنف</option>
                <option value="new_item">+ إضافة صنف جديد</option>
                ${getItemsOptions()}
            </select>
            <input type="text" name="new_item_name_${itemRowCount}" class="form-control"
                   style="display:none; margin-top:5px;" placeholder="اسم الصنف الجديد">
            <input type="text" name="new_item_code_${itemRowCount}" class="form-control"
                   style="display:none; margin-top:5px;" placeholder="كود الصنف الجديد">
        </td>
        <td>
            <input type="number" name="quantity_${itemRowCount}" class="form-control"
                   step="0.001" min="0.001" required onchange="updateTotals()" placeholder="0.000">
        </td>
        <td>
            <select name="unit_${itemRowCount}" class="form-control" required>
                <option value="">اختر الوحدة</option>
                <option value="كيلو">كيلو</option>
                <option value="جرام">جرام</option>
                <option value="لتر">لتر</option>
                <option value="متر">متر</option>
                <option value="قطعة">قطعة</option>
                <option value="كيس">كيس</option>
                <option value="صندوق">صندوق</option>
                <option value="برميل">برميل</option>
                <option value="طن">طن</option>
            </select>
        </td>
        <td>
            <select name="bin_location_${itemRowCount}" class="form-control">
                <option value="">اختر الموقع (اختياري)</option>
                <option value="A01">A01</option>
                <option value="A02">A02</option>
                <option value="A10">A10</option>
                <option value="B01">B01</option>
                <option value="B02">B02</option>
                <option value="C01">C01</option>
                <option value="C02">C02</option>
                <option value="D01">D01</option>
            </select>
        </td>
        <td>
            <button type="button" class="btn-remove" onclick="removeItemRow(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

    tbody.appendChild(row);
    itemRowCount++;
    updateTotals();
}

function removeItemRow(button) {
    button.closest('tr').remove();
    updateTotals();
}

// دالة تحديث كود الصنف تلقائياً
function updateItemCode(selectElement, rowIndex) {
    const selectedOption = selectElement.options[selectElement.selectedIndex];
    const codeInput = document.querySelector(`input[name="item_code_${rowIndex}"]`);
    const newItemNameInput = document.querySelector(`input[name="new_item_name_${rowIndex}"]`);
    const newItemCodeInput = document.querySelector(`input[name="new_item_code_${rowIndex}"]`);

    if (selectElement.value === 'new_item') {
        // إظهار حقول الصنف الجديد
        newItemNameInput.style.display = 'block';
        newItemCodeInput.style.display = 'block';
        newItemNameInput.required = true;
        newItemCodeInput.required = true;
        codeInput.value = '';
        codeInput.placeholder = 'سيتم ملؤه من الحقل أدناه';

        // ربط حقل كود الصنف الجديد بالحقل الرئيسي
        newItemCodeInput.addEventListener('input', function() {
            codeInput.value = this.value;
        });

    } else if (selectElement.value && selectElement.value !== 'new_item') {
        // صنف موجود - ملء الكود تلقائياً
        const itemCode = selectedOption.getAttribute('data-code');
        codeInput.value = itemCode || '';
        codeInput.placeholder = 'كود الصنف';

        // إخفاء حقول الصنف الجديد
        newItemNameInput.style.display = 'none';
        newItemCodeInput.style.display = 'none';
        newItemNameInput.required = false;
        newItemCodeInput.required = false;
        newItemNameInput.value = '';
        newItemCodeInput.value = '';

    } else {
        // لا يوجد اختيار
        codeInput.value = '';
        codeInput.placeholder = 'سيتم ملؤه تلقائياً';

        // إخفاء حقول الصنف الجديد
        newItemNameInput.style.display = 'none';
        newItemCodeInput.style.display = 'none';
        newItemNameInput.required = false;
        newItemCodeInput.required = false;
        newItemNameInput.value = '';
        newItemCodeInput.value = '';
    }
}

// وظائف رفع Excel
function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
}

function handleDragLeave(e) {
    e.currentTarget.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleExcelFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        handleExcelFile(file);
    }
}

function handleExcelFile(file) {
    // التحقق من نوع الملف
    const allowedTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                         'application/vnd.ms-excel'];

    if (!allowedTypes.includes(file.type)) {
        alert('يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)');
        return;
    }

    // التحقق من اختيار المخزن
    const warehouseSelect = document.getElementById('warehouseSelect');
    if (!warehouseSelect.value) {
        alert('يرجى اختيار المخزن أولاً');
        return;
    }

    // عرض اسم الملف
    const uploadArea = document.querySelector('.excel-upload-area');
    const uploadText = uploadArea.querySelector('.upload-text');
    uploadText.innerHTML = `جاري تحليل الملف: <strong>${file.name}</strong>`;
    uploadArea.style.borderColor = '#007bff';
    uploadArea.style.backgroundColor = '#f0f8ff';

    // إظهار منطقة المعاينة مع رسالة تحميل
    document.getElementById('excel-preview').style.display = 'block';
    const previewBody = document.getElementById('excelPreviewBody');
    previewBody.innerHTML = `
        <tr>
            <td colspan="6" style="text-align: center; padding: 20px; color: #007bff;">
                <i class="fas fa-spinner fa-spin" style="font-size: 24px; margin-bottom: 10px;"></i><br>
                جاري تحليل ملف Excel...
            </td>
        </tr>
    `;

    // إرسال الملف للخادم للمعاينة
    const formData = new FormData();
    formData.append('excel_file', file);
    formData.append('warehouse', warehouseSelect.value);
    formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);

    fetch('{% url "inventory:process_excel_preview" %}', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // عرض البيانات المعالجة
            showExcelPreview(data.data);
            
            // تحديث نص منطقة الرفع
            uploadText.innerHTML = `تم تحليل الملف: <strong>${file.name}</strong> - ${data.total_items} صنف`;
            uploadArea.style.borderColor = '#28a745';
            uploadArea.style.backgroundColor = '#f0fff4';
            
            // عرض التحذيرات إن وجدت
            if (data.warnings && data.warnings.length > 0) {
                console.warn('تحذيرات:', data.warnings);
            }
        } else {
            // عرض الأخطاء
            previewBody.innerHTML = `
                <tr>
                    <td colspan="6" style="text-align: center; padding: 20px; color: #dc3545;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i><br>
                        <strong>خطأ في معالجة الملف:</strong><br>
                        ${data.errors.join('<br>')}
                    </td>
                </tr>
            `;
            
            uploadText.innerHTML = `خطأ في الملف: <strong>${file.name}</strong>`;
            uploadArea.style.borderColor = '#dc3545';
            uploadArea.style.backgroundColor = '#fff5f5';
        }
    })
    .catch(error => {
        console.error('خطأ في الشبكة:', error);
        previewBody.innerHTML = `
            <tr>
                <td colspan="6" style="text-align: center; padding: 20px; color: #dc3545;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i><br>
                    خطأ في الاتصال بالخادم
                </td>
            </tr>
        `;
        
        uploadText.innerHTML = `خطأ في معالجة الملف: <strong>${file.name}</strong>`;
        uploadArea.style.borderColor = '#dc3545';
        uploadArea.style.backgroundColor = '#fff5f5';
    });
}

function showExcelPreview(data) {
    const tbody = document.getElementById('excelPreviewBody');
    tbody.innerHTML = '';

    data.forEach(item => {
        const row = document.createElement('tr');
        let statusClass = 'text-primary';
        let iconClass = 'fa-info-circle';

        if (item.status.includes('جديد')) {
            statusClass = 'text-success';
            iconClass = 'fa-plus-circle';
        } else if (item.status.includes('موجود')) {
            statusClass = 'text-info';
            iconClass = 'fa-check-circle';
        } else if (item.status.includes('خطأ')) {
            statusClass = 'text-danger';
            iconClass = 'fa-exclamation-triangle';
        }

        row.innerHTML = `
            <td>${item.item_code}</td>
            <td>${item.item_name}</td>
            <td>${item.quantity}</td>
            <td>${item.unit}</td>
            <td>غير محدد</td>
            <td class="${statusClass}">
                <i class="fas ${iconClass}"></i>
                ${item.status}
            </td>
        `;

        tbody.appendChild(row);
    });

    // تحديث المجاميع
    updateTotalsFromExcel(data);
}

function downloadTemplate() {
    // إنشاء رابط تحميل قالب Excel
    const csvContent = "كود الصنف,اسم الصنف,الكمية,الوحدة,موقع التخزين\nCH001,حمض الكبريتيك,100,لتر,A01\nPL002,بولي إيثيلين,50,كيلو,B01\nCH003,هيدروكسيد الصوديوم,75,كيلو,";
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'قالب_إذن_الاستلام.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// وظائف المجاميع
function updateTotals() {
    const activeTab = document.querySelector('.tab-content.active').id;

    if (activeTab === 'manual-content') {
        updateTotalsFromManual();
    } else {
        updateTotalsFromExcel(excelData);
    }
}

function updateTotalsFromManual() {
    const rows = document.getElementById('itemsTableBody').children;
    let totalItems = rows.length;
    let totalQuantity = 0;
    let uniqueLocations = new Set();

    for (let row of rows) {
        const quantityInput = row.querySelector('input[name*="quantity_"]');
        const locationSelect = row.querySelector('select[name*="bin_location_"]');

        if (quantityInput && quantityInput.value) {
            totalQuantity += parseFloat(quantityInput.value);
        }

        if (locationSelect && locationSelect.value) {
            uniqueLocations.add(locationSelect.value);
        }
    }

    document.getElementById('totalItems').textContent = totalItems;
    document.getElementById('totalQuantity').textContent = totalQuantity.toFixed(3);
    document.getElementById('totalLocations').textContent = uniqueLocations.size;
}

function updateTotalsFromExcel(data) {
    if (!data || data.length === 0) return;

    const totalItems = data.length;
    const totalQuantity = data.reduce((sum, item) => sum + parseFloat(item.quantity || 0), 0);
    const uniqueLocations = 1; // نظراً لأن موقع التخزين سيتم تحديده في الخادم

    document.getElementById('totalItems').textContent = totalItems;
    document.getElementById('totalQuantity').textContent = totalQuantity.toFixed(3);
    document.getElementById('totalLocations').textContent = uniqueLocations;
}

// تحميل مواقع التخزين عند تغيير المخزن
document.getElementById('warehouseSelect').addEventListener('change', function() {
    const warehouseId = this.value;
    if (warehouseId) {
        // هنا يمكن إضافة AJAX لتحميل مواقع التخزين الحقيقية
        binLocations[warehouseId] = [
            {id: 'A01', name: 'A01'}, {id: 'A02', name: 'A02'}, {id: 'A10', name: 'A10'},
            {id: 'B01', name: 'B01'}, {id: 'B02', name: 'B02'}, {id: 'B10', name: 'B10'}
        ];
    }
});

// التحقق من صحة النموذج قبل الإرسال
document.getElementById('receiptForm').addEventListener('submit', function(e) {
    const activeTab = document.querySelector('.tab-content.active').id;

    if (activeTab === 'manual-content') {
        const rows = document.getElementById('itemsTableBody').children;
        if (rows.length === 0) {
            e.preventDefault();
            alert('يرجى إضافة صنف واحد على الأقل');
            return;
        }
        // تحديث عدد الأصناف للإدخال اليدوي
        document.getElementById('itemCount').value = rows.length;
    } else if (activeTab === 'excel-content') {
        const fileInput = document.getElementById('excelFile');
        if (!fileInput.files.length) {
            e.preventDefault();
            alert('يرجى رفع ملف Excel');
            return;
        }
        // تحديث عدد الأصناف لـ Excel (سيتم حسابه في الخادم)
        document.getElementById('itemCount').value = 0;
    }

    const warehouseSelect = document.getElementById('warehouseSelect');
    if (!warehouseSelect.value) {
        e.preventDefault();
        alert('يرجى اختيار المخزن');
        return;
    }

    // إظهار رسالة تحميل
    const submitBtn = document.querySelector('.btn-save');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    submitBtn.disabled = true;

    // إعادة تفعيل الزر في حالة الخطأ
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 10000);
});

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث المجاميع الأولية
    updateTotals();
});
</script>
{% endblock %}
