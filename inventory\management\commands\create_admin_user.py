from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from inventory.models import UserProfile
from django.db import transaction
import logging

class Command(BaseCommand):
    help = 'Creates or updates the super admin user (admin:admin123) with full system access'

    def handle(self, *args, **kwargs):
        try:
            with transaction.atomic():
                # Check if admin user exists
                admin_user = User.objects.filter(username='admin').first()
                
                if admin_user:
                    self.stdout.write(self.style.WARNING('Admin user already exists. Updating permissions...'))
                    admin_user.set_password('admin123')
                    admin_user.is_staff = True
                    admin_user.is_superuser = True
                    admin_user.save()
                else:
                    # Create new admin user
                    admin_user = User.objects.create_user(
                        username='admin',
                        email='<EMAIL>',
                        password='admin123',
                        first_name='مدير',
                        last_name='النظام',
                        is_staff=True,
                        is_superuser=True
                    )
                    self.stdout.write(self.style.SUCCESS('Admin user created successfully'))
                
                # Create or update UserProfile
                profile, created = UserProfile.objects.get_or_create(
                    user=admin_user,
                    defaults={
                        'role': 'MANAGER',
                        'is_active': True,
                        'department': 'MANAGEMENT',
                    }
                )
                
                if not created:
                    profile.role = 'MANAGER'
                    profile.is_active = True
                    profile.save()
                    self.stdout.write(self.style.SUCCESS('Admin profile updated successfully'))
                else:
                    self.stdout.write(self.style.SUCCESS('Admin profile created successfully'))
                
                self.stdout.write(
                    self.style.SUCCESS(
                        '\nSuper admin user created with the following credentials:\n'
                        'Username: admin\n'
                        'Password: admin123\n\n'
                        'This user has exclusive access to user management and all system features.'
                    )
                )
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating admin user: {e}'))
            raise