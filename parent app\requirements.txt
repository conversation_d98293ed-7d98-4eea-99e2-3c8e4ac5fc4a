# KamaVerse - متطلبات المشروع
# نظام ERP متكامل لشركة القماش

# Django Framework
Django==4.2.7
djangorestframework==3.14.0
django-cors-headers==4.3.1
django-filter==23.3
django-extensions==3.2.3

# Database
psycopg2-binary==2.9.7
django-environ==0.11.2

# Authentication & Security
django-allauth==0.57.0
djangorestframework-simplejwt==5.3.0
django-guardian==2.4.0
cryptography==41.0.7

# Async & Real-time
channels==4.0.0
channels-redis==4.1.0
redis==4.6.0

# Background Tasks
celery==5.3.1
django-celery-beat==2.5.0
django-celery-results==2.5.1

# File Handling
Pillow==10.0.0
django-storages==1.14.2
boto3==1.29.7

# Reports & Export
reportlab==4.0.4
openpyxl==3.1.2
xlsxwriter==3.1.9
weasyprint==60.1

# API Documentation
drf-spectacular==0.26.5
django-silk==5.0.4

# Internationalization
django-rosetta==0.9.9

# Development Tools
django-debug-toolbar==4.2.0
django-livereload-server==0.4
factory-boy==3.3.0

# Testing
pytest==7.4.3
pytest-django==4.7.0
pytest-cov==4.1.0
coverage==7.3.2

# Utilities
python-decouple==3.8
requests==2.31.0
python-dateutil==2.8.2
pytz==2023.3
arabic-reshaper==3.0.0
python-bidi==0.4.2

# Monitoring & Logging
sentry-sdk==1.38.0
django-health-check==3.17.0

# Production
gunicorn==21.2.0
whitenoise==6.6.0
dj-database-url==2.1.0
