{% extends 'base.html' %}
{% load static %}
{% load inventory_filters %}

{% block title %}إدارة صلاحيات المستخدم - KamaVerse{% endblock %}

{% block extra_css %}
<!-- Animate.css for animations -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<style>
    .page-header {
        background: linear-gradient(135deg, var(--brand-red), #FF6B6B);
        color: var(--white);
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 10px 25px rgba(214, 40, 40, 0.2);
    }

    .page-header h1 {
        font-weight: 700;
        margin: 0;
        color: var(--white);
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .user-info-section {
        background-color: var(--white);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--line);
        transition: all 0.3s ease;
    }

    .user-info-section:hover {
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        transform: translateY(-5px);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--brand-red);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        position: relative;
        padding-bottom: 10px;
    }

    .section-title:after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 60px;
        height: 3px;
        background: var(--brand-gold);
        border-radius: 3px;
    }

    .section-title i {
        margin-left: 0.5rem;
        color: var(--brand-gold);
    }

    .user-card {
        display: flex;
        align-items: center;
        gap: 2rem;
        margin-bottom: 2.5rem;
        padding: 1.5rem;
        border-radius: 15px;
        background-color: rgba(255, 255, 255, 0.8);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }
    
    .user-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    }

    .user-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--brand-gold-light), var(--brand-gold));
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        color: var(--white);
        flex-shrink: 0;
        box-shadow: 0 8px 25px rgba(200, 154, 60, 0.4);
        border: 4px solid rgba(255, 255, 255, 0.9);
        transition: all 0.3s ease;
    }
    
    .user-card:hover .user-avatar {
        transform: rotate(5deg) scale(1.05);
    }

    .user-details {
        flex-grow: 1;
    }

    .user-details h3 {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        color: var(--brand-red);
        font-weight: 700;
    }

    .user-details p {
        color: var(--slate);
        margin-bottom: 0.8rem;
        font-size: 1.1rem;
        position: relative;
        padding-right: 1.5rem;
    }
    
    .user-details p:before {
        content: '\f105'; /* Font Awesome chevron icon */
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        position: absolute;
        right: 0;
        color: var(--brand-gold);
    }

    .role-badge {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 30px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .role-manager {
        background-color: rgba(25, 135, 84, 0.15);
        color: #198754;
    }

    .role-warehouse-manager {
        background-color: rgba(13, 110, 253, 0.15);
        color: #0d6efd;
    }

    .role-employee {
        background-color: rgba(108, 117, 125, 0.15);
        color: #6c757d;
    }

    .permissions-section {
        background-color: var(--white);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border: 1px solid var(--line);
        transition: all 0.3s ease;
    }
    
    .permissions-section:hover {
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .permissions-group {
        margin-bottom: 2rem;
        border-bottom: 1px solid var(--line);
        padding-bottom: 1.5rem;
        position: relative;
    }

    .permissions-group h3 {
        color: var(--brand-red);
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 1.2rem;
        display: flex;
        align-items: center;
    }
    
    .permissions-group h3:before {
        content: '\f084'; /* Font Awesome key icon */
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        margin-right: 10px;
        color: var(--brand-gold);
    }

    .permission-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        padding: 0.8rem 1rem;
        border-radius: 10px;
        transition: all 0.2s ease;
        border: 1px solid rgba(0, 0, 0, 0.05);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.03);
        position: relative;
        overflow: hidden;
    }

    .permission-item:hover {
        background-color: rgba(214, 40, 40, 0.05);
        transform: translateX(5px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
    }

    .permission-item .form-check {
        margin-left: 1rem;
    }

    .permission-name {
        font-weight: 500;
        flex-grow: 1;
        color: var(--ink);
        transition: all 0.2s ease;
        position: relative;
        padding-right: 10px;
    }
    
    .permission-item:hover .permission-name {
        color: var(--brand-red);
    }

    .permission-name:before {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 4px;
        background: var(--brand-gold);
        border-radius: 50%;
    }

    .form-switch .form-check-input {
        width: 3.5em;
        height: 1.8em;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        border: none;
        background-color: #ced4da;
    }

    .form-switch .form-check-input:checked {
        background-color: var(--brand-red);
        border-color: var(--brand-red);
        transform: scale(1.05);
    }
    
    .form-switch .form-check-input:focus {
        box-shadow: 0 0 0 0.25rem rgba(214, 40, 40, 0.25);
        border: none;
    }

    .form-switch .form-check-input::after {
        content: '';
        position: absolute;
        top: 0.1em;
        right: 0.1em;
        width: 1.6em;
        height: 1.6em;
        border-radius: 50%;
        background-color: white;
        transition: transform 0.15s ease-in-out;
        transform: translateX(0);
    }

    .form-switch .form-check-input:checked::after {
        transform: translateX(1.7em);
    }

    .role-select-container {
        margin-bottom: 2rem;
        background-color: rgba(255, 255, 255, 0.7);
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .form-select, .form-control {
        border-radius: 10px;
        padding: 12px 15px;
        font-size: 1.1rem;
        border: 1px solid rgba(0, 0, 0, 0.1);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        background-color: var(--white);
    }
    
    .form-select:focus, .form-control:focus {
        border-color: var(--brand-red);
        box-shadow: 0 0 0 0.25rem rgba(214, 40, 40, 0.25);
    }
    
    .form-label {
        font-weight: 600;
        margin-bottom: 0.75rem;
        color: var(--ink);
        font-size: 1.1rem;
        display: flex;
        align-items: center;
    }
    
    .form-label i {
        margin-left: 8px;
        color: var(--brand-gold);
    }
    
    .form-check-label {
        font-weight: 500;
        padding-right: 0.5rem;
        display: flex;
        align-items: center;
    }

    .form-check-input:checked ~ .form-check-label {
        color: var(--brand-red);
    }

    .action-buttons {
        margin-top: 2.5rem;
        display: flex;
        gap: 1.5rem;
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .action-buttons .btn {
        padding: 12px 30px;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 50px;
        box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border: none;
        min-width: 180px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .action-buttons .btn:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 25px rgba(0, 0, 0, 0.2);
    }
    
    .btn-save {
        background: linear-gradient(135deg, var(--brand-red), #d62828);
        color: white;
    }
    
    .btn-save:hover {
        background: linear-gradient(135deg, #d62828, #9c1f1f);
    }
    
    .btn-back {
        background: linear-gradient(135deg, #6c757d, #495057);
        color: white;
    }
    
    .btn-back:hover {
        background: linear-gradient(135deg, #495057, #343a40);
    }
    
    .action-buttons .btn i {
        margin-left: 8px;
    }

    /* Enhanced permission group styling */
    .permissions-group {
        background: rgba(248, 249, 250, 0.5);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .permissions-group h3 {
        margin-top: 0;
        padding-bottom: 10px;
        border-bottom: 2px solid rgba(200, 154, 60, 0.3);
    }

    /* Alert styling */
    .alert-warning {
        border-radius: 10px;
        border: none;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }

    .alert-icon {
        color: var(--brand-gold);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .user-card {
            flex-direction: column;
            text-align: center;
        }
        
        .user-details p:before {
            display: none;
        }
        
        .action-buttons .btn {
            min-width: 140px;
            padding: 10px 20px;
            font-size: 1rem;
        }
        
        .permission-item {
            flex-direction: column;
            align-items: flex-start;
        }
        
        .permission-item .form-check {
            margin-bottom: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <h1>
            <i class="fas fa-user-shield me-2"></i>
            إدارة صلاحيات المستخدم
        </h1>
    </div>

    <!-- معلومات المستخدم -->
    <div class="user-info-section">
        <h2 class="section-title">
            <i class="fas fa-user"></i>
            معلومات المستخدم
        </h2>
        
        <div class="user-card">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            
            <div class="user-details">
                <h3>{{ user_profile.user.get_full_name|default:user_profile.user.username }}</h3>
                <p>اسم المستخدم: <strong>{{ user_profile.user.username }}</strong></p>
                <p>
                    الدور: 
                    <span class="role-badge {% if user_profile.role == 'MANAGER' %}role-manager{% elif user_profile.role == 'WAREHOUSE_MANAGER' %}role-warehouse-manager{% else %}role-employee{% endif %}">
                        {{ user_profile.get_role_display }}
                    </span>
                </p>
                <p>القسم: <strong>{{ user_profile.department|default:"غير محدد" }}</strong></p>
                <p>المخزن المخصص: <strong>{{ user_profile.assigned_warehouse.warehouse_name|default:"جميع المخازن" }}</strong></p>
                <p>رقم الهاتف: <strong>{{ user_profile.phone_number|default:"غير محدد" }}</strong></p>
                <p>الحالة: <strong>{{ user_profile.is_active|yesno:"نشط,غير نشط" }}</strong></p>
            </div>
        </div>

        <form method="post" action="{% url 'inventory:update_user_permissions' user_id=user_profile.id %}">
            {% csrf_token %}
            
            <div class="role-select-container">
                <div class="row">
                    <div class="col-md-6">
                        <label for="role" class="form-label"><i class="fas fa-user-tag me-2"></i>تعديل الدور</label>
                        <select name="role" id="role" class="form-select">
                            {% for role_code, role_name in role_choices %}
                            <option value="{{ role_code }}" {% if user_profile.role == role_code %}selected{% endif %}>{{ role_name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    {% if user_profile.role == 'WAREHOUSE_MANAGER' %}
                    <div class="col-md-6 warehouse-select">
                        <label for="warehouse" class="form-label"><i class="fas fa-warehouse me-2"></i>المخزن المخصص</label>
                        <select name="warehouse" id="warehouse" class="form-select">
                            <option value="">-- اختر المخزن --</option>
                            {% for warehouse in warehouses %}
                            <option value="{{ warehouse.id }}" {% if user_profile.assigned_warehouse.id == warehouse.id %}selected{% endif %}>{{ warehouse.warehouse_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    {% endif %}
                </div>
                
                <div class="form-check mt-4">
                    <input class="form-check-input" type="checkbox" name="is_active" id="is_active" {% if user_profile.is_active %}checked{% endif %}>
                    <label class="form-check-label" for="is_active">
                        <i class="fas fa-toggle-on me-2 text-success"></i> مستخدم نشط
                    </label>
                </div>
            </div>

            <!-- الصلاحيات -->
            <div class="permissions-section">
                <h2 class="section-title">
                    <i class="fas fa-key me-2"></i>
                    صلاحيات المستخدم
                </h2>
                
                <!-- صلاحيات المخزون -->
                <div class="permissions-group">
                    <h3><i class="fas fa-boxes me-2"></i> صلاحيات المخزون</h3>
                    
                    <div class="row">
                        {% for perm_code, perm_name in available_permissions %}
                        {% if perm_code == 'VIEW_INVENTORY' or perm_code == 'ADD_ITEMS' or perm_code == 'EDIT_ITEMS' or perm_code == 'DELETE_ITEMS' %}
                        <div class="col-md-6">
                            <div class="permission-item">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="perm_{{ perm_code }}" id="perm_{{ perm_code }}" {% if granted_permissions|get_item:perm_code %}checked{% endif %}>
                                </div>
                                <label class="permission-name" for="perm_{{ perm_code }}">
                                    {{ perm_name }}
                                </label>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
                
                <!-- صلاحيات المخازن -->
                <div class="permissions-group">
                    <h3><i class="fas fa-warehouse me-2"></i> صلاحيات المخازن</h3>
                    
                    <div class="row">
                        {% for perm_code, perm_name in available_permissions %}
                        {% if perm_code == 'VIEW_WAREHOUSE' or perm_code == 'ADD_WAREHOUSE' or perm_code == 'EDIT_WAREHOUSE' or perm_code == 'DELETE_WAREHOUSE' %}
                        <div class="col-md-6">
                            <div class="permission-item">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="perm_{{ perm_code }}" id="perm_{{ perm_code }}" {% if granted_permissions|get_item:perm_code %}checked{% endif %}>
                                </div>
                                <label class="permission-name" for="perm_{{ perm_code }}">
                                    {{ perm_name }}
                                </label>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
                
                <!-- صلاحيات الحركات -->
                <div class="permissions-group">
                    <h3><i class="fas fa-truck-loading me-2"></i> صلاحيات الحركات</h3>
                    
                    <div class="row">
                        {% for perm_code, perm_name in available_permissions %}
                        {% if perm_code == 'GOODS_RECEIPT' or perm_code == 'GOODS_ISSUE' or perm_code == 'STOCK_TRANSFER' %}
                        <div class="col-md-6">
                            <div class="permission-item">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="perm_{{ perm_code }}" id="perm_{{ perm_code }}" {% if granted_permissions|get_item:perm_code %}checked{% endif %}>
                                </div>
                                <label class="permission-name" for="perm_{{ perm_code }}">
                                    {{ perm_name }}
                                </label>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
                
                <!-- صلاحيات التقارير -->
                <div class="permissions-group">
                    <h3><i class="fas fa-chart-bar me-2"></i> صلاحيات التقارير</h3>
                    
                    <div class="row">
                        {% for perm_code, perm_name in available_permissions %}
                        {% if perm_code == 'VIEW_REPORTS' or perm_code == 'EXPORT_REPORTS' %}
                        <div class="col-md-6">
                            <div class="permission-item">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="perm_{{ perm_code }}" id="perm_{{ perm_code }}" {% if granted_permissions|get_item:perm_code %}checked{% endif %}>
                                </div>
                                <label class="permission-name" for="perm_{{ perm_code }}">
                                    {{ perm_name }}
                                </label>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
                
                <!-- صلاحيات إدارة المستخدمين (للمدير فقط) -->
                {% if user_profile.user.username != 'admin' %}
                <div class="permissions-group">
                    <h3><i class="fas fa-users-cog me-2"></i> صلاحيات إدارة المستخدمين</h3>
                    
                    <div class="row">
                        {% for perm_code, perm_name in available_permissions %}
                        {% if perm_code == 'MANAGE_USERS' or perm_code == 'EDIT_USER_PERMISSIONS' or perm_code == 'DELETE_USERS' %}
                        <div class="col-md-6">
                            <div class="permission-item">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="perm_{{ perm_code }}" id="perm_{{ perm_code }}" {% if granted_permissions|get_item:perm_code %}checked{% endif %}>
                                </div>
                                <label class="permission-name" for="perm_{{ perm_code }}">
                                    {{ perm_name }}
                                </label>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                    
                    {% if user_profile.role == 'MANAGER' %}
                    <div class="alert alert-warning">
                        <div class="d-flex align-items-center">
                            <div class="alert-icon me-3">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                            <div>
                                <strong>ملاحظة هامة:</strong> صلاحيات إدارة المستخدمين متاحة فقط لمدير النظام (admin).
                                <p class="mb-0 mt-1">لن يتم تفعيل هذه الصلاحيات حتى لو تم تحديدها.</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            
            <div class="action-buttons">
                <button type="submit" class="btn btn-save btn-lg">
                    <i class="fas fa-save me-2"></i>
                    حفظ التغييرات
                </button>
                
                <a href="{% url 'inventory:users_list' %}" class="btn btn-back btn-lg">
                    <i class="fas fa-arrow-right me-2"></i>
                    العودة للقائمة
                </a>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إظهار/إخفاء حقل المخزن حسب الدور المختار
        const roleSelect = document.getElementById('role');
        const warehouseSelect = document.querySelector('.warehouse-select');
        
        if (roleSelect && warehouseSelect) {
            roleSelect.addEventListener('change', function() {
                if (this.value === 'WAREHOUSE_MANAGER') {
                    warehouseSelect.style.display = 'block';
                    warehouseSelect.classList.add('animate__animated', 'animate__fadeIn');
                } else {
                    // إخفاء بأنيميشن لطيف
                    warehouseSelect.classList.add('animate__animated', 'animate__fadeOut');
                    setTimeout(() => {
                        warehouseSelect.style.display = 'none';
                        warehouseSelect.classList.remove('animate__animated', 'animate__fadeOut');
                    }, 300);
                }
            });
            
            // تطبيق الحالة الأولية
            if (roleSelect.value !== 'WAREHOUSE_MANAGER') {
                warehouseSelect.style.display = 'none';
            }
        }
        
        // إضافة التأثيرات المرئية للمفاتيح التبديلية (switches)
        const permissionSwitches = document.querySelectorAll('.form-check-input[type="checkbox"]');
        
        permissionSwitches.forEach(switchEl => {
            switchEl.addEventListener('change', function() {
                const parentItem = this.closest('.permission-item');
                if (this.checked) {
                    parentItem.style.backgroundColor = 'rgba(25, 135, 84, 0.1)';
                    parentItem.style.borderColor = 'rgba(25, 135, 84, 0.3)';
                    parentItem.style.transform = 'translateX(10px)';
                } else {
                    parentItem.style.backgroundColor = '';
                    parentItem.style.borderColor = 'rgba(0, 0, 0, 0.05)';
                    parentItem.style.transform = '';
                }
            });
            
            // تطبيق الحالة الأولية
            if (switchEl.checked) {
                const parentItem = switchEl.closest('.permission-item');
                if (parentItem) {
                    parentItem.style.backgroundColor = 'rgba(25, 135, 84, 0.1)';
                    parentItem.style.borderColor = 'rgba(25, 135, 84, 0.3)';
                }
            }
        });
        
        // تحسين تجربة إرسال النموذج باستخدام AJAX
        const permissionsForm = document.querySelector('form');
        const saveBtn = document.querySelector('button[type="submit"]');
        
        if (permissionsForm && saveBtn) {
            permissionsForm.addEventListener('submit', function(event) {
                event.preventDefault();
                
                // تغيير حالة الزر إلى جاري الحفظ
                saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> جاري الحفظ...';
                saveBtn.disabled = true;
                
                // الحصول على بيانات النموذج
                const formData = new FormData(this);
                
                // إرسال البيانات باستخدام AJAX
                fetch(this.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (response.ok) {
                        // تم الحفظ بنجاح
                        saveBtn.innerHTML = '<i class="fas fa-check-circle me-2"></i> تم الحفظ بنجاح';
                        saveBtn.classList.remove('btn-save');
                        saveBtn.classList.add('btn-success');
                        
                        // عرض رسالة نجاح
                        const successMessage = document.createElement('div');
                        successMessage.className = 'alert alert-success mt-3 animate__animated animate__fadeIn';
                        successMessage.innerHTML = '<i class="fas fa-check-circle me-2"></i> تم تحديث صلاحيات المستخدم بنجاح';
                        
                        const actionButtons = document.querySelector('.action-buttons');
                        actionButtons.parentNode.insertBefore(successMessage, actionButtons);
                        
                        // بعد ثانيتين إعادة الزر إلى حالته الأصلية
                        setTimeout(() => {
                            saveBtn.innerHTML = '<i class="fas fa-save me-2"></i> حفظ التغييرات';
                            saveBtn.disabled = false;
                            saveBtn.classList.remove('btn-success');
                            saveBtn.classList.add('btn-save');
                        }, 2000);
                        
                        // إخفاء رسالة النجاح بعد 3 ثواني
                        setTimeout(() => {
                            successMessage.classList.remove('animate__fadeIn');
                            successMessage.classList.add('animate__fadeOut');
                            setTimeout(() => {
                                successMessage.remove();
                            }, 500);
                        }, 3000);
                        
                        return response.text();
                    } else {
                        // حدث خطأ
                        throw new Error('فشل تحديث الصلاحيات');
                    }
                })
                .catch(error => {
                    // إعادة الزر إلى حالته الأصلية
                    saveBtn.innerHTML = '<i class="fas fa-save me-2"></i> حفظ التغييرات';
                    saveBtn.disabled = false;
                    
                    // عرض رسالة خطأ
                    const errorMessage = document.createElement('div');
                    errorMessage.className = 'alert alert-danger mt-3 animate__animated animate__fadeIn';
                    errorMessage.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i> ' + error.message;
                    
                    const actionButtons = document.querySelector('.action-buttons');
                    actionButtons.parentNode.insertBefore(errorMessage, actionButtons);
                    
                    // إخفاء رسالة الخطأ بعد 4 ثواني
                    setTimeout(() => {
                        errorMessage.classList.remove('animate__fadeIn');
                        errorMessage.classList.add('animate__fadeOut');
                        setTimeout(() => {
                            errorMessage.remove();
                        }, 500);
                    }, 4000);
                });
            });
        }
    });
</script>
{% endblock %}