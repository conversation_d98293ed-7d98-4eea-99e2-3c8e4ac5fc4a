{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }} - KamaVerse{% endblock %}

{% block extra_css %}
<style>
    /* Header styling to match main page */
    /* Header icon styling */
    .header-info h1.page-title i {
        color: #C89A3C;
    }
    
    .content-header {
        background: linear-gradient(135deg, var(--brand-red) 0%, var(--brand-red-dark) 100%);
        color: white;
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(214, 40, 40, 0.15);
    }
    
    .header-info h1.page-title {
        color: white;
        font-size: 36px;
        font-weight: 700;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .header-info .page-description {
        color: white;
        font-size: 20px;
        font-weight: 400;
        margin: 0;
        opacity: 0.9;
    }
    
    /* Original styles */
    .movements-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .movement-card {
        background: var(--white);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(26, 26, 26, 0.08);
        border: 1px solid var(--line);
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .movement-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(26, 26, 26, 0.12);
    }
    
    .movement-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 32px;
        color: white;
    }
    
    .receipt-card .movement-icon {
        background: linear-gradient(135deg, var(--brand-red), #FF6B6B);
    }
    
    .issue-card .movement-icon {
        background: linear-gradient(135deg, #FF8C42, #FF6B35);
    }
    
    .transfer-card .movement-icon {
        background: linear-gradient(135deg, var(--brand-gold), #FFD93D);
    }
    
    .movement-title {
        font-size: 20px;
        font-weight: 700;
        color: var(--ink);
        margin-bottom: 10px;
    }
    
    .movement-description {
        color: var(--gray-600);
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 20px;
    }
    
    .movement-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 20px;
        border-top: 1px solid var(--line);
    }
    
    .stat-item {
        text-align: center;
    }
    
    .stat-value {
        font-size: 18px;
        font-weight: bold;
        color: var(--brand-red);
    }
    
    .stat-label {
        font-size: 12px;
        color: var(--gray-600);
        margin-top: 5px;
    }
    
    .recent-movements {
        background: var(--white);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(26, 26, 26, 0.08);
        border: 1px solid var(--line);
        margin-top: 30px;
    }
    
    .section-title {
        color: var(--brand-red);
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .movements-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .movements-table th,
    .movements-table td {
        padding: 15px;
        text-align: right;
        border-bottom: 1px solid var(--line);
    }
    
    .movements-table th {
        background: var(--bg-light);
        font-weight: 600;
        color: var(--ink);
    }
    
    .movements-table tbody tr:hover {
        background: var(--bg-light);
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
    }
    
    .status-confirmed {
        background: #D4EDDA;
        color: #155724;
    }
    
    .status-draft {
        background: #FFF3CD;
        color: #856404;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--gray-600);
    }
    
    .empty-icon {
        font-size: 64px;
        color: var(--gray-400);
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="main-content">
    <div class="content-header">
        <div class="header-info">
            <h1 class="page-title">
                <i class="fas fa-exchange-alt"></i>
                {{ page_title }}
            </h1>
            <p class="page-description">إدارة جميع حركات المخزون (استلام، صرف، نقل)</p>
        </div>
    </div>

    <!-- بطاقات أنواع الحركات -->
    <div class="movements-grid">
        <!-- إذن الاستلام -->
        <div class="movement-card receipt-card" onclick="location.href='{% url 'inventory:goods_receipt_create' %}'">
            <div class="movement-icon">
                <i class="fas fa-arrow-down"></i>
            </div>
            <h3 class="movement-title">إذن الاستلام</h3>
            <p class="movement-description">
                تسجيل استلام البضائع والمواد الجديدة وإضافتها للمخزون
            </p>
            <div class="movement-stats">
                <div class="stat-item">
                    <div class="stat-value">{{ today_receipts }}</div>
                    <div class="stat-label">اليوم</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ month_receipts }}</div>
                    <div class="stat-label">هذا الشهر</div>
                </div>
            </div>
        </div>

        <!-- إذن الصرف -->
        <div class="movement-card issue-card" onclick="window.location.href='{% url 'inventory:goods_issue_dashboard' %}'">
            <div class="movement-icon">
                <i class="fas fa-arrow-up"></i>
            </div>
            <h3 class="movement-title">إذن الصرف</h3>
            <p class="movement-description">
                تسجيل صرف المواد من المخزون (بيع، استهلاك، تلف)
            </p>
            <div class="movement-stats">
                <div class="stat-item">
                    <div class="stat-value">{{ today_issues }}</div>
                    <div class="stat-label">اليوم</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ month_issues }}</div>
                    <div class="stat-label">هذا الشهر</div>
                </div>
            </div>
        </div>

        <!-- نقل المخزون -->
        <div class="movement-card transfer-card" onclick="location.href='{% url 'inventory:stock_transfer_list' %}'">
            <div class="movement-icon">
                <i class="fas fa-exchange-alt"></i>
            </div>
            <h3 class="movement-title">نقل المخزون</h3>
            <p class="movement-description">
                نقل المواد بين المخازن أو بين مواقع التخزين
            </p>
            <div class="movement-stats">
                <div class="stat-item">
                    <div class="stat-value">{{ today_transfers|default:0 }}</div>
                    <div class="stat-label">اليوم</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ month_transfers|default:0 }}</div>
                    <div class="stat-label">هذا الشهر</div>
                </div>
            </div>
        </div>


    </div>

    <!-- آخر الحركات -->
    <div class="recent-movements">
        <h2 class="section-title">
            <i class="fas fa-history"></i>
            آخر الحركات
        </h2>
        
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-clipboard-list"></i>
            </div>
            <h3>لا توجد حركات مخزون حتى الآن</h3>
            <p>ابدأ بإنشاء إذن استلام جديد لتسجيل أول حركة مخزون</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// يمكن إضافة JavaScript هنا لتحديث الإحصائيات
</script>
{% endblock %}
