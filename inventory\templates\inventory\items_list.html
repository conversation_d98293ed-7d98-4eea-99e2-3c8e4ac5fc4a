{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }} - <PERSON><PERSON>V<PERSON>{% endblock %}

{% block breadcrumb_items %}
    <li class="breadcrumb-item active">إدارة الأصناف</li>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/items.css' %}">
<style>
/* تحسينات الطباعة */
@media print {
    /* إخفاء العناصر غير المرغوب فيها */
    .navbar, .breadcrumb, .btn-group, .btn-add-item,
    .search-filters, .pagination, .footer, .sidebar {
        display: none !important;
    }

    /* تحسين العنوان */
    .items-header {
        background: white !important;
        color: #333 !important;
        box-shadow: none !important;
        border-bottom: 3px solid #D62828 !important;
        margin: 0 !important;
        padding: 1rem 0 !important;
    }

    .items-header h1 {
        color: #D62828 !important;
        font-size: 24px !important;
        text-align: center !important;
    }

    .items-header p {
        color: #666 !important;
        text-align: center !important;
        font-size: 14px !important;
    }

    /* تحسين البطاقات الإحصائية */
    .stats-cards {
        display: flex !important;
        justify-content: center !important;
        margin: 1rem 0 !important;
        page-break-inside: avoid !important;
    }

    .stat-card {
        border: 2px solid #D62828 !important;
        background: white !important;
        margin: 0 0.5rem !important;
        padding: 0.5rem !important;
        border-radius: 8px !important;
        text-align: center !important;
        min-width: 120px !important;
    }

    .stat-number {
        color: #D62828 !important;
        font-size: 20px !important;
        font-weight: bold !important;
    }

    .stat-label {
        color: #333 !important;
        font-size: 12px !important;
    }

    /* تحسين الجدول */
    .table-container {
        margin-top: 1rem !important;
    }

    .table {
        border-collapse: collapse !important;
        width: 100% !important;
        font-size: 11px !important;
    }

    .table th {
        background-color: #D62828 !important;
        color: white !important;
        border: 1px solid #333 !important;
        padding: 8px 4px !important;
        text-align: center !important;
        font-weight: bold !important;
    }

    .table td {
        border: 1px solid #666 !important;
        padding: 6px 4px !important;
        text-align: center !important;
        color: #333 !important;
    }

    .table tbody tr:nth-child(even) {
        background-color: #f8f9fa !important;
    }

    /* تحسين الألوان والخطوط */
    body {
        color: #333 !important;
        background: white !important;
        font-family: 'Arial', sans-serif !important;
    }

    /* إضافة معلومات الطباعة */
    .print-info {
        display: block !important;
        text-align: center !important;
        margin-top: 1rem !important;
        padding-top: 1rem !important;
        border-top: 1px solid #ccc !important;
        font-size: 10px !important;
        color: #666 !important;
    }

    /* تحسين فواصل الصفحات */
    .items-header, .stats-cards {
        page-break-after: avoid !important;
    }

    .table {
        page-break-inside: auto !important;
    }

    .table tr {
        page-break-inside: avoid !important;
        page-break-after: auto !important;
    }

    .table thead {
        display: table-header-group !important;
    }

    .table tfoot {
        display: table-footer-group !important;
    }
}

/* إخفاء معلومات الطباعة في العرض العادي */
.print-info {
    display: none;
}
</style>
{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="items-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-boxes me-3 gold-accent"></i>
                    إدارة الأصناف
                </h1>
                <p class="mb-0 opacity-75">إدارة وتتبع جميع أصناف المواد الكيميائية والبلاستيكية</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'inventory:items_export_excel' %}?{{ request.GET.urlencode }}" class="btn btn-outline-secondary export-btn" title="تصدير Excel">
                    <i class="fas fa-file-excel me-1"></i>
                    Excel
                </a>
                <button type="button" class="btn btn-outline-secondary" id="printBtn" title="طباعة">
                    <i class="fas fa-print me-1"></i>
                    طباعة
                </button>
                <a href="{% url 'inventory:item_add' %}" class="btn btn-add-item">
                    <i class="fas fa-plus me-2"></i>
                    إضافة صنف جديد
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="stats-cards">
    <div class="stat-card premium-card">
        <div class="stat-number">{{ total_items }}</div>
        <div class="stat-label">إجمالي الأصناف</div>
    </div>
    <div class="stat-card premium-card">
        <div class="stat-number">{{ categories_stats|length }}</div>
        <div class="stat-label">مجموعات المواد</div>
    </div>
    <div class="stat-card premium-card">
        <div class="stat-number">{{ material_types_stats|length }}</div>
        <div class="stat-label">أنواع المواد</div>
    </div>
    <div class="stat-card premium-card">
        <div class="stat-number">{{ page_obj.paginator.num_pages }}</div>
        <div class="stat-label">عدد الصفحات</div>
    </div>
</div>

<!-- Search and Filters -->
<div class="search-filters">
    <form method="GET" class="row g-3">
        <div class="col-md-4">
            <label class="form-label">البحث</label>
            <input type="text" class="form-control" name="search" value="{{ search_query }}" 
                   placeholder="البحث في الأصناف...">
        </div>
        <div class="col-md-2">
            <label class="form-label">مجموعة المواد</label>
            <select class="form-select" name="category">
                <option value="">جميع المجموعات</option>
                {% for value, label in category_choices %}
                    <option value="{{ value }}" {% if category_filter == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">نوع المادة</label>
            <select class="form-select" name="material_type">
                <option value="">جميع الأنواع</option>
                {% for value, label in material_type_choices %}
                    <option value="{{ value }}" {% if material_type_filter == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">مستوى الخطورة</label>
            <select class="form-select" name="hazard_level">
                <option value="">جميع المستويات</option>
                {% for value, label in hazard_level_choices %}
                    <option value="{{ value }}" {% if hazard_level_filter == value %}selected{% endif %}>
                        {{ label }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-2">
            <label class="form-label">الحالة</label>
            <select class="form-select" name="status">
                <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                <option value="all" {% if status_filter == 'all' %}selected{% endif %}>الكل</option>
            </select>
        </div>
        <div class="col-md-12">
            <button type="submit" class="btn" style="background: var(--brand-red); color: var(--white); border: none; border-radius: 8px; padding: 0.75rem 1.5rem; font-weight: 600;">
                <i class="fas fa-search me-2"></i>بحث
            </button>
            <a href="{% url 'inventory:items_list' %}" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-times me-2"></i>مسح الفلاتر
            </a>
        </div>
    </form>
</div>

<!-- Items Table -->
<div class="items-table">
    {% if page_obj %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>كود الصنف</th>
                        <th>اسم الصنف</th>
                        <th>مجموعة المواد</th>
                        <th>نوع المادة</th>
                        <th>الكمية</th>
                        <th>الوحدة</th>
                        <th>مستوى الخطورة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in page_obj %}
                        <tr>
                            <td>
                                <span class="item-code">{{ item.item_code }}</span>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ item.item_name_ar }}</strong>
                                    {% if item.item_name_en %}
                                        <br><small class="text-muted">{{ item.item_name_en }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-category bg-info">
                                    {{ item.get_category_display }}
                                </span>
                            </td>
                            <td>{{ item.get_material_type_display }}</td>
                            <td>
                                <span class="quantity-badge">{{ item.max_stock_level|floatformat:0 }}</span>
                            </td>
                            <td>{{ item.get_unit_of_measure_display }}</td>
                            <td>
                                <span class="badge badge-hazard-{{ item.hazard_level|lower }}">
                                    {{ item.get_hazard_level_display }}
                                </span>
                            </td>
                            <td>
                                {% if item.is_active %}
                                    <span class="status-active">نشط</span>
                                {% else %}
                                    <span class="status-inactive">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{% url 'inventory:item_detail' item.pk %}" class="btn btn-outline-primary" data-bs-toggle="tooltip" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'inventory:item_delete' item.pk %}" class="btn btn-outline-danger" data-bs-toggle="tooltip" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
            <div class="d-flex justify-content-between align-items-center p-3">
                <div class="text-muted">
                    عرض {{ page_obj.start_index }} إلى {{ page_obj.end_index }} من {{ page_obj.paginator.count }} صنف
                </div>
                <nav>
                    <ul class="pagination mb-0">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">السابق</a>
                            </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">التالي</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        {% endif %}
    {% else %}
        <div class="text-center p-5">
            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد أصناف</h4>
            <p class="text-muted">لم يتم العثور على أصناف تطابق معايير البحث</p>
            <a href="{% url 'inventory:items_list' %}" class="btn btn-primary">عرض جميع الأصناف</a>
        </div>
    {% endif %}
</div>
<!-- معلومات الطباعة -->
<div class="print-info">
    <p>تم طباعة هذا التقرير من نظام KamaVerse لإدارة المخزون</p>
    <p>تاريخ الطباعة: <span id="printDate"></span> | الوقت: <span id="printTime"></span></p>
    <p>المستخدم: {{ user.get_full_name|default:user.username }}</p>
</div>

{% endblock %}

{% block extra_js %}
<script src="{% static 'js/items.js' %}"></script>
{% endblock %}
