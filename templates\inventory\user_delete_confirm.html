{% extends 'base.html' %}
{% load static %}

{% block title %}حذف مستخدم - KamaVerse{% endblock %}

{% block extra_css %}
<style>
    .page-header {
        background: linear-gradient(135deg, var(--brand-red), #FF6B6B);
        color: var(--white);
        padding: 2rem;
        border-radius: 20px;
        margin-bottom: 2rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .page-header h1 {
        font-weight: 700;
        margin: 0;
        color: var(--white);
    }

    .confirm-section {
        background-color: var(--white);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        border: 1px solid var(--line);
        text-align: center;
    }

    .alert-icon {
        font-size: 4rem;
        color: var(--brand-red);
        margin-bottom: 1.5rem;
    }

    .user-info {
        background-color: rgba(214, 40, 40, 0.05);
        padding: 1.5rem;
        border-radius: 15px;
        margin: 2rem 0;
        text-align: right;
    }

    .user-info h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        color: var(--brand-red);
    }

    .action-buttons {
        margin-top: 2rem;
        display: flex;
        justify-content: center;
        gap: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="page-header">
        <h1>
            <i class="fas fa-user-minus me-2"></i>
            حذف مستخدم
        </h1>
    </div>

    <!-- قسم تأكيد الحذف -->
    <div class="confirm-section">
        <div class="alert-icon">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        
        <h2>هل أنت متأكد من حذف هذا المستخدم؟</h2>
        <p class="text-muted">هذا الإجراء لا يمكن التراجع عنه. سيتم حذف المستخدم وجميع بياناته وصلاحياته نهائياً.</p>
        
        <div class="user-info">
            <h3>بيانات المستخدم</h3>
            <p><strong>اسم المستخدم:</strong> {{ user_profile.user.username }}</p>
            <p><strong>الاسم الكامل:</strong> {{ user_profile.user.get_full_name|default:"غير محدد" }}</p>
            <p><strong>الدور:</strong> {{ user_profile.get_role_display }}</p>
            <p><strong>القسم:</strong> {{ user_profile.department|default:"غير محدد" }}</p>
            <p><strong>المخزن المخصص:</strong> {{ user_profile.assigned_warehouse.warehouse_name|default:"جميع المخازن" }}</p>
        </div>
        
        <form method="post" action="{% url 'inventory:delete_user' user_id=user_profile.id %}">
            {% csrf_token %}
            
            <div class="action-buttons">
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash me-2"></i>
                    تأكيد الحذف
                </button>
                
                <a href="{% url 'inventory:users_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times me-2"></i>
                    إلغاء
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}