{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/items.css' %}">
<style>
    .warehouse-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        padding: 25px;
        margin-bottom: 25px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: 2px solid #f0f0f0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .warehouse-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border-color: #D62828;
    }

    .warehouse-code-badge {
        position: absolute;
        top: 15px;
        left: 15px;
        background: #C89A3C;
        color: white;
        padding: 8px 15px;
        border-radius: 25px;
        font-size: 0.85rem;
        font-weight: bold;
        letter-spacing: 1px;
    }

    .warehouse-title {
        color: #D62828;
        font-size: 1.6rem;
        font-weight: bold;
        margin: 30px 0 20px 0;
        text-align: center;
    }

    .warehouse-stats {
        display: flex;
        justify-content: space-around;
        margin: 20px 0;
        padding: 15px 0;
        border-top: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }

    .stat-item {
        text-align: center;
        flex: 1;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        display: block;
        margin-bottom: 5px;
    }

    .stat-number.occupied {
        color: #D62828;
    }

    .stat-number.total {
        color: #D62828;
    }

    .stat-label {
        font-size: 0.9rem;
        color: #666;
        font-weight: 500;
    }

    .items-count-section {
        margin: 25px 0;
        text-align: center;
    }

    .occupancy-section {
        margin: 25px 0;
        text-align: center;
    }

    .section-label {
        font-size: 0.95rem;
        color: #666;
        font-weight: 500;
        margin-bottom: 8px;
        display: block;
    }

    .section-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 15px;
        display: block;
    }

    .items-number {
        color: #D62828;
    }

    .occupancy-number {
        color: #333;
    }

    .occupancy-bar {
        width: 100%;
        height: 8px;
        background: #f0f0f0;
        border-radius: 4px;
        overflow: hidden;
        margin-bottom: 15px;
    }

    .occupancy-fill {
        height: 100%;
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .occupancy-fill.low {
        background: linear-gradient(90deg, #28a745, #20c997);
    }

    .occupancy-fill.medium {
        background: linear-gradient(90deg, #ffc107, #fd7e14);
    }

    .occupancy-fill.high {
        background: linear-gradient(90deg, #dc3545, #e74c3c);
    }

    .warehouse-actions {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 20px;
    }

    .btn-details {
        background: #D62828;
        color: white;
        padding: 12px 25px;
        border: none;
        border-radius: 25px;
        font-weight: bold;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-details:hover {
        background: #b71c1c;
        color: white;
        transform: translateY(-2px);
    }

    .btn-locations {
        background: #C89A3C;
        color: white;
        padding: 12px 25px;
        border: none;
        border-radius: 25px;
        font-weight: bold;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .btn-locations:hover {
        background: #b8941f;
        color: white;
        transform: translateY(-2px);
    }

    .warehouse-status {
        position: absolute;
        bottom: 15px;
        right: 15px;
        font-size: 0.8rem;
        padding: 4px 10px;
        border-radius: 12px;
        font-weight: bold;
    }

    .status-active {
        background: #d4edda;
        color: #155724;
    }

    .status-inactive {
        background: #f8d7da;
        color: #721c24;
    }
    

    
    .search-filters {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .filters-row {
        display: grid;
        grid-template-columns: 1fr auto auto;
        gap: 15px;
        align-items: end;
    }

    .form-group {
        margin-bottom: 0;
    }

    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 10px 15px;
        font-size: 1rem;
    }

    .form-control:focus {
        border-color: #D62828;
        box-shadow: 0 0 0 0.2rem rgba(214, 40, 40, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<!-- Header Section -->
<div class="items-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-warehouse me-3 gold-accent"></i>
                    إدارة المخازن
                </h1>
                <p class="mb-0 opacity-75">إدارة وعرض جميع المخازن ومواقع التخزين</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex gap-2 align-items-center justify-content-end">
                    <span class="badge bg-primary fs-6">
                        إجمالي المخازن: {{ total_warehouses }}
                    </span>
                    <a href="{% url 'inventory:warehouse_create' %}" class="btn btn-add-item">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء مخزن جديد
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>



<div class="container-fluid">



    <!-- Warehouses List -->
    <div class="row">
        {% for item in page_obj %}
        <div class="col-lg-6 col-xl-4">
            <div class="warehouse-card">
                <!-- Warehouse Code Badge -->
                <div class="warehouse-code-badge">{{ item.warehouse.warehouse_code }}</div>

                <!-- Warehouse Title -->
                <h3 class="warehouse-title">{{ item.warehouse.warehouse_name }}</h3>

                <!-- Items Count Section -->
                <div class="items-count-section">
                    <div class="section-label">عدد الأصناف</div>
                    <div class="section-number items-number">{{ item.unique_items_count }}</div>
                </div>

                <!-- Occupancy Section -->
                <div class="occupancy-section">
                    <div class="section-label">نسبة الإشغال</div>
                    <div class="section-number occupancy-number">{{ item.occupancy_percentage|floatformat:1 }}%</div>

                    <div class="occupancy-bar">
                        <div class="occupancy-fill {% if item.occupancy_percentage <= 30 %}low{% elif item.occupancy_percentage <= 70 %}medium{% else %}high{% endif %}"
                             style="width: {{ item.occupancy_percentage }}%"></div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="warehouse-actions">
                    <a href="{% url 'inventory:warehouse_detail' item.warehouse.pk %}" class="btn-details">
                        <i class="fas fa-info-circle"></i>
                        تفاصيل
                    </a>
                </div>

                <!-- Status Badge -->
                <div class="warehouse-status {% if item.warehouse.is_active %}status-active{% else %}status-inactive{% endif %}">
                    {% if item.warehouse.is_active %}نشط{% else %}غير نشط{% endif %}
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-warehouse fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد مخازن</h4>
                <p class="text-muted">لم يتم العثور على أي مخازن تطابق معايير البحث</p>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <nav aria-label="تنقل الصفحات" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">السابق</a>
                </li>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <li class="page-item active">
                        <span class="page-link">{{ num }}</span>
                    </li>
                {% else %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">التالي</a>
                </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}
