"""
Core Signals for KamaVerse
إشارات النظام الأساسية لتسجيل العمليات
"""

from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.contrib.auth.signals import user_logged_in, user_logged_out
from django.utils import timezone
from .models import KeyAuditLog, KeyUser
import threading

# Thread local storage for request data
_thread_locals = threading.local()


def get_current_request():
    """الحصول على الطلب الحالي من thread local storage"""
    return getattr(_thread_locals, 'request', None)


def set_current_request(request):
    """تعيين الطلب الحالي في thread local storage"""
    _thread_locals.request = request


def get_client_ip(request):
    """الحصول على عنوان IP للعميل"""
    if not request:
        return '127.0.0.1'
    
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
    return ip


@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    """تسجيل عملية تسجيل الدخول"""
    try:
        KeyAuditLog.log_operation(
            user=user,
            operation_type='LOGIN',
            module='SYSTEM',
            description=f'تسجيل دخول المستخدم {user.arabic_name}',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            success=True
        )
        
        # Update user's last login IP
        if hasattr(user, 'last_login_ip'):
            user.last_login_ip = get_client_ip(request)
            user.reset_failed_login_attempts()
            user.save(update_fields=['last_login_ip', 'failed_login_attempts'])
            
    except Exception as e:
        # Don't break login process if logging fails
        print(f"Error logging user login: {e}")


@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    """تسجيل عملية تسجيل الخروج"""
    try:
        if user:
            KeyAuditLog.log_operation(
                user=user,
                operation_type='LOGOUT',
                module='SYSTEM',
                description=f'تسجيل خروج المستخدم {user.arabic_name}',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                success=True
            )
    except Exception as e:
        print(f"Error logging user logout: {e}")


@receiver(post_save, sender=KeyUser)
def log_user_changes(sender, instance, created, **kwargs):
    """تسجيل تغييرات المستخدمين"""
    try:
        request = get_current_request()
        current_user = getattr(request, 'user', None) if request else None
        
        if created:
            KeyAuditLog.log_operation(
                user=current_user,
                operation_type='CREATE',
                module='USERS_MODULE',
                table_name='KeyUser',
                record_id=instance.id,
                description=f'إنشاء مستخدم جديد: {instance.arabic_name}',
                new_data={
                    'username': instance.username,
                    'arabic_name': instance.arabic_name,
                    'employee_id': instance.employee_id,
                    'department': instance.department,
                    'user_level': instance.user_level,
                },
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
            )
        else:
            KeyAuditLog.log_operation(
                user=current_user,
                operation_type='UPDATE',
                module='USERS_MODULE',
                table_name='KeyUser',
                record_id=instance.id,
                description=f'تحديث بيانات المستخدم: {instance.arabic_name}',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
            )
    except Exception as e:
        print(f"Error logging user changes: {e}")


@receiver(post_delete, sender=KeyUser)
def log_user_deletion(sender, instance, **kwargs):
    """تسجيل حذف المستخدمين"""
    try:
        request = get_current_request()
        current_user = getattr(request, 'user', None) if request else None
        
        KeyAuditLog.log_operation(
            user=current_user,
            operation_type='DELETE',
            module='USERS_MODULE',
            table_name='KeyUser',
            record_id=instance.id,
            description=f'حذف المستخدم: {instance.arabic_name}',
            old_data={
                'username': instance.username,
                'arabic_name': instance.arabic_name,
                'employee_id': instance.employee_id,
                'department': instance.department,
                'user_level': instance.user_level,
            },
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
        )
    except Exception as e:
        print(f"Error logging user deletion: {e}")


# Middleware class to capture request in thread local
class AuditLogMiddleware:
    """
    Middleware لحفظ معلومات الطلب في thread local storage
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        set_current_request(request)
        response = self.get_response(request)
        set_current_request(None)
        return response
