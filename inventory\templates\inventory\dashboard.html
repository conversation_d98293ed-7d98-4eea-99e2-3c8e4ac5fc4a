{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - KamaVerse</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{% static 'css/dashboard-new.css' %}" rel="stylesheet">
    <link href="{% static 'css/items.css' %}" rel="stylesheet">
    <style>
        .chart-container {
            position: relative;
            height: 350px;
            padding: 20px;
        }

        .activity-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(26, 26, 26, 0.08);
            border: 1px solid #f0f0f0;
        }

        .activity-section h3 {
            color: #D62828;
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .activity-subtitle {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .chart-container {
                height: 300px;
                padding: 10px;
            }
        }

        /* Top Items Ranking Styles */
        .performer-avatar.rank-1,
        .performer-avatar.rank-2,
        .performer-avatar.rank-3 {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-weight: bold;
            color: white;
            font-size: 16px;
        }

        .performer-avatar.rank-1 {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
        }

        .performer-avatar.rank-2 {
            background: linear-gradient(135deg, #C0C0C0, #A8A8A8);
            box-shadow: 0 4px 15px rgba(192, 192, 192, 0.3);
        }

        .performer-avatar.rank-3 {
            background: linear-gradient(135deg, #CD7F32, #B8860B);
            box-shadow: 0 4px 15px rgba(205, 127, 50, 0.3);
        }

        .rank-number {
            font-size: 18px;
            font-weight: 800;
        }

        .quantity-display {
            text-align: right;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        }

        .quantity-number {
            font-size: 18px;
            font-weight: bold;
            color: #D62828;
            line-height: 1;
        }

        .quantity-unit {
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .performer-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }

        .performer-item:hover {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px 10px;
        }

        .performer-item:last-child {
            border-bottom: none;
        }

        .performer-info {
            flex: 1;
            margin: 0 15px;
        }

        .performer-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .performer-handle {
            color: #666;
            font-size: 13px;
        }

        /* Improved Stats Cards */
        .stats-row-improved {
            display: flex;
            justify-content: space-between;
            align-items: stretch;
            gap: 30px;
            margin: 0 0 30px 0;
            width: 100%;
            padding: 0;
        }

        .stat-card-improved {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            flex: 1;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .stat-card-improved:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }

        .stat-card-improved:hover .stat-number-improved {
            transform: scale(1.1);
        }

        .stat-card-improved:hover .stat-icon-improved {
            transform: scale(1.1) rotate(5deg);
        }

        .stat-card-improved.items-card {
            border-left: 5px solid #007bff;
        }

        .stat-card-improved.items-card:hover {
            border-left: 5px solid #4285f4;
            background: linear-gradient(135deg, #ffffff 0%, #f0f7ff 100%);
        }

        .stat-card-improved.warehouses-card {
            border-left: 5px solid #dc3545;
        }

        .stat-card-improved.warehouses-card:hover {
            border-left: 5px solid #ea4335;
            background: linear-gradient(135deg, #ffffff 0%, #fff0f0 100%);
        }

        .stat-icon-improved {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px auto;
            font-size: 32px;
            color: white;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .stat-icon-improved::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
            transform: rotate(45deg);
            transition: all 0.3s ease;
        }

        .stat-card-improved:hover .stat-icon-improved::before {
            transform: rotate(45deg) translate(50%, 50%);
        }

        .items-card .stat-icon-improved {
            background: linear-gradient(135deg, #4285f4 0%, #1a73e8 100%);
        }

        .warehouses-card .stat-icon-improved {
            background: linear-gradient(135deg, #ea4335 0%, #d33b2c 100%);
        }

        .stat-content-improved {
            position: relative;
            z-index: 2;
            text-align: center;
        }

        .stat-number-improved {
            font-size: 48px;
            font-weight: 700;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 12px;
            line-height: 1;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .stat-label-improved {
            font-size: 20px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }

        .stat-description {
            font-size: 14px;
            color: #6c757d;
            font-weight: 400;
            line-height: 1.5;
            opacity: 0.8;
            margin-top: 8px;
            padding: 8px 12px;
            background: rgba(108, 117, 125, 0.05);
            border-radius: 8px;
            border-left: 3px solid rgba(108, 117, 125, 0.2);
        }

        .stat-decoration {
            position: absolute;
            top: -20px;
            right: -20px;
            opacity: 0.1;
        }

        .decoration-circle {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, #007bff, #dc3545);
            position: absolute;
        }

        .decoration-circle.small {
            width: 60px;
            height: 60px;
            top: 40px;
            right: 40px;
        }

        @media (max-width: 768px) {
            .stats-row-improved {
                flex-direction: column;
                gap: 20px;
                padding: 0;
            }

            .stat-card-improved {
                padding: 20px;
                flex: none;
                width: 100%;
                min-height: 120px;
            }

            .stat-number-improved {
                font-size: 28px;
            }

            .stat-label-improved {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-wrapper">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="{% static 'images/kamaverse-logo.png' %}" alt="KamaVerse Logo" class="logo-img">
                    <span>KamaVerse</span>
                </div>
            </div>

            <div class="user-profile">
                <div class="user-avatar">
                    <img src="{% static 'images/user-avatar.jpg' %}" alt="User" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNFNUU3RUIiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDEyQzE0LjIwOTEgMTIgMTYgMTAuMjA5MSAxNiA4QzE2IDUuNzkwODYgMTQuMjA5MSA0IDEyIDRDOS43OTA4NiA0IDggNS43OTA4NiA4IDhDOCAxMC4yMDkxIDkuNzkwODYgMTIgMTIgMTJaIiBmaWxsPSIjOUM5Qzk5Ii8+CjxwYXRoIGQ9Ik0xMiAxNEM5LjMzIDEzLjk5IDcuMDEgMTUuNjIgNiAxOEMxMC4wMSAyMCAxMy45OSAyMCAxOCAxOEMxNi45OSAxNS42MiAxNC42NyAxMy45OSAxMiAxNHoiIGZpbGw9IiM5QzlCNTkiLz4KPC9zdmc+Cjwvc3ZnPgo='">
                </div>
                <div class="user-info">
                    {% if user.is_authenticated %}
                        <h4>{{ user.get_full_name|default:user.username }}</h4>
                        <p>{{ user.userprofile.get_role_display|default:"مستخدم النظام" }}</p>
                    {% else %}
                        <h4>زائر</h4>
                        <p>غير مسجل</p>
                    {% endif %}
                </div>
            </div>

            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="#"><i class="fas fa-home"></i> الصفحة الرئيسية</a>
                    </li>
                    <li>
                        <a href="{% url 'inventory:items_list' %}"><i class="fas fa-boxes"></i> إدارة الأصناف</a>
                    </li>
                    <li>
                        <a href="{% url 'inventory:warehouses_list' %}"><i class="fas fa-warehouse"></i> إدارة المخازن</a>
                    </li>
                    <li>
                        <a href="{% url 'inventory:movements_list' %}"><i class="fas fa-exchange-alt"></i> حركات المخزون</a>
                    </li>
                    {% if user.username == 'admin' %}
                    <li>
                        <a href="{% url 'inventory:users_list' %}"><i class="fas fa-users"></i> إدارة المستخدمين</a>
                    </li>
                    {% endif %}
                    {% if user.username == 'admin' or user.username == 'manager' %}
                    <li>
                        <a href="{% url 'inventory:reports_main_dashboard' %}"><i class="fas fa-chart-bar"></i> التقارير</a>
                    </li>
                    {% endif %}
                    <li>
                        <a href="#"><i class="fas fa-bell"></i> التنبيهات</a>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                {% if user.is_authenticated %}
                    <a href="{% url 'inventory:logout' %}" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </a>
                {% else %}
                    <a href="{% url 'inventory:login' %}" class="logout-btn">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </a>
                {% endif %}
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Spacer -->
            <div style="height: 40px;"></div>

            <!-- Header Section -->
            <div class="items-header">
                <div class="container">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="mb-2">
                                <i class="fas fa-home me-3 gold-accent"></i>
                                الصفحة الرئيسية
                            </h1>
                            <p class="mb-0 opacity-75">لوحة التحكم الرئيسية لنظام إدارة المخزون</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-card">

                <!-- Stats Cards -->
                <div class="stats-row-improved">
                    <div class="stat-card-improved items-card" onclick="window.location.href='{% url 'inventory:items_list' %}'" style="cursor: pointer;">
                        <div class="stat-icon-improved">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="stat-content-improved">
                            <div class="stat-number-improved">{{ total_items }}</div>
                            <div class="stat-label-improved">إجمالي الأصناف</div>
                            <div class="stat-description">عدد الأصناف في النظام</div>
                        </div>
                        <div class="stat-decoration">
                            <div class="decoration-circle"></div>
                            <div class="decoration-circle small"></div>
                        </div>
                    </div>

                    <div class="stat-card-improved warehouses-card">
                        <div class="stat-icon-improved">
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="stat-content-improved">
                            <div class="stat-number-improved">{{ total_warehouses }}</div>
                            <div class="stat-label-improved">إجمالي المخازن</div>
                            <div class="stat-description">الإسكندرية، الدائري، القاهرة، المنوفية</div>
                        </div>
                        <div class="stat-decoration">
                            <div class="decoration-circle"></div>
                            <div class="decoration-circle small"></div>
                        </div>
                    </div>
                </div>

                <!-- Content Row -->
                <div class="content-row">
                    <!-- Activity Section -->
                    <div class="activity-section">
                        <h3>حركات المخزون الشهرية</h3>
                        <p class="activity-subtitle">آخر 12 شهر • الشهر الحالي: {{ current_month_total }} حركة</p>
                        <div class="chart-container">
                            <canvas id="movementsChart"></canvas>
                        </div>
                    </div>

                    <!-- Top Items by Quantity -->
                    <div class="top-performers">
                        <h3>أكثر 3 منتجات في المخازن</h3>
                        <div class="performer-list">
                            {% for item in top_items_by_quantity %}
                            <div class="performer-item">
                                <div class="performer-avatar rank-{{ forloop.counter }}">
                                    <span class="rank-number">{{ forloop.counter }}</span>
                                </div>
                                <div class="performer-info">
                                    <div class="performer-name">{{ item.item__item_name_ar }}</div>
                                    <div class="performer-handle">{{ item.item__item_code }}</div>
                                </div>
                                <div class="performer-score">
                                    <div class="quantity-display">
                                        <span class="quantity-number">{{ item.total_quantity|floatformat:0 }}</span>
                                        <span class="quantity-unit">{{ item.item__unit_of_measure }}</span>
                                    </div>
                                </div>
                            </div>
                            {% empty %}
                            <div class="text-center text-muted">
                                <i class="fas fa-box-open fa-2x mb-2"></i>
                                <p>لا توجد أصناف في المخزون</p>
                            </div>
                            {% endfor %}
                        </div>
                        <a href="{% url 'inventory:items_list' %}" class="view-more">عرض جميع الأصناف ←</a>
                    </div>
                </div>

                <!-- Channels Section -->
                <div class="channels-section">
                    <h3>حالة المخازن</h3>
                    <p class="channels-subtitle">إحصائيات المخازن لفترة أسبوع واحد</p>

                    <div class="channels-grid">
                        {% for warehouse_stat in warehouses_stats %}
                        <div class="channel-card {% if forloop.counter0 == 0 %}dribbble{% elif forloop.counter0 == 1 %}behance{% elif forloop.counter0 == 2 %}instagram{% else %}pinterest{% endif %}">
                            <div class="channel-icon">
                                <i class="fas fa-warehouse"></i>
                            </div>
                            <div class="channel-info">
                                <div class="channel-name">{{ warehouse_stat.warehouse.warehouse_name }}</div>
                                <div class="channel-handle">نسبة الامتلاء</div>
                                <div class="channel-change">{{ warehouse_stat.occupancy_percentage|floatformat:0 }}%</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Monthly Movements Chart
        const movementsData = JSON.parse('{{ monthly_movements|escapejs }}');

        const ctx = document.getElementById('movementsChart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: movementsData.map(item => item.month),
                datasets: [
                    {
                        label: 'أذون الاستلام',
                        data: movementsData.map(item => item.receipts),
                        backgroundColor: '#28a745',
                        borderColor: '#28a745',
                        borderWidth: 1,
                        borderRadius: 4,
                        borderSkipped: false,
                    },
                    {
                        label: 'أذون الصرف',
                        data: movementsData.map(item => item.issues),
                        backgroundColor: '#dc3545',
                        borderColor: '#dc3545',
                        borderWidth: 1,
                        borderRadius: 4,
                        borderSkipped: false,
                    },
                    {
                        label: 'عمليات النقل',
                        data: movementsData.map(item => item.transfers),
                        backgroundColor: '#C89A3C',
                        borderColor: '#C89A3C',
                        borderWidth: 1,
                        borderRadius: 4,
                        borderSkipped: false,
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20,
                            font: {
                                family: 'Tajawal',
                                size: 16
                            }
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                return `${context.dataset.label}: ${context.parsed.y} حركة`;
                            },
                            footer: function(context) {
                                const monthData = movementsData[context[0].dataIndex];
                                return `الإجمالي: ${monthData.total} حركة`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        stacked: false,
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                family: 'Tajawal',
                                size: 14
                            },
                            maxRotation: 45
                        }
                    },
                    y: {
                        stacked: false,
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        },
                        ticks: {
                            font: {
                                family: 'Tajawal',
                                size: 14
                            },
                            callback: function(value) {
                                return value + ' حركة';
                            }
                        }
                    }
                },
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                elements: {
                    bar: {
                        borderWidth: 1,
                    }
                }
            }
        });
    </script>
</body>

<!-- قسم المدير (مرئي فقط للمستخدم admin) -->
{% if is_admin %}
<div class="row mt-4">
    <div class="col-12">
        <div class="activity-section" style="border-left: 5px solid #FFC107;">
            <h3>
                <i class="fas fa-user-shield me-2" style="color: #FFC107;"></i>
                لوحة تحكم مدير النظام
            </h3>
            <p class="activity-subtitle">أدوات مدير النظام الحصرية</p>
            
            <div class="row">
                <div class="col-md-4 mb-3">
                    <a href="{% url 'inventory:users_list' %}" class="btn btn-warning btn-lg w-100">
                        <i class="fas fa-users-cog me-2"></i>
                        إدارة المستخدمين
                    </a>
                </div>
            </div>
            
            <div class="alert alert-warning mt-3">
                <i class="fas fa-info-circle me-2"></i>
                أنت الوحيد الذي يمكنه الوصول إلى هذه الصفحة وإدارة المستخدمين في النظام.
            </div>
        </div>
    </div>
</div>
{% endif %}

</html>
