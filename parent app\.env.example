# KamaVerse Environment Variables Example
# نسخ هذا الملف إلى .env وتعديل القيم حسب البيئة

# Django Settings
DJANGO_ENVIRONMENT=development
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Settings (Development - SQLite)
DB_ENGINE=django.db.backends.sqlite3
DB_NAME=db.sqlite3

# Database Settings (Production - PostgreSQL)
# DB_ENGINE=django.db.backends.postgresql
# DB_NAME=kamaverse_prod
# DB_USER=kamaverse_user
# DB_PASSWORD=your-db-password
# DB_HOST=localhost
# DB_PORT=5432

# Redis Settings (for Channels and Celery)
REDIS_URL=redis://127.0.0.1:6379/0

# Email Settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password
DEFAULT_FROM_EMAIL=<EMAIL>

# AWS S3 Settings (for file storage in production)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=kamaverse-storage
AWS_S3_REGION_NAME=us-east-1

# Celery Settings
CELERY_BROKER_URL=redis://127.0.0.1:6379/0
CELERY_RESULT_BACKEND=redis://127.0.0.1:6379/0

# CORS Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Security Settings (Production)
SECURE_SSL_REDIRECT=False
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False

# Logging
LOG_LEVEL=DEBUG

# KamaVerse Specific Settings
COMPANY_NAME=شركة القماش للاستيراد والتصدير
SYSTEM_NAME=KamaVerse
SUPPORT_EMAIL=<EMAIL>
MAX_LOGIN_ATTEMPTS=3
SESSION_TIMEOUT_MINUTES=480

# External APIs (if needed)
# EXTERNAL_API_KEY=your-api-key
# EXTERNAL_API_URL=https://api.example.com

# Monitoring (Production)
# SENTRY_DSN=your-sentry-dsn

# Backup Settings
# BACKUP_ENABLED=True
# BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
