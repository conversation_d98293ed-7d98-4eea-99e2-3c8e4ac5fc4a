{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }} - KamaVerse{% endblock %}

{% block extra_css %}
<style>
    .receipt-detail {
        background: var(--white);
        border-radius: 20px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(26, 26, 26, 0.08);
        border: 1px solid var(--line);
        margin-bottom: 30px;
    }
    
    .receipt-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid var(--line);
    }
    
    .receipt-info h2 {
        color: var(--brand-red);
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 10px;
    }
    
    .receipt-meta {
        color: var(--gray-600);
        font-size: 14px;
    }
    
    .status-badge {
        padding: 8px 20px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 14px;
    }
    
    .status-confirmed {
        background: #D4EDDA;
        color: #155724;
    }
    
    .status-draft {
        background: #FFF3CD;
        color: #856404;
    }
    
    .receipt-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 30px;
        margin-bottom: 30px;
    }
    
    .detail-group {
        background: var(--bg-light);
        padding: 20px;
        border-radius: 15px;
        border: 1px solid var(--line);
    }
    
    .detail-title {
        color: var(--brand-red);
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .detail-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding: 8px 0;
        border-bottom: 1px solid var(--line);
    }
    
    .detail-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .detail-label {
        color: var(--gray-600);
        font-weight: 500;
    }
    
    .detail-value {
        color: var(--ink);
        font-weight: 600;
    }
    
    .items-section {
        margin-top: 30px;
    }
    
    .section-title {
        color: var(--brand-red);
        font-size: 20px;
        font-weight: 700;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .items-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }
    
    .items-table th,
    .items-table td {
        padding: 15px;
        text-align: right;
        border: 1px solid var(--line);
    }
    
    .items-table th {
        background: var(--brand-red);
        color: white;
        font-weight: 600;
    }
    
    .items-table tbody tr:nth-child(even) {
        background: var(--bg-light);
    }
    
    .totals-section {
        background: var(--brand-red);
        color: white;
        padding: 20px;
        border-radius: 15px;
        margin-top: 20px;
    }
    
    .totals-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }
    
    .total-item {
        text-align: center;
    }
    
    .total-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .total-label {
        font-size: 14px;
        opacity: 0.9;
    }
    
    .actions-section {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin-top: 30px;
    }
    
    .btn {
        padding: 12px 25px;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        border: none;
        font-size: 14px;
    }
    
    .btn-primary {
        background: var(--brand-red);
        color: white;
    }
    
    .btn-secondary {
        background: var(--brand-gold);
        color: white;
    }
    
    .btn-outline {
        background: transparent;
        color: var(--gray-600);
        border: 1px solid var(--line);
    }
    
    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="main-content">
    <div class="content-header">
        <div class="header-info">
            <h1 class="page-title">
                <i class="fas fa-receipt"></i>
                {{ page_title }}
            </h1>
            <p class="page-description">تفاصيل إذن الاستلام</p>
        </div>
    </div>

    <div class="receipt-detail">
        <!-- رأس الإذن -->
        <div class="receipt-header">
            <div class="receipt-info">
                <h2>{{ receipt.receipt_number }}</h2>
                <div class="receipt-meta">
                    <i class="fas fa-calendar"></i>
                    {{ receipt.receipt_date|date:"Y-m-d H:i" }}
                    •
                    <i class="fas fa-user"></i>
                    {{ receipt.created_by.get_full_name }}
                </div>
            </div>
            <div class="receipt-status">
                <span class="status-badge status-{{ receipt.status|lower }}">
                    {{ receipt.get_status_display }}
                </span>
            </div>
        </div>

        <!-- تفاصيل الإذن -->
        <div class="receipt-details">
            <div class="detail-group">
                <h3 class="detail-title">
                    <i class="fas fa-warehouse"></i>
                    معلومات المخزن
                </h3>
                <div class="detail-item">
                    <span class="detail-label">اسم المخزن:</span>
                    <span class="detail-value">{{ receipt.warehouse.warehouse_name }}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">كود المخزن:</span>
                    <span class="detail-value">{{ receipt.warehouse.warehouse_code }}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">الموقع:</span>
                    <span class="detail-value">{{ receipt.warehouse.location }}</span>
                </div>
            </div>

            <div class="detail-group">
                <h3 class="detail-title">
                    <i class="fas fa-info-circle"></i>
                    معلومات الإذن
                </h3>
                <div class="detail-item">
                    <span class="detail-label">تاريخ الإنشاء:</span>
                    <span class="detail-value">{{ receipt.created_at|date:"Y-m-d H:i" }}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">آخر تحديث:</span>
                    <span class="detail-value">{{ receipt.updated_at|date:"Y-m-d H:i" }}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">المسؤول:</span>
                    <span class="detail-value">{{ receipt.created_by.get_full_name }}</span>
                </div>
            </div>
        </div>

        <!-- ملاحظات -->
        {% if receipt.notes %}
        <div class="detail-group">
            <h3 class="detail-title">
                <i class="fas fa-sticky-note"></i>
                ملاحظات
            </h3>
            <p>{{ receipt.notes }}</p>
        </div>
        {% endif %}

        <!-- أصناف الإذن -->
        <div class="items-section">
            <h3 class="section-title">
                <i class="fas fa-boxes"></i>
                أصناف الإذن
            </h3>
            
            <table class="items-table">
                <thead>
                    <tr>
                        <th>كود الصنف</th>
                        <th>اسم الصنف</th>
                        <th>الكمية</th>
                        <th>الوحدة</th>
                        <th>موقع التخزين</th>
                        <th>رقم الدفعة</th>
                        <th>تكلفة الوحدة</th>
                        <th>إجمالي التكلفة</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in receipt.receipt_items.all %}
                    <tr>
                        <td>{{ item.item.item_code }}</td>
                        <td>{{ item.item.item_name_ar }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>{{ item.item.unit_of_measure }}</td>
                        <td>{{ item.bin_location.bin_code }}</td>
                        <td>{{ item.batch_number|default:"-" }}</td>
                        <td>{{ item.unit_cost|default:"-" }}</td>
                        <td>{{ item.total_cost|default:"-" }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" style="text-align: center; color: var(--gray-600);">
                            لا توجد أصناف في هذا الإذن
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- المجاميع -->
        <div class="totals-section">
            <div class="totals-grid">
                <div class="total-item">
                    <div class="total-value">{{ receipt.total_items }}</div>
                    <div class="total-label">عدد الأصناف</div>
                </div>
                <div class="total-item">
                    <div class="total-value">{{ receipt.total_quantity }}</div>
                    <div class="total-label">إجمالي الكمية</div>
                </div>
            </div>
        </div>

        <!-- الإجراءات -->
        <div class="actions-section">
            <a href="{% url 'inventory:goods_receipt_print' pk=receipt.pk %}" class="btn btn-primary" target="_blank">
                <i class="fas fa-print"></i>
                طباعة الإذن
            </a>
            <a href="{% url 'inventory:goods_receipt_pdf' pk=receipt.pk %}" class="btn btn-success">
                <i class="fas fa-file-pdf"></i>
                تحميل PDF
            </a>
            <a href="{% url 'inventory:movements_list' %}" class="btn btn-outline">
                <i class="fas fa-arrow-right"></i>
                العودة للحركات
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// يمكن إضافة JavaScript هنا حسب الحاجة
</script>
{% endblock %}
