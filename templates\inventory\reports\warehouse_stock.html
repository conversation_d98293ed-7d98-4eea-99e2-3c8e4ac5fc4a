{% extends "base.html" %}
{% load static %}

{% block title %}تقرير الأرصدة حسب المخزن - KamaVerse{% endblock %}

{% block extra_css %}
<style>
    :root {
        --brand-red: #D62828;
        --brand-red-dark: #8B1116;
        --brand-red-light: #FCE8E8;
        --brand-gold: #C89A3C;
        --brand-gold-light: #F4D488;
        --brand-gold-dark: #8C6420;
        --ink: #1A1A1A;
        --slate: #4A4F57;
        --line: #E6E8ED;
        --canvas: #F7F8FB;
        --white: #FFFFFF;
        --success: #2E7D32;
        --warning: #F39C12;
        --error: #C21807;
    }

    .page-container {
        background: linear-gradient(180deg, #FFFFFF 0%, #F7F8FB 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .page-header {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    .page-header h1 {
        color: var(--ink);
        font-weight: 700;
        font-size: 2.5rem;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .page-header .icon {
        color: var(--brand-gold);
        font-size: 3rem;
    }

    .page-header p {
        color: var(--slate);
        font-size: 1.1rem;
        margin: 1rem 0 0 0;
    }
    
    /* نظام البحث والفلاتر */
    .filters-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }
    
    .filters-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--ink);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .filter-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    /* المخزن المختار */
    .warehouse-report-container {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    .warehouse-header {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid var(--brand-gold);
    }

    .warehouse-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.8rem;
        color: var(--brand-gold);
        background: var(--brand-gold-light);
    }

    .warehouse-info h2 {
        color: var(--ink);
        font-weight: 700;
        font-size: 1.8rem;
        margin: 0;
    }

    .warehouse-info p {
        color: var(--slate);
        font-size: 1rem;
        margin: 0.3rem 0 0 0;
    }
    
    /* ملخص نسب الإشغال والفراغ */
    .occupancy-stats {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stat-item {
        background: var(--canvas);
        border-radius: 12px;
        padding: 1.2rem;
        text-align: center;
        border: 1px solid var(--line);
    }
    
    .stat-value {
        color: var(--brand-red);
        font-weight: 700;
        font-size: 1.8rem;
        margin: 0;
    }
    
    .stat-label {
        color: var(--slate);
        font-size: 1rem;
        margin: 0.5rem 0 0 0;
    }
    
    /* أشرطة النسب */
    .occupancy-bars {
        background: var(--canvas);
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid var(--line);
    }
    
    .occupancy-section {
        margin-bottom: 1.5rem;
    }
    
    .occupancy-section:last-child {
        margin-bottom: 0;
    }
    
    .occupancy-label {
        display: flex;
        justify-content: space-between;
        color: var(--slate);
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .progress {
        height: 12px;
        background: var(--line);
        border-radius: 6px;
        overflow: hidden;
    }
    
    .progress-bar {
        height: 100%;
        border-radius: 6px;
        transition: width 0.3s ease;
    }
    
    .progress-bar.occupied {
        background: linear-gradient(135deg, var(--brand-gold) 0%, var(--brand-gold-dark) 100%);
    }
    
    .progress-bar.free {
        background: linear-gradient(135deg, var(--success) 0%, #1B5E20 100%);
    }

    /* جدول الأصناف */
    .items-table {
        background: var(--canvas);
        border-radius: 12px;
        padding: 1.5rem;
        border: 1px solid var(--line);
        margin-bottom: 1rem;
    }

    .table-title {
        color: var(--ink);
        font-weight: 600;
        font-size: 1.2rem;
        margin-bottom: 1.2rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .table-title i {
        color: var(--brand-gold);
    }

    .table-responsive {
        border-radius: 8px;
        border: 1px solid var(--line);
        background: var(--white);
        overflow-x: auto;
    }

    .table {
        margin: 0;
        color: var(--ink);
        width: 100%;
    }

    .table thead th {
        background: var(--canvas);
        color: var(--ink);
        font-weight: 600;
        border-bottom: 1px solid var(--line);
        padding: 0.75rem;
        font-size: 0.9rem;
    }

    .table tbody td {
        padding: 0.75rem;
        border-bottom: 1px solid var(--line);
        vertical-align: middle;
    }

    .table tbody tr:hover {
        background: rgba(200, 154, 60, 0.05);
    }

    .item-code {
        font-family: 'Courier New', monospace;
        background: var(--canvas);
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.85rem;
        color: var(--slate);
    }

    .quantity-badge {
        background: var(--brand-red-light);
        color: var(--brand-red);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
    }
    
    .unit-badge {
        background: var(--brand-gold-light);
        color: var(--brand-gold-dark);
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.85rem;
        display: inline-block;
    }

    /* قسم اختيار المخزن */
    .warehouse-selection-prompt {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 3rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
        text-align: center;
    }
    
    /* زر الطباعة */
    .print-buttons button {
        background: var(--brand-red);
        color: var(--white);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .print-buttons button:hover {
        background: var(--brand-red-dark);
        transform: translateY(-2px);
    }

    /* عناصر التنقل */
    .breadcrumb {
        background: transparent;
        padding: 0;
        margin-bottom: 1rem;
    }

    .breadcrumb-item {
        color: var(--slate);
    }

    .breadcrumb-item.active {
        color: var(--brand-red);
        font-weight: 600;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: ">";
        color: var(--brand-gold);
        margin: 0 0.5rem;
    }

    /* بيانات فارغة */
    .no-data {
        text-align: center;
        padding: 3rem;
        color: var(--slate);
    }

    .no-data i {
        font-size: 4rem;
        color: var(--brand-gold);
        margin-bottom: 1rem;
    }
    
    /* أنماط الطباعة - المعدلة */
    @media print {
        .no-print {
            display: none !important;
        }
        
        body {
            background: white;
            font-size: 12pt;
            color: #000;
        }
        
        .page-container {
            background: white;
            padding: 0;
            margin: 0;
        }
        
        /* صفحة الطباعة الأولى - معلومات المخزن فقط */
        .warehouse-report-container {
            box-shadow: none;
            border: 1px solid #888;
            padding: 1rem;
            page-break-after: always; /* قسم الصفحة بعد هذه الصفحة */
        }
        
        /* إخفاء جدول الأصناف في الصفحة الأولى */
        .items-table {
            display: none;
        }
        
        .page-header {
            box-shadow: none;
            border: none;
            padding: 1rem;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .page-header h1 {
            font-size: 20pt;
            justify-content: center;
        }
        
        .warehouse-header {
            border-bottom: 2px solid #000;
            margin-bottom: 1rem;
            page-break-after: avoid;
        }
        
        .warehouse-info h2 {
            font-size: 16pt;
        }
        
        .occupancy-stats {
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            page-break-inside: avoid;
        }
        
        .stat-item {
            background: #f9f9f9;
            border: 1px solid #aaa;
        }
        
        .stat-value {
            font-size: 16pt;
        }
        
        .occupancy-bars {
            background: #f9f9f9;
            border: 1px solid #aaa;
            page-break-inside: avoid;
        }
        
        .progress {
            border: 1px solid #ddd;
        }
        
        .progress-bar.occupied {
            background: #C89A3C;
        }
        
        .progress-bar.free {
            background: #2E7D32;
        }
        
        /* صفحة الطباعة الثانية - جدول الأصناف فقط */
        .items-table-print {
            display: block !important;
            background: white;
            padding: 1rem;
            margin-top: 2rem;
        }
        
        .items-table-print .table-title {
            text-align: center;
            font-size: 16pt;
            margin-bottom: 1rem;
        }
        
        .table-responsive {
            overflow-x: visible;
            border: none;
        }
        
        .table {
            border-collapse: collapse;
            width: 100%;
            table-layout: fixed;
        }
        
        .table thead th {
            background: #eee;
            border-bottom: 1px solid #888;
            padding: 8px;
        }
        
        .table tbody td {
            border-bottom: 1px solid #ddd;
            padding: 8px;
        }
        
        .item-code {
            background: #eee;
            color: #333;
        }
        
        .quantity-badge {
            background: #eee;
            color: #333;
            font-weight: bold;
        }
        
        .unit-badge {
            background: #f5f2e5;
            color: #8C6420;
            font-weight: bold;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
        }
        
        /* سيطرة على فواصل الصفحات */
        .warehouse-header,
        .occupancy-stats,
        .occupancy-bars,
        .table-title {
            page-break-after: avoid;
        }
        
        .table tbody tr {
            page-break-inside: avoid;
        }
    }

    /* استجابة للشاشات الصغيرة */
    @media (max-width: 768px) {
        .page-header h1 {
            font-size: 2rem;
        }
        
        .page-header .icon {
            font-size: 2.5rem;
        }

        .warehouse-header {
            flex-direction: column;
            text-align: center;
        }

        .occupancy-stats {
            grid-template-columns: 1fr;
        }

        .table-responsive {
            font-size: 0.85rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="container-fluid">
        <!-- مسار التنقل -->
        <nav aria-label="breadcrumb" class="no-print">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:dashboard' %}">الصفحة الرئيسية</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:reports_main_dashboard' %}">التقارير</a>
                </li>
                <li class="breadcrumb-item active">الأرصدة حسب المخزن</li>
            </ol>
        </nav>

        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1>
                <i class="fas fa-building icon"></i>
                تقرير الأرصدة حسب المخزن
            </h1>
            <p>عرض ملخص الأرصدة والكميات مع نسب الإشغال والفراغ لكل مخزن</p>
        </div>
        
        <!-- نظام البحث واختيار المخزن -->
        <div class="search-container no-print">
            <div class="filters-card">
                <div class="filters-title">
                    <i class="fas fa-search"></i>
                    اختيار المخزن
                </div>
                
                <form method="GET" action="" id="warehouseSelectForm">
                    <div class="filter-row">
                        <div class="form-group">
                            <label for="selected_warehouse">اختر المخزن</label>
                            <select id="selected_warehouse" name="warehouse_id" class="form-control" onchange="this.form.submit()">
                                <option value="">-- اختر المخزن --</option>
                                {% for warehouse in all_warehouses %}
                                    <option value="{{ warehouse.id }}" {% if selected_warehouse_id == warehouse.id|stringformat:"s" %}selected{% endif %}>
                                        {{ warehouse.warehouse_name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- أزرار الطباعة -->
        {% if selected_warehouse %}
        <div class="print-buttons no-print" style="margin-bottom: 2rem;">
            <button onclick="window.print()" class="btn-primary">
                <i class="fas fa-print"></i>
                طباعة التقرير
            </button>
        </div>
        {% endif %}

        <!-- عرض بيانات المخزن المختار -->
        {% if selected_warehouse %}
            <div class="warehouse-report-container">
                <!-- رأس المخزن -->
                <div class="warehouse-header">
                    <div class="warehouse-icon">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <div class="warehouse-info">
                        <h2>{{ selected_warehouse.warehouse_name }}</h2>
                        <p>{{ selected_warehouse.location|default:"غير محدد" }}</p>
                    </div>
                </div>

                <!-- ملخص النسب -->
                <div class="occupancy-stats">
                    <div class="stat-item">
                        <p class="stat-value">{{ warehouse_data.total_items|floatformat:0 }}</p>
                        <p class="stat-label">عدد الأصناف</p>
                    </div>
                    <div class="stat-item">
                        <p class="stat-value">{{ warehouse_data.occupancy_percentage|floatformat:1 }}%</p>
                        <p class="stat-label">نسبة الإشغال</p>
                    </div>
                    <div class="stat-item">
                        <p class="stat-value">{{ free_percentage|floatformat:1 }}%</p>
                        <p class="stat-label">نسبة الفراغ</p>
                    </div>
                </div>

                <!-- أشرطة النسب -->
                <div class="occupancy-bars">
                    <div class="occupancy-section">
                        <div class="occupancy-label">
                            <span>نسبة الإشغال</span>
                            <span><strong>{{ warehouse_data.occupancy_percentage|floatformat:1 }}%</strong></span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar occupied" style="width: {{ warehouse_data.occupancy_percentage }}%"></div>
                        </div>
                    </div>
                    
                    <div class="occupancy-section">
                        <div class="occupancy-label">
                            <span>نسبة الفراغ</span>
                            <span><strong>{{ free_percentage|floatformat:1 }}%</strong></span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar free" style="width: {{ free_percentage }}%"></div>
                        </div>
                    </div>
                </div>

                <!-- جدول الأصناف - ظاهر فقط على الشاشة -->
                <div class="items-table">
                    <div class="table-title">
                        <i class="fas fa-table"></i>
                        الأصناف في مخزن {{ selected_warehouse.warehouse_name }}
                    </div>
                    
                    {% if warehouse_data.stock_balances %}
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>كود الصنف</th>
                                        <th>اسم الصنف</th>
                                        <th>الكمية</th>
                                        <th>الوحدة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for balance in warehouse_data.stock_balances %}
                                        <tr>
                                            <td>
                                                <span class="item-code">{{ balance.item.item_code }}</span>
                                            </td>
                                            <td>
                                                <strong>{{ balance.item.item_name_ar }}</strong>
                                                {% if balance.item.item_name_en %}
                                                    <br><small class="text-muted">{{ balance.item.item_name_en }}</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="quantity-badge">{{ balance.current_quantity|floatformat:2 }}</span>
                                            </td>
                                            <td>
                                                <span class="unit-badge">{{ balance.item.get_unit_of_measure_display }}</span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="no-data" style="padding: 2rem;">
                            <i class="fas fa-inbox"></i>
                            <h5>لا توجد أصناف في هذا المخزن</h5>
                            <p>المخزن فارغ حالياً</p>
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- جدول الأصناف - ظاهر فقط في الطباعة (الصفحة الثانية) -->
            <div class="items-table items-table-print" style="display: none;">
                <div class="table-title">
                    <i class="fas fa-table"></i>
                    الأصناف في مخزن {{ selected_warehouse.warehouse_name }}
                </div>
                
                {% if warehouse_data.stock_balances %}
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>كود الصنف</th>
                                    <th>اسم الصنف</th>
                                    <th>الكمية</th>
                                    <th>الوحدة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for balance in warehouse_data.stock_balances %}
                                    <tr>
                                        <td>
                                            <span class="item-code">{{ balance.item.item_code }}</span>
                                        </td>
                                        <td>
                                            <strong>{{ balance.item.item_name_ar }}</strong>
                                            {% if balance.item.item_name_en %}
                                                <br><small class="text-muted">{{ balance.item.item_name_en }}</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="quantity-badge">{{ balance.current_quantity|floatformat:2 }}</span>
                                        </td>
                                        <td>
                                            <span class="unit-badge">{{ balance.item.get_unit_of_measure_display }}</span>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="no-data" style="padding: 2rem;">
                        <i class="fas fa-inbox"></i>
                        <h5>لا توجد أصناف في هذا المخزن</h5>
                        <p>المخزن فارغ حالياً</p>
                    </div>
                {% endif %}
            </div>
        {% elif warehouse_data|length > 0 %}
            <div class="warehouse-selection-prompt">
                <div class="no-data">
                    <i class="fas fa-hand-point-up"></i>
                    <h4>الرجاء اختيار مخزن</h4>
                    <p>قم باختيار مخزن من القائمة أعلاه لعرض تفاصيله</p>
                </div>
            </div>
        {% else %}
            <div class="warehouse-card">
                <div class="no-data">
                    <i class="fas fa-warehouse"></i>
                    <h4>لا توجد مخازن متاحة</h4>
                    <p>لا توجد مخازن مفعلة في النظام</p>
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}