@echo off
echo ========================================
echo   Killing Django Application Processes
echo ========================================

echo.
echo Stopping Django development server...
taskkill /F /IM python.exe 2>nul
taskkill /F /IM pythonw.exe 2>nul

echo.
echo Stopping any manage.py processes...
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq python.exe" /FO CSV ^| findstr "manage.py"') do (
    taskkill /F /PID %%i 2>nul
)

echo.
echo Stopping runserver processes...
for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq python.exe" /FO CSV ^| findstr "runserver"') do (
    taskkill /F /PID %%i 2>nul
)

echo.
echo Stopping any Django processes on common ports...
for /f "tokens=5" %%i in ('netstat -aon ^| findstr ":8000"') do (
    taskkill /F /PID %%i 2>nul
)

for /f "tokens=5" %%i in ('netstat -aon ^| findstr ":8080"') do (
    taskkill /F /PID %%i 2>nul
)

for /f "tokens=5" %%i in ('netstat -aon ^| findstr ":3000"') do (
    taskkill /F /PID %%i 2>nul
)

echo.
echo Killing any Python processes containing "kamach" or "kamaverse"...
wmic process where "name='python.exe' and CommandLine like '%%kamach%%'" delete 2>nul
wmic process where "name='python.exe' and CommandLine like '%%kamaverse%%'" delete 2>nul

echo.
echo Checking for any remaining Python processes...
tasklist /FI "IMAGENAME eq python.exe" | findstr python.exe
if %errorlevel% == 0 (
    echo Warning: Some Python processes are still running
    echo You may need to manually check them
) else (
    echo No Python processes found - All clear!
)

echo.
echo ========================================
echo   Process cleanup completed
echo ========================================
pause