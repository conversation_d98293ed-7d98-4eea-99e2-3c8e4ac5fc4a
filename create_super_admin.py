"""
<PERSON><PERSON><PERSON> to create a super admin user with access to all system features
including exclusive access to user management.
"""

from django.contrib.auth.models import User
from inventory.models import UserProfile
from django.db import transaction
from django.utils import timezone
from django.core.management.base import BaseCommand
import logging

logger = logging.getLogger(__name__)

def create_super_admin():
    """Create or update the super admin user with specific credentials"""
    try:
        with transaction.atomic():
            # Check if the admin user already exists
            if User.objects.filter(username='admin').exists():
                admin_user = User.objects.get(username='admin')
                logger.info("Admin user already exists, updating credentials and permissions")
                admin_user.set_password('admin123')
                # Set superuser status
                admin_user.is_superuser = True
                admin_user.is_staff = True
                admin_user.save()
            else:
                # Create the admin user
                admin_user = User.objects.create_user(
                    username='admin',
                    password='admin123',
                    email='<EMAIL>',
                    first_name='مدير',
                    last_name='النظام',
                    is_superuser=True,
                    is_staff=True,
                    is_active=True,
                    date_joined=timezone.now()
                )
                logger.info("Admin user created successfully")
            
            # Check if the user has a profile already
            profile, created = UserProfile.objects.get_or_create(
                user=admin_user,
                defaults={
                    'role': 'MANAGER',  # Set to manager to have access to all warehouses
                    'assigned_warehouse': None,  # No specific warehouse (access to all)
                    'is_active': True,
                    'department': 'MANAGEMENT',
                    'phone_number': '0000000000'
                }
            )
            
            if not created:
                # Update existing profile
                profile.role = 'MANAGER'
                profile.assigned_warehouse = None
                profile.is_active = True
                profile.save()
                logger.info("Admin user profile updated successfully")
            else:
                logger.info("Admin user profile created successfully")
        
        return True, "Super admin user created/updated successfully"
    
    except Exception as e:
        logger.error(f"Error creating super admin: {str(e)}")
        return False, str(e)

if __name__ == "__main__":
    success, message = create_super_admin()
    print(message)