{% extends 'base.html' %}
{% load static %}

{% block title %}{{ item.item_name_ar }} - تفاصيل الصنف - KamaVerse{% endblock %}

{% block extra_css %}
<link href="{% static 'css/items.css' %}" rel="stylesheet">
<style>
.item-header {
    background: linear-gradient(135deg, var(--brand-red) 0%, var(--brand-red-dark) 100%);
    color: var(--white);
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.item-header h1 {
    margin: 0;
    font-weight: 700;
}

.item-header .item-code {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: inline-block;
    margin-top: 1rem;
    font-weight: 600;
}

.detail-section {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(26, 26, 26, 0.08);
    border: 1px solid var(--line);
}

.detail-section h3 {
    color: var(--brand-red);
    margin-bottom: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.detail-section h3 i {
    color: var(--brand-gold);
}

.detail-row {
    display: flex;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--line);
}

.detail-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: var(--charcoal);
    width: 200px;
    flex-shrink: 0;
}

.detail-value {
    color: var(--slate);
    flex: 1;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.status-active {
    background: var(--success-light);
    color: var(--success);
}

.status-inactive {
    background: var(--danger-light);
    color: var(--danger);
}

.hazard-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.hazard-none { background: var(--success-light); color: var(--success); }
.hazard-low { background: var(--info-light); color: var(--info); }
.hazard-medium { background: var(--warning-light); color: var(--warning); }
.hazard-high { background: var(--danger-light); color: var(--danger); }
.hazard-critical { background: var(--charcoal); color: var(--white); }

.action-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.btn-edit {
    background: var(--brand-gold);
    color: var(--white);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
}

.btn-edit:hover {
    background: var(--brand-gold-dark);
    color: var(--white);
    transform: translateY(-1px);
}

.btn-back {
    background: var(--slate);
    color: var(--white);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
}

.btn-back:hover {
    background: var(--charcoal);
    color: var(--white);
}

.btn-delete {
    background: var(--danger);
    color: var(--white);
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn-delete:hover {
    background: var(--danger-dark);
}

.stock-info {
    background: var(--canvas);
    padding: 1.5rem;
    border-radius: 8px;
    margin-top: 1rem;
}

.stock-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--line);
}

.stock-item:last-child {
    border-bottom: none;
}

.stock-label {
    font-weight: 600;
    color: var(--charcoal);
}

.stock-value {
    color: var(--brand-red);
    font-weight: 700;
    font-size: 1.1rem;
}
</style>
{% endblock %}

{% block content %}
<!-- Item Header -->
<div class="item-header">
    <h1>{{ item.item_name_ar }}</h1>
    {% if item.item_name_en %}
        <p class="mb-0 opacity-75">{{ item.item_name_en }}</p>
    {% endif %}
    <div class="item-code">{{ item.item_code }}</div>
</div>

<!-- Basic Information -->
<div class="detail-section">
    <h3>
        <i class="fas fa-info-circle"></i>
        المعلومات الأساسية
    </h3>
    
    <div class="detail-row">
        <div class="detail-label">كود الصنف:</div>
        <div class="detail-value">{{ item.item_code }}</div>
    </div>
    
    <div class="detail-row">
        <div class="detail-label">اسم الصنف (عربي):</div>
        <div class="detail-value">{{ item.item_name_ar }}</div>
    </div>
    
    {% if item.item_name_en %}
    <div class="detail-row">
        <div class="detail-label">اسم الصنف (إنجليزي):</div>
        <div class="detail-value">{{ item.item_name_en }}</div>
    </div>
    {% endif %}
    
    {% if item.chemical_formula %}
    <div class="detail-row">
        <div class="detail-label">الصيغة الكيميائية:</div>
        <div class="detail-value">{{ item.chemical_formula }}</div>
    </div>
    {% endif %}
    
    {% if item.description %}
    <div class="detail-row">
        <div class="detail-label">الوصف:</div>
        <div class="detail-value">{{ item.description }}</div>
    </div>
    {% endif %}
    
    <div class="detail-row">
        <div class="detail-label">الحالة:</div>
        <div class="detail-value">
            {% if item.is_active %}
                <span class="status-badge status-active">نشط</span>
            {% else %}
                <span class="status-badge status-inactive">غير نشط</span>
            {% endif %}
        </div>
    </div>
</div>

<!-- Classification -->
<div class="detail-section">
    <h3>
        <i class="fas fa-tags"></i>
        التصنيف
    </h3>
    
    <div class="detail-row">
        <div class="detail-label">مجموعة المواد:</div>
        <div class="detail-value">{{ item.get_category_display }}</div>
    </div>
    
    <div class="detail-row">
        <div class="detail-label">نوع المادة:</div>
        <div class="detail-value">{{ item.get_material_type_display }}</div>
    </div>
    
    <div class="detail-row">
        <div class="detail-label">مستوى الخطورة:</div>
        <div class="detail-value">
            <span class="hazard-badge hazard-{{ item.hazard_level|lower }}">
                {{ item.get_hazard_level_display }}
            </span>
        </div>
    </div>
</div>

<!-- Units and Stock -->
<div class="detail-section">
    <h3>
        <i class="fas fa-balance-scale"></i>
        الوحدات والمخزون
    </h3>
    
    <div class="detail-row">
        <div class="detail-label">وحدة القياس الأساسية:</div>
        <div class="detail-value">{{ item.get_unit_of_measure_display }}</div>
    </div>
    
    <div class="detail-row">
        <div class="detail-label">الحد الأدنى للمخزون:</div>
        <div class="detail-value">{{ item.minimum_stock_level|floatformat:2 }} {{ item.get_unit_of_measure_display }}</div>
    </div>
    
    <div class="detail-row">
        <div class="detail-label">نقطة إعادة الطلب:</div>
        <div class="detail-value">{{ item.reorder_point|floatformat:2 }} {{ item.get_unit_of_measure_display }}</div>
    </div>
    
    <!-- Current Stock Information -->
    <div class="stock-info">
        <h5 class="mb-3">معلومات المخزون الحالي</h5>
        <div class="stock-item">
            <div class="stock-label">الرصيد الحالي:</div>
            <div class="stock-value">{{ total_quantity|floatformat:2 }} {{ item.get_unit_of_measure_display }}</div>
        </div>
        <div class="stock-item">
            <div class="stock-label">عدد المخازن المتوفر بها:</div>
            <div class="stock-value">{{ warehouse_count }} مخزن</div>
        </div>
        <div class="stock-item">
            <div class="stock-label">آخر حركة:</div>
            <div class="stock-value">
                {% if last_movement %}
                    {{ last_movement.get_movement_type_display }} - {{ last_movement.movement_date|date:"d/m/Y H:i" }}
                {% else %}
                    لا توجد حركات
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Warehouse Stock Details -->
    {% if stock_balances %}
    <div class="stock-info mt-3">
        <h5 class="mb-3">تفاصيل المخزون حسب المخازن</h5>
        {% for balance in stock_balances %}
        <div class="stock-item">
            <div class="stock-label">
                {{ balance.warehouse.warehouse_name }} - {{ balance.bin_location.bin_code }}
                {% if balance.batch_number %}
                    (دفعة: {{ balance.batch_number }})
                {% endif %}
            </div>
            <div class="stock-value">{{ balance.current_quantity|floatformat:2 }} {{ item.get_unit_of_measure_display }}</div>
        </div>
        {% endfor %}
    </div>
    {% endif %}
</div>

<!-- Storage Information -->
<div class="detail-section">
    <h3>
        <i class="fas fa-warehouse"></i>
        معلومات التخزين
    </h3>
    
    {% if item.storage_conditions %}
    <div class="detail-row">
        <div class="detail-label">شروط التخزين:</div>
        <div class="detail-value">{{ item.storage_conditions }}</div>
    </div>
    {% endif %}
    
    {% if item.shelf_life_months %}
    <div class="detail-row">
        <div class="detail-label">مدة الصلاحية:</div>
        <div class="detail-value">{{ item.shelf_life_months }} شهر</div>
    </div>
    {% endif %}
    
    <div class="detail-row">
        <div class="detail-label">تاريخ الإضافة:</div>
        <div class="detail-value">{{ item.created_at|date:"d/m/Y H:i" }}</div>
    </div>
    
    <div class="detail-row">
        <div class="detail-label">آخر تحديث:</div>
        <div class="detail-value">{{ item.updated_at|date:"d/m/Y H:i" }}</div>
    </div>
</div>

<!-- Action Buttons -->
<div class="action-buttons">
    <a href="{% url 'inventory:items_list' %}" class="btn-back">
        <i class="fas fa-arrow-right me-2"></i>
        العودة للقائمة
    </a>
    
    <a href="{% url 'inventory:item_edit' item.pk %}" class="btn-edit">
        <i class="fas fa-edit me-2"></i>
        تعديل الصنف
    </a>
    
    <button type="button" class="btn-delete" onclick="confirmDelete()">
        <i class="fas fa-trash me-2"></i>
        حذف الصنف
    </button>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete() {
    if (confirm('هل أنت متأكد من حذف هذا الصنف؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        window.location.href = '{% url "inventory:item_delete" item.pk %}';
    }
}
</script>
{% endblock %}
