{% extends 'base.html' %}
{% load static %}

{% block title %}التقارير - KamaVerse{% endblock %}

{% block extra_css %}
<style>
    .reports-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        overflow: hidden;
        margin-bottom: 30px;
    }

    .reports-header {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
        padding: 25px 30px;
        text-align: center;
    }

    .reports-header h1 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: bold;
    }

    .reports-body {
        padding: 30px;
    }

    .report-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 30px;
        border: 1px solid #e9ecef;
    }

    .section-title {
        color: #17a2b8;
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .report-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }

    .report-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;
    }

    .report-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .report-card h3 {
        color: #17a2b8;
        font-size: 14px;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .report-form {
        margin-bottom: 15px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
        font-size: 12px;
    }

    .form-control {
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 13px;
        width: 100%;
    }

    .btn-report {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: 600;
        font-size: 13px;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        margin-bottom: 5px;
    }

    .btn-report:hover {
        background: linear-gradient(135deg, #138496, #117a8b);
        transform: translateY(-1px);
    }

    .btn-secondary {
        background: #6c757d;
    }

    .btn-secondary:hover {
        background: #5a6268;
    }

    .quick-links {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        margin-top: 15px;
    }

    .quick-link {
        background: #e9ecef;
        color: #495057;
        padding: 5px 10px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 11px;
        transition: all 0.3s ease;
    }

    .quick-link:hover {
        background: #17a2b8;
        color: white;
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .report-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="reports-container">
    <div class="reports-header">
        <h1><i class="fas fa-chart-bar me-3"></i>مركز التقارير</h1>
        <p>تقارير شاملة لإيصالات الاستلام والمخزون</p>
    </div>

    <div class="reports-body">
        <!-- تقارير إيصالات الاستلام -->
        <div class="report-section">
            <div class="section-title">
                <i class="fas fa-receipt"></i>
                تقارير إيصالات الاستلام
            </div>

            <div class="report-grid">
                <!-- التقرير اليومي -->
                <div class="report-card">
                    <h3><i class="fas fa-calendar-day"></i>التقرير اليومي</h3>
                    <form class="report-form" action="{% url 'inventory:daily_receipts_report' %}" method="get">
                        <div class="form-group">
                            <label class="form-label">التاريخ:</label>
                            <input type="date" name="date" class="form-control" value="{% now 'Y-m-d' %}">
                        </div>
                        <button type="submit" class="btn-report">
                            <i class="fas fa-file-pdf me-1"></i>
                            تحميل التقرير اليومي
                        </button>
                    </form>
                    <div class="quick-links">
                        <a href="{% url 'inventory:daily_receipts_report' %}?date={% now 'Y-m-d' %}" class="quick-link">اليوم</a>
                        <a href="{% url 'inventory:daily_receipts_report' %}?date={{ yesterday }}" class="quick-link">أمس</a>
                    </div>
                </div>

                <!-- التقرير الشهري -->
                <div class="report-card">
                    <h3><i class="fas fa-calendar-alt"></i>التقرير الشهري</h3>
                    <form class="report-form" action="{% url 'inventory:monthly_receipts_report' %}" method="get">
                        <div class="form-group">
                            <label class="form-label">السنة:</label>
                            <select name="year" class="form-control">
                                {% for year in years %}
                                <option value="{{ year }}" {% if year == current_year %}selected{% endif %}>{{ year }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">الشهر:</label>
                            <select name="month" class="form-control">
                                {% for month in months %}
                                <option value="{{ month.number }}" {% if month.number == current_month %}selected{% endif %}>{{ month.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <button type="submit" class="btn-report">
                            <i class="fas fa-file-pdf me-1"></i>
                            تحميل التقرير الشهري
                        </button>
                    </form>
                    <div class="quick-links">
                        <a href="{% url 'inventory:monthly_receipts_report' %}?year={% now 'Y' %}&month={% now 'm' %}" class="quick-link">هذا الشهر</a>
                        <a href="{% url 'inventory:monthly_receipts_report' %}?year={{ last_month.year }}&month={{ last_month.month }}" class="quick-link">الشهر الماضي</a>
                    </div>
                </div>

                <!-- تقرير مخصص -->
                <div class="report-card">
                    <h3><i class="fas fa-calendar-week"></i>تقرير مخصص</h3>
                    <form class="report-form" action="{% url 'inventory:custom_receipts_report' %}" method="get">
                        <div class="form-group">
                            <label class="form-label">من تاريخ:</label>
                            <input type="date" name="start_date" class="form-control" value="{% now 'Y-m-01' %}">
                        </div>
                        <div class="form-group">
                            <label class="form-label">إلى تاريخ:</label>
                            <input type="date" name="end_date" class="form-control" value="{% now 'Y-m-d' %}">
                        </div>
                        <div class="form-group">
                            <label class="form-label">المخزن (اختياري):</label>
                            <select name="warehouse" class="form-control">
                                <option value="">جميع المخازن</option>
                                {% for warehouse in warehouses %}
                                <option value="{{ warehouse.id }}">{{ warehouse.warehouse_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <button type="submit" class="btn-report">
                            <i class="fas fa-file-pdf me-1"></i>
                            تحميل التقرير المخصص
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="report-section">
            <div class="section-title">
                <i class="fas fa-chart-pie"></i>
                إحصائيات سريعة
            </div>

            <div class="report-grid">
                <div class="report-card">
                    <h3><i class="fas fa-today"></i>إحصائيات اليوم</h3>
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 24px; font-weight: bold; color: #17a2b8;">{{ today_stats.receipts_count }}</div>
                        <div style="font-size: 12px; color: #6c757d;">إيصالات اليوم</div>
                        <div style="font-size: 18px; font-weight: bold; color: #28a745; margin-top: 10px;">{{ today_stats.items_count }}</div>
                        <div style="font-size: 12px; color: #6c757d;">أصناف مستلمة</div>
                    </div>
                </div>

                <div class="report-card">
                    <h3><i class="fas fa-calendar-month"></i>إحصائيات الشهر</h3>
                    <div style="text-align: center; padding: 20px;">
                        <div style="font-size: 24px; font-weight: bold; color: #17a2b8;">{{ month_stats.receipts_count }}</div>
                        <div style="font-size: 12px; color: #6c757d;">إيصالات الشهر</div>
                        <div style="font-size: 18px; font-weight: bold; color: #28a745; margin-top: 10px;">{{ month_stats.items_count }}</div>
                        <div style="font-size: 12px; color: #6c757d;">أصناف مستلمة</div>
                    </div>
                </div>

                <div class="report-card">
                    <h3><i class="fas fa-warehouse"></i>أنشط المخازن</h3>
                    <div style="padding: 10px;">
                        {% for warehouse in active_warehouses %}
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px; font-size: 12px;">
                            <span>{{ warehouse.name }}</span>
                            <span style="font-weight: bold; color: #17a2b8;">{{ warehouse.receipts_count }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- العودة -->
        <div style="text-align: center; margin-top: 30px;">
            <a href="{% url 'inventory:dashboard' %}" class="btn-report btn-secondary" style="width: auto; padding: 12px 30px;">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للوحة التحكم
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// يمكن إضافة JavaScript هنا حسب الحاجة
document.addEventListener('DOMContentLoaded', function() {
    // تحديث التواريخ تلقائياً
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    // يمكن إضافة المزيد من الوظائف هنا
});
</script>
{% endblock %}
