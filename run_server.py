#!/usr/bin/env python
"""
Direct Django Server Runner - تشغيل مباشر لخادم Django
"""
import os
import sys
import subprocess

def main():
    print("=" * 60)
    print("    KamaVerse Inventory System")
    print("    نظام إدارة المخزون - شركة القماش")
    print("=" * 60)
    print()
    
    # Clear environment variables that might interfere
    if 'VIRTUAL_ENV' in os.environ:
        del os.environ['VIRTUAL_ENV']
    
    # Set Django settings
    os.environ['DJANGO_SETTINGS_MODULE'] = 'kamaverse_inventory.settings'
    
    print("🔧 Installing Django...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'django', '--quiet'], check=True)
        print("✅ Django installed successfully")
    except subprocess.CalledProcessError:
        print("❌ Failed to install Django")
        return
    
    print("🔧 Running migrations...")
    try:
        subprocess.run([sys.executable, 'manage.py', 'migrate'], check=True, capture_output=True)
        print("✅ Database migrations completed")
    except subprocess.CalledProcessError as e:
        print(f"❌ Migration failed: {e}")
        return
    
    print("🔧 Creating admin user...")
    try:
        # Create superuser if it doesn't exist
        create_user_script = """
import os
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='admin').exists():
    User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
    print('Admin user created')
else:
    print('Admin user already exists')
"""
        subprocess.run([sys.executable, '-c', create_user_script], check=True)
        print("✅ Admin user ready (admin/admin123)")
    except subprocess.CalledProcessError:
        print("⚠️ Could not create admin user (may already exist)")
    
    print()
    print("🚀 Starting Django server...")
    print("=" * 60)
    print("🌐 Server URL: http://127.0.0.1:8000")
    print("👤 Admin Panel: http://127.0.0.1:8000/admin")
    print("🔑 Login: admin / admin123")
    print("🛑 Press Ctrl+C to stop the server")
    print("=" * 60)
    print()
    
    try:
        # Start the Django development server
        subprocess.run([sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000'])
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Server error: {e}")

if __name__ == '__main__':
    main()
