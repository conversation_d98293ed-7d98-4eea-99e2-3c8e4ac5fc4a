# Generated by Django 5.2.5 on 2025-09-01 15:43

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0007_stocktransferitem_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserPermission',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('permission_type', models.CharField(choices=[('VIEW_INVENTORY', 'عرض المخزون'), ('ADD_ITEMS', 'إضافة أصناف'), ('EDIT_ITEMS', 'تعديل أصناف'), ('DELETE_ITEMS', 'حذف أصناف'), ('VIEW_WAREHOUSE', 'عرض المخازن'), ('ADD_WAREHOUSE', 'إضافة مخزن'), ('EDIT_WAREHOUSE', 'تعديل مخزن'), ('DELETE_WAREHOUSE', 'حذف مخزن'), ('GOODS_RECEIPT', 'إذن استلام'), ('GOODS_ISSUE', 'إذن صرف'), ('STOCK_TRANSFER', 'نقل مخزون'), ('VIEW_REPORTS', 'عرض التقارير'), ('EXPORT_REPORTS', 'تصدير التقارير'), ('MANAGE_USERS', 'إدارة المستخدمين'), ('EDIT_USER_PERMISSIONS', 'تعديل صلاحيات المستخدمين'), ('DELETE_USERS', 'حذف المستخدمين')], max_length=50, verbose_name='نوع الصلاحية')),
                ('is_granted', models.BooleanField(default=True, verbose_name='ممنوحة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('granted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='granted_permissions', to=settings.AUTH_USER_MODEL, verbose_name='منحت بواسطة')),
                ('user_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='permissions', to='inventory.userprofile', verbose_name='ملف المستخدم')),
            ],
            options={
                'verbose_name': 'صلاحية مستخدم',
                'verbose_name_plural': 'صلاحيات المستخدمين',
                'unique_together': {('user_profile', 'permission_type')},
            },
        ),
    ]
