{% extends 'base.html' %}
{% load static %}

{% block title %}تفاصيل حركات المخزون - {{ warehouse.warehouse_name }} - KamaVerse{% endblock %}

{% block extra_css %}
<style>
    :root {
        --brand-red: #D62828;
        --brand-red-dark: #8B1116;
        --brand-red-light: #FCE8E8;
        --brand-gold: #C89A3C;
        --brand-gold-light: #F4D488;
        --brand-gold-dark: #8C6420;
        --ink: #1A1A1A;
        --slate: #4A4F57;
        --line: #E6E8ED;
        --canvas: #F7F8FB;
        --white: #FFFFFF;
        --success: #2E7D32;
        --warning: #F39C12;
        --error: #C21807;
    }

    .page-container {
        background: linear-gradient(180deg, #FFFFFF 0%, #F7F8FB 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .page-header {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    .page-header h1 {
        color: var(--ink);
        font-weight: 700;
        font-size: 2rem;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .page-header .icon {
        color: var(--brand-gold);
        font-size: 2.5rem;
    }

    .filters-card {
        background: var(--white);
        border: 1px solid var(--line);
        border-radius: 16px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(26, 26, 26, 0.08);
    }

    .filters-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--ink);
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .filter-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .summary-stats {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: var(--white);
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 8px 20px rgba(26, 26, 26, 0.05);
        text-align: center;
    }

    .stat-card .stat-title {
        color: var(--slate);
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .stat-card .stat-value {
        font-size: 2.2rem;
        font-weight: 700;
        margin: 0;
    }

    .stat-card.receipts .stat-value {
        color: var(--success);
    }

    .stat-card.issues .stat-value {
        color: var(--error);
    }

    .stat-card.transfer-in .stat-value {
        color: var(--brand-gold);
    }

    .stat-card.transfer-out .stat-value {
        color: var(--warning);
    }

    .movements-table-container {
        background: var(--white);
        border-radius: 16px;
        box-shadow: 0 8px 20px rgba(26, 26, 26, 0.05);
        overflow: hidden;
        margin-bottom: 2.5rem;
    }

    .table-header {
        padding: 1.5rem 2rem;
        background: var(--canvas);
        border-bottom: 1px solid var(--line);
    }

    .table-header h3 {
        color: var(--ink);
        font-weight: 600;
        font-size: 1.3rem;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .movements-table {
        width: 100%;
        border-collapse: collapse;
    }

    .movements-table th {
        background: var(--canvas);
        color: var(--slate);
        font-weight: 600;
        text-align: right;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--line);
    }

    .movements-table td {
        padding: 1.2rem 1.5rem;
        border-bottom: 1px solid var(--line);
        color: var(--ink);
    }

    .movements-table tr:hover {
        background: rgba(200, 154, 60, 0.05);
    }

    .movement-type {
        display: inline-block;
        padding: 0.4rem 1rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .movement-type.receipt {
        background: rgba(46, 125, 50, 0.1);
        color: var(--success);
    }

    .movement-type.issue {
        background: rgba(194, 24, 7, 0.1);
        color: var(--error);
    }

    .movement-type.transfer-in {
        background: rgba(200, 154, 60, 0.1);
        color: var(--brand-gold);
    }

    .movement-type.transfer-out {
        background: rgba(243, 156, 18, 0.1);
        color: var(--warning);
    }

    .btn-export {
        background: var(--success);
        color: var(--white);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin-left: 1rem;
    }

    .btn-export:hover {
        background: #1B5E20;
        transform: translateY(-2px);
    }

    .btn-action {
        background: var(--brand-gold);
        color: var(--white);
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.3rem;
        text-decoration: none;
    }

    .btn-action:hover {
        background: var(--brand-gold-dark);
        transform: translateY(-2px);
        color: var(--white);
        text-decoration: none;
    }

    .btn-action.print {
        background: var(--error);
    }

    .btn-action.print:hover {
        background: var(--brand-red-dark);
    }

    .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
        margin-bottom: 2rem;
    }

    .pagination {
        display: flex;
        gap: 0.5rem;
    }

    .pagination .page-item .page-link {
        border-radius: 8px;
        color: var(--slate);
        border: 1px solid var(--line);
        padding: 0.5rem 1rem;
        font-weight: 600;
    }

    .pagination .page-item.active .page-link {
        background: var(--brand-gold);
        color: white;
        border-color: var(--brand-gold);
    }

    /* Print styles */
    @media print {
        .no-print {
            display: none !important;
        }
        
        .page-container {
            background: white;
            padding: 0;
        }
        
        .movements-table-container {
            box-shadow: none;
            border: 1px solid #ddd;
        }
        
        .movements-table {
            table-layout: fixed;
            width: 100%;
        }
        
        .movements-table th,
        .movements-table td {
            padding: 8px;
        }
    }

    @media (max-width: 768px) {
        .filter-row {
            grid-template-columns: 1fr;
            gap: 1rem;
        }
        
        .summary-stats {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }
        
        .movements-table {
            min-width: 1000px;
        }
        
        .movements-table-container {
            overflow-x: auto;
        }
    }
    
    @media (max-width: 480px) {
        .summary-stats {
            grid-template-columns: 1fr;
        }
    }

    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: var(--slate);
    }

    .empty-icon {
        font-size: 4rem;
        color: var(--line);
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-container">
    <div class="container-fluid">
        <!-- مسار التنقل -->
        <nav aria-label="breadcrumb" class="no-print">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:dashboard' %}">الصفحة الرئيسية</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:reports_main_dashboard' %}">التقارير</a>
                </li>
                <li class="breadcrumb-item">
                    <a href="{% url 'inventory:consolidated_movements_report' %}">حركات المخزون المجمعة</a>
                </li>
                <li class="breadcrumb-item active">{{ warehouse.warehouse_name }}</li>
            </ol>
        </nav>

        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1>
                <i class="fas fa-warehouse icon"></i>
                تفاصيل حركات المخزون - {{ warehouse.warehouse_name }}
                {% if warehouse.city %}
                <small style="font-size: 0.8em; color: var(--slate); margin-right: 0.5rem;">({{ warehouse.city }})</small>
                {% endif %}
            </h1>
            <p>تقرير تفصيلي لجميع حركات المخزون (استلام-صرف-نقل) لمخزن {{ warehouse.warehouse_name }}</p>
        </div>

        <!-- أزرار الطباعة -->
        <div class="no-print" style="margin-bottom: 2rem;">
            <button onclick="window.print();" class="btn-export" style="background: var(--error);">
                <i class="fas fa-print"></i>
                طباعة التقرير
            </button>
        </div>

        <!-- الفلاتر -->
        <div class="filters-card no-print">
            <div class="filters-title">
                <i class="fas fa-filter"></i>
                بحث متقدم عن الحركات
            </div>
            
            <form method="GET" action="">
                <input type="hidden" name="warehouse" value="{{ warehouse.id }}">
                
                <div class="filter-row">
                    <div class="form-group">
                        <label for="movement_type">نوع الحركة</label>
                        <select id="movement_type" name="movement_type" class="form-control">
                            <option value="">جميع أنواع الحركات</option>
                            {% for type_code, type_name in movement_types %}
                                <option value="{{ type_code }}" 
                                        {% if movement_type == type_code %}selected{% endif %}>
                                    {{ type_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="date_from">من تاريخ</label>
                        <input type="date" id="date_from" name="date_from" 
                               class="form-control" value="{{ date_from }}">
                    </div>
                    
                    <div class="form-group">
                        <label for="date_to">إلى تاريخ</label>
                        <input type="date" id="date_to" name="date_to" 
                               class="form-control" value="{{ date_to }}">
                    </div>
                </div>
                
                <div class="d-flex gap-2 flex-wrap">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-search"></i>
                        بحث
                    </button>
                    <a href="{% url 'inventory:warehouse_movements_detail' warehouse.id %}" class="btn-secondary">
                        <i class="fas fa-times"></i>
                        إزالة الفلاتر
                    </a>
                </div>
            </form>
        </div>

        <!-- ملخص الإحصائيات -->
        <div class="summary-stats">
            <div class="stat-card">
                <div class="stat-title">إجمالي الحركات</div>
                <div class="stat-value">{{ summary_data.total_movements }}</div>
            </div>
            
            <div class="stat-card receipts">
                <div class="stat-title">إذن استلام</div>
                <div class="stat-value">{{ summary_data.total_receipts }}</div>
            </div>
            
            <div class="stat-card issues">
                <div class="stat-title">إذن صرف</div>
                <div class="stat-value">{{ summary_data.total_issues }}</div>
            </div>
            
            <div class="stat-card transfer-in">
                <div class="stat-title">نقل مخزون</div>
                <div class="stat-value">{{ summary_data.total_transfers }}</div>
            </div>
        </div>

        <!-- جدول الحركات -->
        <div class="movements-table-container">
            <div class="table-header">
                <h3>
                    <i class="fas fa-exchange-alt"></i>
                    حركات مخزن {{ warehouse.warehouse_name }}
                </h3>
            </div>
            
            {% if movements %}
                <div class="table-responsive">
                    <table class="movements-table">
                        <thead>
                            <tr>
                                <th>رقم الحركة</th>
                                <th>التاريخ</th>
                                <th>نوع الحركة</th>
                                <th>الصنف</th>
                                <th>الكمية</th>
                                <th>وحدة القياس</th>
                                <th>المستند المرجعي</th>
                                <th class="no-print">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for movement in movements %}
                                <tr>
                                    <td>{{ movement.movement_number }}</td>
                                    <td>{{ movement.movement_date|date:"Y-m-d H:i" }}</td>
                                    <td>
                                        {% if movement.movement_type == 'RECEIPT' %}
                                            <span class="movement-type receipt">{{ movement.get_movement_type_display }}</span>
                                        {% elif movement.movement_type == 'ISSUE' %}
                                            <span class="movement-type issue">{{ movement.get_movement_type_display }}</span>
                                        {% elif movement.movement_type == 'TRANSFER_IN' %}
                                            <span class="movement-type transfer-in">{{ movement.get_movement_type_display }}</span>
                                        {% elif movement.movement_type == 'TRANSFER_OUT' %}
                                            <span class="movement-type transfer-out">{{ movement.get_movement_type_display }}</span>
                                        {% else %}
                                            {{ movement.get_movement_type_display }}
                                        {% endif %}
                                    </td>
                                    <td>{{ movement.item.item_name_ar }}</td>
                                    <td>{{ movement.quantity }}</td>
                                    <td>{{ movement.get_unit_of_measure_display }}</td>
                                    <td>{{ movement.reference_document|default:"-" }}</td>
                                    <td class="no-print">
                                        {% if movement.movement_type == 'RECEIPT' and movement.reference_document %}
                                            {% if movement.reference_document|length == 36 %}
                                                <a href="{% url 'inventory:goods_receipt_detail' movement.reference_document %}" class="btn-action">
                                                    <i class="fas fa-eye"></i>
                                                    عرض
                                                </a>
                                                <a href="{% url 'inventory:goods_receipt_print' movement.reference_document %}" class="btn-action print">
                                                    <i class="fas fa-print"></i>
                                                    طباعة
                                                </a>
                                            {% endif %}
                                        {% elif movement.movement_type == 'ISSUE' and movement.reference_document %}
                                            {% if movement.reference_document|length == 36 %}
                                                <a href="{% url 'inventory:goods_issue_detail' movement.reference_document %}" class="btn-action">
                                                    <i class="fas fa-eye"></i>
                                                    عرض
                                                </a>
                                                <a href="{% url 'inventory:goods_issue_print' movement.reference_document %}" class="btn-action print">
                                                    <i class="fas fa-print"></i>
                                                    طباعة
                                                </a>
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if is_paginated %}
                <div class="pagination-container no-print">
                    <nav aria-label="...">
                        <ul class="pagination">
                            {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1{% if movement_type %}&movement_type={{ movement_type }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if movement_type %}&movement_type={{ movement_type }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% if movement_type %}&movement_type={{ movement_type }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if movement_type %}&movement_type={{ movement_type }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if movement_type %}&movement_type={{ movement_type }}{% endif %}{% if date_from %}&date_from={{ date_from }}{% endif %}{% if date_to %}&date_to={{ date_to }}{% endif %}" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            {% else %}
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <h3>لا توجد حركات مخزون</h3>
                    <p>لا توجد حركات متطابقة مع معايير البحث</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

{% endblock %}