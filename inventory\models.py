from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.contrib.auth.models import User
from django.utils import timezone
import uuid
import logging
from decimal import Decimal


class ItemMaster(models.Model):
    """
    نموذج بيانات الأصناف الرئيسية
    Item Master Data Model - مطابق لـ SAP B1 مع تخصص للمواد الكيميائية
    """

    # Product Categories - فئات المنتجات
    PRODUCT_CATEGORIES = [
        ('PLASTIC_RAW', 'خامات بلاستيك'),
        ('RUBBER_RAW', 'خامات مطاط'),
        ('CHEMICALS', 'مواد كيماوية'),
        ('ADDITIVES', 'إضافات'),
        ('COLORANTS', 'ملونات'),
        ('STABILIZERS', 'مثبتات'),
        ('FILLERS', 'حشوات'),
        ('RESINS', 'راتنجات'),
        ('OILS', 'زيوت'),
        ('ACIDS', 'أحماض'),
        ('OXIDES', 'أكاسيد'),
        ('FINISHED', 'منتجات نهائية'),
    ]

    # Material Types - أنواع المواد
    MATERIAL_TYPES = [
        ('RAW_MATERIAL', 'مادة خام'),
        ('FINISHED_PRODUCT', 'منتج نهائي'),
        ('AUXILIARY_MATERIAL', 'مادة مساعدة'),
        ('PACKAGING', 'مواد تعبئة'),
    ]

    # Units of Measure - وحدات القياس
    UNITS_OF_MEASURE = [
        ('KG', 'كيلوجرام'),
        ('TON', 'طن'),
        ('LITER', 'لتر'),
        ('GALLON', 'جالون'),
        ('BAG', 'شيكارة'),
        ('DRUM', 'برميل'),
        ('CARTON', 'كرتونة'),
        ('PIECE', 'قطعة'),
        ('METER', 'متر'),
        ('ROLL', 'لفة'),
        ('CUBIC_METER', 'متر مكعب'),
    ]

    # Hazard Levels - مستويات الخطورة
    HAZARD_LEVELS = [
        ('NONE', 'غير خطر'),
        ('LOW', 'خطورة منخفضة'),
        ('MEDIUM', 'خطورة متوسطة'),
        ('HIGH', 'خطورة عالية'),
        ('CRITICAL', 'خطورة حرجة'),
    ]

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # General Tab - التبويب العام
    item_code = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='كود الصنف',
        help_text='كود تلقائي مع prefix حسب النوع'
    )

    item_name_ar = models.CharField(
        max_length=200,
        verbose_name='اسم الصنف بالعربية'
    )

    item_name_en = models.CharField(
        max_length=200,
        verbose_name='الاسم العلمي/الإنجليزي',
        blank=True,
        null=True
    )

    category = models.CharField(
        max_length=20,
        choices=PRODUCT_CATEGORIES,
        verbose_name='مجموعة المواد'
    )

    material_type = models.CharField(
        max_length=20,
        choices=MATERIAL_TYPES,
        verbose_name='نوع المادة'
    )

    # Inventory Tab - تبويب المخزون
    unit_of_measure = models.CharField(
        max_length=20,
        choices=UNITS_OF_MEASURE,
        default='KG',
        verbose_name='الوحدة الأساسية'
    )

    default_warehouse = models.ForeignKey(
        'Warehouse',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        verbose_name='المخزن الافتراضي'
    )

    default_bin_location = models.ForeignKey(
        'BinLocation',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        verbose_name='موقع التخزين الافتراضي'
    )

    # Planning Tab - تبويب التخطيط
    minimum_stock_level = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='الحد الأدنى للمخزون'
    )

    reorder_point = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='نقطة إعادة الطلب'
    )

    # Physical Properties - الخصائص الفيزيائية
    weight_per_unit = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        blank=True,
        null=True,
        verbose_name='الوزن لكل وحدة'
    )

    density = models.DecimalField(
        max_digits=8,
        decimal_places=4,
        blank=True,
        null=True,
        verbose_name='الكثافة (جم/سم³)'
    )

    color = models.CharField(
        max_length=50,
        verbose_name='اللون',
        blank=True,
        null=True
    )

    # Chemical Properties - الخصائص الكيميائية
    chemical_formula = models.CharField(
        max_length=100,
        verbose_name='الصيغة الكيميائية',
        blank=True,
        null=True
    )

    cas_number = models.CharField(
        max_length=20,
        verbose_name='رقم CAS',
        blank=True,
        null=True,
        help_text='Chemical Abstracts Service Number'
    )

    purity_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        blank=True,
        null=True,
        verbose_name='نسبة النقاء (%)'
    )

    # Safety Information - معلومات الأمان
    hazard_level = models.CharField(
        max_length=20,
        choices=HAZARD_LEVELS,
        default='NONE',
        verbose_name='مستوى الخطورة'
    )

    safety_data_sheet = models.FileField(
        upload_to='items/sds/',
        blank=True,
        null=True,
        verbose_name='ورقة بيانات الأمان'
    )

    # Storage Conditions - شروط التخزين
    storage_temperature_min = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name='درجة الحرارة الدنيا (°م)'
    )

    storage_temperature_max = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name='درجة الحرارة العليا (°م)'
    )

    humidity_max = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name='الرطوبة القصوى (%)'
    )

    shelf_life_months = models.PositiveIntegerField(
        blank=True,
        null=True,
        verbose_name='مدة الصلاحية (شهر)'
    )

    # Picture Tab - تبويب الصورة
    item_image = models.ImageField(
        upload_to='items/images/',
        blank=True,
        null=True,
        verbose_name='صورة المادة'
    )

    # Additional Information
    barcode = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='الباركود'
    )

    supplier_item_code = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='كود الصنف عند المورد'
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )

    # Status and Tracking
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='created_items',
        verbose_name='أنشئ بواسطة'
    )

    class Meta:
        verbose_name = 'صنف'
        verbose_name_plural = 'الأصناف'
        ordering = ['item_name_ar']
        indexes = [
            models.Index(fields=['item_code']),
            models.Index(fields=['category']),
            models.Index(fields=['material_type']),
            models.Index(fields=['is_active']),
            models.Index(fields=['hazard_level']),
        ]

    def __str__(self):
        return f"{self.item_name_ar} ({self.item_code})"

    def get_current_stock(self):
        """الحصول على المخزون الحالي"""
        # سيتم تطوير هذه الوظيفة مع نموذج StockBalance
        return Decimal('0.00')

    def is_low_stock(self):
        """التحقق من انخفاض المخزون"""
        current_stock = self.get_current_stock()
        return current_stock <= self.minimum_stock_level

    def is_out_of_stock(self):
        """التحقق من نفاد المخزون"""
        return self.get_current_stock() <= 0

    def save(self, *args, **kwargs):
        """حفظ مخصص لتوليد كود الصنف تلقائياً"""
        if not self.item_code:
            # توليد كود تلقائي حسب الفئة
            prefix_map = {
                'PLASTIC_RAW': 'PL',
                'RUBBER_RAW': 'RB',
                'CHEMICALS': 'CH',
                'ADDITIVES': 'AD',
                'COLORANTS': 'CL',
                'STABILIZERS': 'ST',
                'FILLERS': 'FL',
                'RESINS': 'RS',
                'OILS': 'OL',
                'ACIDS': 'AC',
                'OXIDES': 'OX',
                'FINISHED': 'FN',
            }
            prefix = prefix_map.get(self.category, 'IT')

            # البحث عن آخر رقم مستخدم
            last_item = ItemMaster.objects.filter(
                item_code__startswith=prefix
            ).order_by('item_code').last()

            if last_item:
                try:
                    last_number = int(last_item.item_code[2:])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1

            self.item_code = f"{prefix}{new_number:04d}"

        super().save(*args, **kwargs)


class Warehouse(models.Model):
    """
    نموذج المخازن
    Warehouse Management Model
    """

    # Warehouse Types - أنواع المخازن
    WAREHOUSE_TYPES = [
        ('MAIN', 'مخزن رئيسي'),
        ('BRANCH', 'مخزن فرعي'),
        ('QUARANTINE', 'مخزن حجر صحي'),
        ('DAMAGED', 'مخزن تالف'),
        ('TRANSIT', 'مخزن عبور'),
    ]

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Basic Information
    warehouse_code = models.CharField(
        max_length=20,
        unique=True,
        verbose_name='كود المخزن'
    )

    warehouse_name = models.CharField(
        max_length=100,
        verbose_name='اسم المخزن'
    )

    warehouse_type = models.CharField(
        max_length=20,
        choices=WAREHOUSE_TYPES,
        default='MAIN',
        verbose_name='نوع المخزن'
    )

    # Location Information
    address = models.TextField(
        verbose_name='العنوان التفصيلي'
    )

    city = models.CharField(
        max_length=50,
        verbose_name='المدينة'
    )

    governorate = models.CharField(
        max_length=50,
        verbose_name='المحافظة'
    )

    postal_code = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        verbose_name='الرمز البريدي'
    )

    # Capacity Information
    total_capacity = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='السعة الإجمالية (متر مكعب)'
    )

    current_occupancy = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='الإشغال الحالي (متر مكعب)'
    )

    # Environmental Controls
    temperature_controlled = models.BooleanField(
        default=False,
        verbose_name='مكيف الهواء'
    )

    humidity_controlled = models.BooleanField(
        default=False,
        verbose_name='تحكم في الرطوبة'
    )

    # Management Information
    warehouse_manager = models.CharField(
        max_length=100,
        verbose_name='مسؤول المخزن'
    )

    manager_phone = models.CharField(
        max_length=20,
        verbose_name='هاتف المسؤول'
    )

    manager_email = models.EmailField(
        blank=True,
        null=True,
        verbose_name='بريد المسؤول'
    )

    # Status and Tracking
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='created_warehouses',
        verbose_name='أنشئ بواسطة'
    )

    class Meta:
        verbose_name = 'مخزن'
        verbose_name_plural = 'المخازن'
        ordering = ['warehouse_name']
        indexes = [
            models.Index(fields=['warehouse_code']),
            models.Index(fields=['warehouse_type']),
            models.Index(fields=['is_active']),
            models.Index(fields=['city']),
        ]

    def __str__(self):
        return f"{self.warehouse_name} ({self.warehouse_code})"

    def get_occupancy_percentage(self):
        """حساب نسبة الإشغال"""
        if self.total_capacity > 0:
            return float(self.current_occupancy / self.total_capacity) * 100
        return 0

    def get_available_capacity(self):
        """حساب السعة المتاحة"""
        return self.total_capacity - self.current_occupancy

    def get_used_capacity(self):
        """حساب السعة المستخدمة"""
        return self.current_occupancy

    def is_full(self):
        """التحقق من امتلاء المخزن"""
        return self.get_occupancy_percentage() >= 95

    def is_nearly_full(self):
        """التحقق من اقتراب امتلاء المخزن"""
        return self.get_occupancy_percentage() >= 80

    def save(self, *args, **kwargs):
        """حفظ مخصص مع تعيين قيمة افتراضية للإشغال لتفعيل نسبة الإشغال والفراغ"""
        # إذا كان إنشاء جديد (وليس تحديثاً) وكانت قيمة الإشغال صفر
        if self._state.adding and self.current_occupancy == 0 and self.total_capacity > 0:
            # تعيين قيمة افتراضية للإشغال (25% من السعة الكلية) لتفعيل نسبة الإشغال
            from decimal import Decimal
            self.current_occupancy = self.total_capacity * Decimal('0.25')
        
        super().save(*args, **kwargs)


class BinLocation(models.Model):
    """
    نموذج مواقع التخزين
    Bin Location Management Model
    """

    # Location Status - حالة الموقع
    LOCATION_STATUS = [
        ('AVAILABLE', 'متاح'),
        ('OCCUPIED', 'مشغول'),
        ('MAINTENANCE', 'تحت الصيانة'),
        ('BLOCKED', 'محجوز'),
        ('DAMAGED', 'تالف'),
    ]

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Basic Information
    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.CASCADE,
        related_name='bin_locations',
        verbose_name='المخزن'
    )

    bin_code = models.CharField(
        max_length=20,
        verbose_name='كود الموقع',
        help_text='مثال: A1, B1, C1...'
    )

    bin_name = models.CharField(
        max_length=100,
        verbose_name='اسم الموقع',
        blank=True,
        null=True
    )

    # Location Details
    aisle = models.CharField(
        max_length=10,
        verbose_name='الممر',
        help_text='مثال: A, B, C'
    )

    rack = models.CharField(
        max_length=10,
        verbose_name='الرف',
        help_text='مثال: 1, 2, 3'
    )

    level = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        verbose_name='المستوى',
        help_text='مثال: 1, 2, 3'
    )

    # Capacity Information
    capacity = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='السعة (متر مكعب)'
    )

    current_quantity = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='الكمية الحالية'
    )

    # Status
    status = models.CharField(
        max_length=20,
        choices=LOCATION_STATUS,
        default='AVAILABLE',
        verbose_name='حالة الموقع'
    )

    # Environmental Specifications
    temperature_zone = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='منطقة الحرارة'
    )

    hazard_zone = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='منطقة الخطورة'
    )

    # Tracking
    last_movement_date = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='تاريخ آخر حركة'
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )

    # Status and Tracking
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    class Meta:
        verbose_name = 'موقع تخزين'
        verbose_name_plural = 'مواقع التخزين'
        ordering = ['warehouse', 'aisle', 'rack', 'level']
        unique_together = ['warehouse', 'bin_code']
        indexes = [
            models.Index(fields=['warehouse', 'bin_code']),
            models.Index(fields=['status']),
            models.Index(fields=['aisle', 'rack']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.warehouse.warehouse_name} - {self.bin_code}"

    def get_occupancy_percentage(self):
        """حساب نسبة الإشغال"""
        if self.capacity > 0:
            return float(self.current_quantity / self.capacity) * 100
        return 0

    def get_available_capacity(self):
        """حساب السعة المتاحة"""
        return self.capacity - self.current_quantity

    def get_utilization_percentage(self):
        """حساب نسبة الاستخدام (نفس get_occupancy_percentage)"""
        return self.get_occupancy_percentage()

    def is_full(self):
        """التحقق من امتلاء الموقع"""
        return self.get_occupancy_percentage() >= 100

    def is_available(self):
        """التحقق من توفر الموقع"""
        return self.status == 'AVAILABLE' and not self.is_full()

    def save(self, *args, **kwargs):
        """حفظ مخصص لتحديث حالة الموقع"""
        if self.is_full():
            self.status = 'OCCUPIED'
        elif self.current_quantity == 0 and self.status == 'OCCUPIED':
            self.status = 'AVAILABLE'

        super().save(*args, **kwargs)


class StockMovement(models.Model):
    """
    نموذج حركات المخزون
    Stock Movement Model - تسجيل جميع حركات المخزون
    """

    # Movement Types - أنواع الحركات
    MOVEMENT_TYPES = [
        ('RECEIPT', 'إذن استلام'),
        ('ISSUE', 'إذن صرف'),
        ('TRANSFER_IN', 'نقل وارد'),
        ('TRANSFER_OUT', 'نقل صادر'),
        ('OPENING_BALANCE', 'رصيد افتتاحي'),
        ('DAMAGE', 'تلف'),
        ('EXPIRED', 'منتهي الصلاحية'),
    ]

    # Movement Status - حالة الحركة
    MOVEMENT_STATUS = [
        ('DRAFT', 'مسودة'),
        ('PENDING', 'في الانتظار'),
        ('APPROVED', 'معتمد'),
        ('COMPLETED', 'مكتمل'),
        ('CANCELLED', 'ملغي'),
    ]

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Movement Information
    movement_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='رقم الحركة'
    )

    movement_type = models.CharField(
        max_length=20,
        choices=MOVEMENT_TYPES,
        verbose_name='نوع الحركة'
    )

    movement_date = models.DateTimeField(
        verbose_name='تاريخ الحركة'
    )

    # Item and Location
    item = models.ForeignKey(
        ItemMaster,
        on_delete=models.CASCADE,
        related_name='movements',
        verbose_name='الصنف'
    )

    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.CASCADE,
        related_name='movements',
        verbose_name='المخزن'
    )

    bin_location = models.ForeignKey(
        BinLocation,
        on_delete=models.CASCADE,
        related_name='movements',
        verbose_name='موقع التخزين'
    )

    # Quantity Information
    quantity = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        validators=[MinValueValidator(0.001)],
        verbose_name='الكمية'
    )

    unit_of_measure = models.CharField(
        max_length=20,
        verbose_name='وحدة القياس'
    )

    # Batch Information
    batch_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='رقم الدفعة'
    )

    production_date = models.DateField(
        blank=True,
        null=True,
        verbose_name='تاريخ الإنتاج'
    )

    expiry_date = models.DateField(
        blank=True,
        null=True,
        verbose_name='تاريخ انتهاء الصلاحية'
    )

    # Reference Information
    reference_document = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='المستند المرجعي'
    )

    supplier_invoice = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='فاتورة المورد'
    )

    container_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='رقم الحاوية'
    )

    # Cost Information
    unit_cost = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        blank=True,
        null=True,
        verbose_name='تكلفة الوحدة'
    )

    total_cost = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name='إجمالي التكلفة'
    )

    # Status and Approval
    status = models.CharField(
        max_length=20,
        choices=MOVEMENT_STATUS,
        default='DRAFT',
        verbose_name='حالة الحركة'
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )

    # Tracking
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='created_movements',
        verbose_name='أنشئ بواسطة'
    )

    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='approved_movements',
        verbose_name='اعتمد بواسطة'
    )

    approved_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='تاريخ الاعتماد'
    )

    class Meta:
        verbose_name = 'حركة مخزون'
        verbose_name_plural = 'حركات المخزون'
        ordering = ['-movement_date', '-created_at']
        indexes = [
            models.Index(fields=['movement_number']),
            models.Index(fields=['movement_type']),
            models.Index(fields=['movement_date']),
            models.Index(fields=['item']),
            models.Index(fields=['warehouse']),
            models.Index(fields=['status']),
            models.Index(fields=['batch_number']),
        ]

    def __str__(self):
        return f"{self.movement_number} - {self.get_movement_type_display()}"

    def save(self, *args, **kwargs):
        """حفظ مخصص لتوليد رقم الحركة تلقائياً"""
        if not self.movement_number:
            # توليد رقم حركة تلقائي
            prefix_map = {
                'RECEIPT': 'REC',
                'ISSUE': 'ISS',
                'TRANSFER_IN': 'TIN',
                'TRANSFER_OUT': 'TOU',
                'OPENING_BALANCE': 'OPN',
                'DAMAGE': 'DMG',
                'EXPIRED': 'EXP',
            }
            prefix = prefix_map.get(self.movement_type, 'MOV')

            # البحث عن آخر رقم مستخدم
            last_movement = StockMovement.objects.filter(
                movement_number__startswith=prefix
            ).order_by('movement_number').last()

            if last_movement:
                try:
                    last_number = int(last_movement.movement_number[3:])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1

            self.movement_number = f"{prefix}{new_number:06d}"

        # حساب إجمالي التكلفة
        if self.unit_cost and self.quantity:
            self.total_cost = self.unit_cost * self.quantity

        super().save(*args, **kwargs)


class StockBalance(models.Model):
    """
    نموذج أرصدة المخزون
    Stock Balance Model - تتبع الأرصدة الحالية لكل صنف في كل موقع
    """

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Item and Location
    item = models.ForeignKey(
        ItemMaster,
        on_delete=models.CASCADE,
        related_name='stock_balances',
        verbose_name='الصنف'
    )

    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.CASCADE,
        related_name='stock_balances',
        verbose_name='المخزن'
    )

    bin_location = models.ForeignKey(
        BinLocation,
        on_delete=models.CASCADE,
        related_name='stock_balances',
        verbose_name='موقع التخزين'
    )

    # Batch Information
    batch_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='رقم الدفعة'
    )

    production_date = models.DateField(
        blank=True,
        null=True,
        verbose_name='تاريخ الإنتاج'
    )

    expiry_date = models.DateField(
        blank=True,
        null=True,
        verbose_name='تاريخ انتهاء الصلاحية'
    )

    # Quantity Information
    current_quantity = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='الكمية الحالية'
    )

    reserved_quantity = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='الكمية المحجوزة'
    )

    available_quantity = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='الكمية المتاحة'
    )

    unit_of_measure = models.CharField(
        max_length=20,
        verbose_name='وحدة القياس'
    )

    # Cost Information
    average_cost = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='متوسط التكلفة'
    )

    total_value = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='إجمالي القيمة'
    )

    # Tracking
    last_movement_date = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='تاريخ آخر حركة'
    )

    last_updated = models.DateTimeField(
        auto_now=True,
        verbose_name='آخر تحديث'
    )

    class Meta:
        verbose_name = 'رصيد مخزون'
        verbose_name_plural = 'أرصدة المخزون'
        ordering = ['item', 'warehouse', 'bin_location']
        unique_together = ['item', 'warehouse', 'bin_location', 'batch_number']
        indexes = [
            models.Index(fields=['item']),
            models.Index(fields=['warehouse']),
            models.Index(fields=['bin_location']),
            models.Index(fields=['batch_number']),
            models.Index(fields=['expiry_date']),
            models.Index(fields=['current_quantity']),
        ]

    def __str__(self):
        batch_info = f" - دفعة: {self.batch_number}" if self.batch_number else ""
        return f"{self.item.item_name_ar} - {self.warehouse.warehouse_name} - {self.bin_location.bin_code}{batch_info}"

    def is_expired(self):
        """التحقق من انتهاء الصلاحية"""
        if self.expiry_date:
            from django.utils import timezone
            return self.expiry_date <= timezone.now().date()
        return False

    def days_to_expiry(self):
        """حساب الأيام المتبقية لانتهاء الصلاحية"""
        if self.expiry_date:
            from django.utils import timezone
            delta = self.expiry_date - timezone.now().date()
            return delta.days
        return None

    def is_near_expiry(self, days=30):
        """التحقق من اقتراب انتهاء الصلاحية"""
        days_left = self.days_to_expiry()
        return days_left is not None and days_left <= days

    def save(self, *args, **kwargs):
        """حفظ مخصص لحساب الكمية المتاحة والقيمة الإجمالية ودمج الأصناف المكررة"""
        # حساب الكمية المتاحة
        self.available_quantity = self.current_quantity - self.reserved_quantity

        # حساب القيمة الإجمالية
        if self.average_cost and self.current_quantity:
            self.total_value = self.average_cost * self.current_quantity

        # التحقق من وجود أصناف مكررة قبل الحفظ
        is_new = self._state.adding

        # تجنب الدمج التلقائي إذا كان هناك معامل خاص
        skip_merge = kwargs.pop('skip_merge', False)

        super().save(*args, **kwargs)

        # دمج الأصناف المكررة بعد الحفظ (فقط للأصناف الجديدة وإذا لم يتم تجاهل الدمج)
        if is_new and not skip_merge and self.current_quantity > 0:
            self.merge_duplicate_items()

    def merge_duplicate_items(self):
        """دمج الأصناف المكررة بنفس الاسم في نفس المخزن"""
        from django.db.models import Q
        from decimal import Decimal

        # البحث عن أصناف أخرى بنفس الاسم في نفس المخزن
        duplicates = StockBalance.objects.filter(
            warehouse=self.warehouse,
            item__item_name_ar__iexact=self.item.item_name_ar.strip(),
            current_quantity__gt=0
        ).exclude(pk=self.pk).select_related('item')

        if duplicates.exists():
            total_quantity = self.current_quantity
            total_value = self.total_value or Decimal('0')
            duplicates_count = duplicates.count()

            # جمع الكميات والقيم من الأصناف المكررة
            for duplicate in duplicates:
                total_quantity += duplicate.current_quantity
                if duplicate.total_value:
                    total_value += duplicate.total_value

            # تحديث الصنف الحالي بالكميات المجمعة
            self.current_quantity = total_quantity
            self.available_quantity = total_quantity - self.reserved_quantity
            self.total_value = total_value

            # إعادة حساب متوسط التكلفة
            if total_quantity > 0 and total_value > 0:
                self.average_cost = total_value / total_quantity

            # حفظ التحديثات بدون استدعاء merge مرة أخرى
            # استخدام save مع skip_merge لتجنب استدعاء merge مرة أخرى
            self.save(skip_merge=True)

            # حذف الأصناف المكررة
            duplicates.delete()

            # تسجيل العملية في السجل
            logger = logging.getLogger(__name__)
            logger.info(
                f"تم دمج {duplicates_count} صنف مكرر للصنف {self.item.item_name_ar} "
                f"في المخزن {self.warehouse.warehouse_name}. "
                f"إجمالي الكمية: {total_quantity}"
            )


class Alert(models.Model):
    """
    نموذج التنبيهات
    Alert Model - نظام التنبيهات الشامل لجميع أنشطة المخازن
    """

    # Alert Types - أنواع التنبيهات
    ALERT_TYPES = [
        ('LOW_STOCK', 'مخزون منخفض'),
        ('OUT_OF_STOCK', 'نفاد المخزون'),
        ('NEAR_EXPIRY', 'اقتراب انتهاء الصلاحية'),
        ('EXPIRED', 'منتهي الصلاحية'),
        ('SLOW_MOVING', 'بطيء الحركة'),
        ('WAREHOUSE_FULL', 'امتلاء المخزن'),
        ('ITEM_ADDED', 'إضافة صنف جديد'),
        ('ITEM_MODIFIED', 'تعديل صنف'),
        ('ITEM_DELETED', 'حذف صنف'),
        ('RECEIPT_CREATED', 'إذن استلام جديد'),
        ('ISSUE_CREATED', 'إذن صرف جديد'),
        ('TRANSFER_REQUESTED', 'طلب نقل مخزون'),
        ('TRANSFER_APPROVED', 'اعتماد نقل مخزون'),
        ('TRANSFER_REJECTED', 'رفض نقل مخزون'),
        ('TRANSFER_COMPLETED', 'إكمال نقل مخزون'),

        ('SHIPMENT_ARRIVED', 'وصول شحنة'),
        ('DAMAGE_DETECTED', 'اكتشاف تلف'),
        ('LOCATION_CHANGED', 'تغيير موقع تخزين'),
        ('WAREHOUSE_ADDED', 'إضافة مخزن جديد'),
        ('LOCATION_STATUS_CHANGED', 'تغيير حالة موقع التخزين'),
    ]

    # Priority Levels - مستويات الأولوية
    PRIORITY_LEVELS = [
        ('LOW', 'منخفضة'),
        ('MEDIUM', 'متوسطة'),
        ('HIGH', 'عالية'),
        ('CRITICAL', 'حرجة'),
    ]

    # Alert Status - حالة التنبيه
    ALERT_STATUS = [
        ('ACTIVE', 'نشط'),
        ('ACKNOWLEDGED', 'تم الاطلاع'),
        ('RESOLVED', 'تم الحل'),
        ('DISMISSED', 'تم الإغلاق'),
    ]

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Alert Information
    alert_type = models.CharField(
        max_length=30,
        choices=ALERT_TYPES,
        verbose_name='نوع التنبيه'
    )

    title = models.CharField(
        max_length=200,
        verbose_name='عنوان التنبيه'
    )

    message = models.TextField(
        verbose_name='رسالة التنبيه'
    )

    priority = models.CharField(
        max_length=10,
        choices=PRIORITY_LEVELS,
        default='MEDIUM',
        verbose_name='مستوى الأولوية'
    )

    # Related Objects
    item = models.ForeignKey(
        ItemMaster,
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='alerts',
        verbose_name='الصنف المرتبط'
    )

    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='alerts',
        verbose_name='المخزن المرتبط'
    )

    bin_location = models.ForeignKey(
        BinLocation,
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='alerts',
        verbose_name='موقع التخزين المرتبط'
    )

    movement = models.ForeignKey(
        StockMovement,
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='alerts',
        verbose_name='الحركة المرتبطة'
    )

    # Status and Tracking
    status = models.CharField(
        max_length=15,
        choices=ALERT_STATUS,
        default='ACTIVE',
        verbose_name='حالة التنبيه'
    )

    is_read = models.BooleanField(
        default=False,
        verbose_name='تم القراءة'
    )

    # Tracking
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    acknowledged_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='تاريخ الاطلاع'
    )

    resolved_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='تاريخ الحل'
    )

    acknowledged_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='acknowledged_alerts',
        verbose_name='تم الاطلاع بواسطة'
    )

    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='resolved_alerts',
        verbose_name='تم الحل بواسطة'
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )

    class Meta:
        verbose_name = 'تنبيه'
        verbose_name_plural = 'التنبيهات'
        ordering = ['-created_at', '-priority']
        indexes = [
            models.Index(fields=['alert_type']),
            models.Index(fields=['priority']),
            models.Index(fields=['status']),
            models.Index(fields=['is_read']),
            models.Index(fields=['created_at']),
            models.Index(fields=['item']),
            models.Index(fields=['warehouse']),
        ]

    def __str__(self):
        return f"{self.title} - {self.get_priority_display()}"

    def mark_as_read(self, user=None):
        """تحديد التنبيه كمقروء"""
        self.is_read = True
        if not self.acknowledged_at:
            from django.utils import timezone
            self.acknowledged_at = timezone.now()
            self.acknowledged_by = user
            self.status = 'ACKNOWLEDGED'
        self.save()

    def resolve(self, user=None, notes=None):
        """حل التنبيه"""
        from django.utils import timezone
        self.status = 'RESOLVED'
        self.resolved_at = timezone.now()
        self.resolved_by = user
        if notes:
            self.notes = notes
        self.save()

    def dismiss(self):
        """إغلاق التنبيه"""
        self.status = 'DISMISSED'
        self.save()

    @classmethod
    def create_alert(cls, alert_type, title, message, priority='MEDIUM',
                    item=None, warehouse=None, bin_location=None, movement=None):
        """إنشاء تنبيه جديد"""
        return cls.objects.create(
            alert_type=alert_type,
            title=title,
            message=message,
            priority=priority,
            item=item,
            warehouse=warehouse,
            bin_location=bin_location,
            movement=movement
        )


class UserProfile(models.Model):
    """
    نموذج ملف المستخدم - نظام الصلاحيات البسيط
    User Profile Model - Simple Permissions System
    """

    # User Roles - أدوار المستخدمين
    USER_ROLES = [
        ('MANAGER', 'مدير المخازن'),
        ('WAREHOUSE_MANAGER', 'مسؤول مخزن'),
        ('EMPLOYEE', 'موظف'),
    ]

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # User Information
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile',
        verbose_name='المستخدم'
    )

    # Role and Permissions
    role = models.CharField(
        max_length=20,
        choices=USER_ROLES,
        default='EMPLOYEE',
        verbose_name='الدور'
    )

    # Assigned Warehouse (null for managers who can access all warehouses)
    assigned_warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='assigned_users',
        verbose_name='المخزن المخصص'
    )

    # Additional Information
    phone_number = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        verbose_name='رقم الهاتف'
    )

    department = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='القسم'
    )

    # Status and Tracking
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    class Meta:
        verbose_name = 'ملف المستخدم'
        verbose_name_plural = 'ملفات المستخدمين'
        ordering = ['user__first_name', 'user__last_name']

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.get_role_display()}"

    def can_access_warehouse(self, warehouse):
        """التحقق من إمكانية الوصول للمخزن"""
        if self.role == 'MANAGER':
            return True  # المدير يمكنه الوصول لجميع المخازن
        return self.assigned_warehouse == warehouse

    def get_accessible_warehouses(self):
        """الحصول على المخازن التي يمكن الوصول إليها"""
        if self.role == 'MANAGER':
            return Warehouse.objects.filter(is_active=True)
        elif self.assigned_warehouse:
            return Warehouse.objects.filter(id=self.assigned_warehouse.id)
        else:
            return Warehouse.objects.none()

    def has_permission(self, permission_type):
        """التحقق مما إذا كان المستخدم لديه صلاحية معينة"""
        # المدير لديه جميع الصلاحيات
        if self.user.username == 'admin':
            return True

        # للمديرين العاديين
        if self.role == 'MANAGER':
            # إذا كانت صلاحية إدارة المستخدمين لا يمكن منحها لغير المدير العام
            if permission_type in ['MANAGE_USERS', 'DELETE_USERS', 'EDIT_USER_PERMISSIONS']:
                return False
            return True

        # للمديرين المخازن
        if self.role == 'WAREHOUSE_MANAGER':
            if permission_type in ['VIEW_REPORTS', 'VIEW_WAREHOUSE', 'GOODS_RECEIPT', 'GOODS_ISSUE']:
                return True
            return UserPermission.objects.filter(user_profile=self, permission_type=permission_type, is_granted=True).exists()

        # للموظفين
        return UserPermission.objects.filter(user_profile=self, permission_type=permission_type, is_granted=True).exists()

    @classmethod
    def get_user_warehouse(cls, user):
        """دالة للحصول على مخزن المستخدم"""
        try:
            profile = cls.objects.get(user=user)
            return profile.assigned_warehouse
        except cls.DoesNotExist:
            return None

    @classmethod
    def get_user_role(cls, user):
        """دالة للحصول على دور المستخدم"""
        try:
            profile = cls.objects.get(user=user)
            return profile.role
        except cls.DoesNotExist:
            return 'EMPLOYEE'


class UserPermission(models.Model):
    """
    نموذج صلاحيات المستخدم
    User Permission Model - نظام الصلاحيات التفصيلي
    """
    # Permission Types - أنواع الصلاحيات
    PERMISSION_TYPES = [
        # المخزون
        ('VIEW_INVENTORY', 'عرض المخزون'),
        ('ADD_ITEMS', 'إضافة أصناف'),
        ('EDIT_ITEMS', 'تعديل أصناف'),
        ('DELETE_ITEMS', 'حذف أصناف'),
        
        # المخازن
        ('VIEW_WAREHOUSE', 'عرض المخازن'),
        ('ADD_WAREHOUSE', 'إضافة مخزن'),
        ('EDIT_WAREHOUSE', 'تعديل مخزن'),
        ('DELETE_WAREHOUSE', 'حذف مخزن'),
        
        # الحركات
        ('GOODS_RECEIPT', 'إذن استلام'),
        ('GOODS_ISSUE', 'إذن صرف'),
        ('STOCK_TRANSFER', 'نقل مخزون'),
        
        # التقارير
        ('VIEW_REPORTS', 'عرض التقارير'),
        ('EXPORT_REPORTS', 'تصدير التقارير'),
        
        # المستخدمين
        ('MANAGE_USERS', 'إدارة المستخدمين'),
        ('EDIT_USER_PERMISSIONS', 'تعديل صلاحيات المستخدمين'),
        ('DELETE_USERS', 'حذف المستخدمين'),
    ]

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # User Profile
    user_profile = models.ForeignKey(
        UserProfile,
        on_delete=models.CASCADE,
        related_name='permissions',
        verbose_name='ملف المستخدم'
    )

    # Permission Type
    permission_type = models.CharField(
        max_length=50,
        choices=PERMISSION_TYPES,
        verbose_name='نوع الصلاحية'
    )

    # Permission Status
    is_granted = models.BooleanField(
        default=True,
        verbose_name='ممنوحة'
    )

    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    # Who granted this permission
    granted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='granted_permissions',
        verbose_name='منحت بواسطة'
    )

    class Meta:
        verbose_name = 'صلاحية مستخدم'
        verbose_name_plural = 'صلاحيات المستخدمين'
        unique_together = ['user_profile', 'permission_type']

    def __str__(self):
        return f"{self.user_profile.user.username} - {self.get_permission_type_display()}: {'ممنوحة' if self.is_granted else 'غير ممنوحة'}"



class GoodsReceipt(models.Model):
    """
    نموذج إذن الاستلام
    Goods Receipt Model
    """

    # Receipt Status - حالة الإذن
    RECEIPT_STATUS = [
        ('DRAFT', 'مسودة'),
        ('CONFIRMED', 'مؤكد'),
        ('COMPLETED', 'مكتمل'),
        ('CANCELLED', 'ملغي'),
    ]

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Receipt Information
    receipt_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='رقم الإذن'
    )

    receipt_date = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإذن'
    )

    # Warehouse and User
    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.PROTECT,
        related_name='goods_receipts',
        verbose_name='المخزن'
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_receipts',
        verbose_name='أنشئ بواسطة'
    )

    # Status and Notes
    status = models.CharField(
        max_length=20,
        choices=RECEIPT_STATUS,
        default='DRAFT',
        verbose_name='الحالة'
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )

    # Totals
    total_items = models.PositiveIntegerField(
        default=0,
        verbose_name='عدد الأصناف'
    )

    total_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=3,
        default=Decimal('0.000'),
        verbose_name='إجمالي الكمية'
    )

    # Tracking
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    class Meta:
        verbose_name = 'إذن استلام'
        verbose_name_plural = 'إيصالات الاستلام'
        ordering = ['-receipt_date']
        indexes = [
            models.Index(fields=['receipt_number']),
            models.Index(fields=['warehouse']),
            models.Index(fields=['status']),
            models.Index(fields=['receipt_date']),
        ]

    def __str__(self):
        return f"إذن استلام {self.receipt_number} - {self.warehouse.warehouse_name}"

    def save(self, *args, **kwargs):
        if not self.receipt_number:
            self.receipt_number = self.generate_receipt_number()
        super().save(*args, **kwargs)

    def generate_receipt_number(self):
        """توليد رقم إذن تلقائي"""
        from datetime import datetime
        now = datetime.now()
        year = now.year
        month = now.month

        # البحث عن آخر رقم في نفس الشهر
        last_receipt = GoodsReceipt.objects.filter(
            receipt_number__startswith=f'GR-{year}-{month:02d}'
        ).order_by('-receipt_number').first()

        if last_receipt:
            # استخراج الرقم التسلسلي من آخر إذن
            last_number = int(last_receipt.receipt_number.split('-')[-1])
            next_number = last_number + 1
        else:
            next_number = 1

        return f'GR-{year}-{month:02d}-{next_number:04d}'

    def calculate_totals(self):
        """حساب المجاميع"""
        items = self.receipt_items.all()
        self.total_items = items.count()
        self.total_quantity = sum(item.quantity for item in items)
        self.save(update_fields=['total_items', 'total_quantity'])

    def confirm_receipt(self):
        """تأكيد الإذن وتحديث المخزون"""
        if self.status != 'DRAFT':
            return False

        # تحديث حالة الإذن
        self.status = 'CONFIRMED'
        self.save()

        # تحديث أرصدة المخزون
        for item in self.receipt_items.all():
            item.update_stock_balance()

        return True


class GoodsReceiptItem(models.Model):
    """
    نموذج أصناف إذن الاستلام
    Goods Receipt Item Model
    """

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Receipt Reference
    receipt = models.ForeignKey(
        GoodsReceipt,
        on_delete=models.CASCADE,
        related_name='receipt_items',
        verbose_name='إذن الاستلام'
    )

    # Item Information
    item = models.ForeignKey(
        ItemMaster,
        on_delete=models.PROTECT,
        related_name='receipt_items',
        verbose_name='الصنف'
    )

    # Quantity and Location
    quantity = models.DecimalField(
        max_digits=15,
        decimal_places=3,
        verbose_name='الكمية'
    )

    bin_location = models.ForeignKey(
        BinLocation,
        on_delete=models.PROTECT,
        related_name='receipt_items',
        verbose_name='موقع التخزين'
    )

    # Batch Information (optional)
    batch_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='رقم الدفعة'
    )

    expiry_date = models.DateField(
        blank=True,
        null=True,
        verbose_name='تاريخ انتهاء الصلاحية'
    )

    # Unit Cost (optional)
    unit_cost = models.DecimalField(
        max_digits=15,
        decimal_places=3,
        blank=True,
        null=True,
        verbose_name='تكلفة الوحدة'
    )

    total_cost = models.DecimalField(
        max_digits=15,
        decimal_places=3,
        blank=True,
        null=True,
        verbose_name='إجمالي التكلفة'
    )

    # Notes
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )

    # Tracking
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'صنف إذن الاستلام'
        verbose_name_plural = 'أصناف إيصالات الاستلام'
        ordering = ['item__item_name_ar']
        indexes = [
            models.Index(fields=['receipt']),
            models.Index(fields=['item']),
            models.Index(fields=['bin_location']),
            models.Index(fields=['batch_number']),
        ]

    def __str__(self):
        return f"{self.item.item_name_ar} - {self.quantity} {self.item.unit_of_measure}"

    def save(self, *args, **kwargs):
        # حساب إجمالي التكلفة
        if self.unit_cost:
            self.total_cost = self.quantity * self.unit_cost
        super().save(*args, **kwargs)

        # تحديث مجاميع الإذن
        self.receipt.calculate_totals()

    def update_stock_balance(self):
        """تحديث رصيد المخزون"""
        # البحث عن رصيد موجود أو إنشاء جديد
        stock_balance, created = StockBalance.objects.get_or_create(
            item=self.item,
            warehouse=self.receipt.warehouse,
            bin_location=self.bin_location,
            batch_number=self.batch_number or '',
            defaults={
                'current_quantity': Decimal('0.000'),
                'reserved_quantity': Decimal('0.000'),
                'available_quantity': Decimal('0.000'),
                'average_cost': self.unit_cost or Decimal('0.000'),
                'total_value': Decimal('0.000'),
                'expiry_date': self.expiry_date,
                'unit_of_measure': self.item.unit_of_measure,
            }
        )

        # تحديث الكميات
        stock_balance.current_quantity += self.quantity
        stock_balance.available_quantity = stock_balance.current_quantity - stock_balance.reserved_quantity

        # تحديث التكلفة والقيمة
        if self.unit_cost:
            stock_balance.average_cost = self.unit_cost
        stock_balance.total_value = stock_balance.current_quantity * stock_balance.average_cost

        stock_balance.save()

        # تسجيل حركة المخزون
        from django.utils import timezone
        import uuid
        movement_number = f'MOV-{timezone.now().strftime("%Y%m%d%H%M%S")}-{str(uuid.uuid4())[:8]}'
        StockMovement.objects.create(
            movement_number=movement_number,
            movement_type='RECEIPT',
            movement_date=timezone.now(),
            item=self.item,
            warehouse=self.receipt.warehouse,
            bin_location=self.bin_location,
            quantity=self.quantity,
            unit_of_measure=self.item.unit_of_measure,
            batch_number=self.batch_number,
            expiry_date=self.expiry_date,
            reference_document=self.receipt.receipt_number,
            unit_cost=self.unit_cost,
            total_cost=self.total_cost,
            notes=f'استلام - إذن رقم {self.receipt.receipt_number}',
            created_by=self.receipt.created_by
        )


class GoodsIssue(models.Model):
    """
    نموذج إذن الصرف
    Goods Issue Model
    """

    # Issue Types - أنواع الصرف
    ISSUE_TYPES = [
        ('SALE', 'بيع'),
        ('DAMAGE', 'تلف'),
    ]

    # Issue Status - حالة الإذن
    ISSUE_STATUS = [
        ('DRAFT', 'مسودة'),
        ('CONFIRMED', 'مؤكد'),
        ('COMPLETED', 'مكتمل'),
        ('CANCELLED', 'ملغي'),
    ]

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Issue Information
    issue_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='رقم الإذن'
    )

    issue_type = models.CharField(
        max_length=20,
        choices=ISSUE_TYPES,
        verbose_name='نوع الصرف'
    )

    issue_date = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإذن'
    )

    # Warehouse and User
    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.PROTECT,
        related_name='goods_issues',
        verbose_name='المخزن'
    )

    created_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='created_issues',
        verbose_name='أنشئ بواسطة'
    )

    # Status and Notes
    status = models.CharField(
        max_length=20,
        choices=ISSUE_STATUS,
        default='DRAFT',
        verbose_name='الحالة'
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )

    # Totals
    total_items = models.PositiveIntegerField(
        default=0,
        verbose_name='عدد الأصناف'
    )

    total_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=3,
        default=0,
        verbose_name='إجمالي الكمية'
    )

    # Tracking
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    class Meta:
        verbose_name = 'إذن صرف'
        verbose_name_plural = 'أذون الصرف'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['issue_number']),
            models.Index(fields=['issue_type']),
            models.Index(fields=['issue_date']),
            models.Index(fields=['warehouse']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.issue_number} - {self.get_issue_type_display()}"

    def save(self, *args, **kwargs):
        """حفظ مخصص لتوليد رقم الإذن"""
        if not self.issue_number:
            from django.utils import timezone
            now = timezone.now()
            prefix = 'SALE' if self.issue_type == 'SALE' else 'DMG'
            count = GoodsIssue.objects.filter(
                issue_type=self.issue_type,
                created_at__year=now.year,
                created_at__month=now.month
            ).count() + 1
            self.issue_number = f'{prefix}-{now.strftime("%Y%m")}-{count:04d}'
        super().save(*args, **kwargs)

    def confirm_issue(self):
        """تأكيد الإذن وتحديث المخزون"""
        if self.status != 'DRAFT':
            return False

        # تحديث حالة الإذن
        self.status = 'CONFIRMED'
        self.save()

        # تحديث المخزون لكل صنف
        for item in self.issue_items.all():
            item.update_stock()

        return True

    def get_total_value(self):
        """حساب إجمالي قيمة الإذن"""
        return sum(item.total_cost or 0 for item in self.issue_items.all())


class GoodsIssueItem(models.Model):
    """
    نموذج أصناف إذن الصرف
    Goods Issue Item Model
    """

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Issue Reference
    issue = models.ForeignKey(
        GoodsIssue,
        on_delete=models.CASCADE,
        related_name='issue_items',
        verbose_name='إذن الصرف'
    )

    # Item Information
    item = models.ForeignKey(
        ItemMaster,
        on_delete=models.PROTECT,
        related_name='issue_items',
        verbose_name='الصنف'
    )

    # Location
    bin_location = models.ForeignKey(
        BinLocation,
        on_delete=models.PROTECT,
        related_name='issue_items',
        blank=True,
        null=True,
        verbose_name='موقع التخزين'
    )

    # Quantity Information
    quantity = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        validators=[MinValueValidator(0.001)],
        verbose_name='الكمية'
    )

    unit_of_measure = models.CharField(
        max_length=20,
        verbose_name='وحدة القياس'
    )

    # Cost Information
    unit_cost = models.DecimalField(
        max_digits=12,
        decimal_places=4,
        blank=True,
        null=True,
        verbose_name='تكلفة الوحدة'
    )

    total_cost = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name='إجمالي التكلفة'
    )

    # Tracking
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'صنف إذن الصرف'
        verbose_name_plural = 'أصناف أذون الصرف'
        ordering = ['created_at']

    def __str__(self):
        return f"{self.issue.issue_number} - {self.item.item_name_ar}"

    def save(self, *args, **kwargs):
        """حفظ مخصص لحساب التكلفة الإجمالية"""
        # نسخ وحدة القياس من الصنف
        if not self.unit_of_measure:
            self.unit_of_measure = self.item.unit_of_measure

        # حساب التكلفة الإجمالية
        if self.unit_cost and self.quantity:
            self.total_cost = self.unit_cost * self.quantity

        super().save(*args, **kwargs)

    def update_stock(self):
        """تحديث المخزون عند تأكيد الصرف"""

        # البحث عن الرصيد المناسب
        if self.bin_location:
            # إذا تم تحديد موقع تخزين محدد
            stock_balance = StockBalance.objects.filter(
                item=self.item,
                warehouse=self.issue.warehouse,
                bin_location=self.bin_location
            ).first()
        else:
            # البحث عن أي رصيد متاح للصنف في المخزن
            stock_balance = StockBalance.objects.filter(
                item=self.item,
                warehouse=self.issue.warehouse,
                available_quantity__gt=0
            ).first()

        if not stock_balance:
            raise ValueError(f'لا يوجد رصيد متاح للصنف {self.item.item_name_ar}')

        if stock_balance.available_quantity < self.quantity:
            raise ValueError(f'الكمية المتاحة غير كافية للصنف {self.item.item_name_ar} - المتاح: {stock_balance.available_quantity}, المطلوب: {self.quantity}')

        # تحديث الرصيد
        stock_balance.current_quantity -= self.quantity
        stock_balance.available_quantity -= self.quantity

        # تحديث القيمة الإجمالية
        if stock_balance.average_cost:
            stock_balance.total_value = stock_balance.average_cost * stock_balance.current_quantity

        # تسجيل حركة المخزون
        from django.utils import timezone
        import uuid

        stock_balance.last_movement_date = timezone.now()
        stock_balance.save()
        movement_number = f'MOV-{timezone.now().strftime("%Y%m%d%H%M%S")}-{str(uuid.uuid4())[:8]}'
        StockMovement.objects.create(
            movement_number=movement_number,
            movement_type='ISSUE',
            movement_date=timezone.now(),
            item=self.item,
            warehouse=self.issue.warehouse,
            bin_location=self.bin_location or stock_balance.bin_location,
            quantity=self.quantity,
            unit_of_measure=self.item.unit_of_measure,
            reference_document=self.issue.issue_number,
            unit_cost=self.unit_cost,
            total_cost=self.total_cost,
            notes=f'صرف {self.issue.get_issue_type_display()} - إذن رقم {self.issue.issue_number}',
            created_by=self.issue.created_by
        )


class StockTransfer(models.Model):
    """
    نموذج طلبات نقل المخزون
    Stock Transfer Request Model
    """

    # Transfer Status - حالة الطلب
    TRANSFER_STATUS = [
        ('PENDING', 'في الانتظار'),
        ('APPROVED', 'معتمد'),
        ('REJECTED', 'مرفوض'),
        ('COMPLETED', 'مكتمل'),
        ('CANCELLED', 'ملغي'),
    ]

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Transfer Information
    transfer_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='رقم طلب النقل'
    )

    transfer_date = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الطلب'
    )

    # Source and Destination
    source_warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.PROTECT,
        related_name='outgoing_transfers',
        verbose_name='المخزن المصدر'
    )

    # المخزن الهدف - يحدده مدير المخازن عند الموافقة
    destination_warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.PROTECT,
        related_name='incoming_transfers',
        blank=True,
        null=True,
        verbose_name='المخزن الهدف'
    )



    # Request Information
    reason = models.CharField(
        max_length=200,
        verbose_name='سبب النقل'
    )

    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )

    # Status and Approval
    status = models.CharField(
        max_length=20,
        choices=TRANSFER_STATUS,
        default='PENDING',
        verbose_name='حالة الطلب'
    )

    # Users
    requested_by = models.ForeignKey(
        User,
        on_delete=models.PROTECT,
        related_name='requested_transfers',
        verbose_name='طلب بواسطة'
    )

    approved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='approved_transfers',
        verbose_name='اعتمد بواسطة'
    )

    # Dates
    approved_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='تاريخ الاعتماد'
    )

    completed_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='تاريخ الإكمال'
    )

    # Approval Notes
    approval_notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات الاعتماد'
    )

    # Tracking
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    completed_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='تاريخ الإكمال'
    )

    class Meta:
        verbose_name = 'طلب نقل مخزون'
        verbose_name_plural = 'طلبات نقل المخزون'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['transfer_number']),
            models.Index(fields=['status']),
            models.Index(fields=['source_warehouse']),
            models.Index(fields=['destination_warehouse']),
            models.Index(fields=['requested_by']),
            models.Index(fields=['transfer_date']),
        ]

    def __str__(self):
        dest_name = self.destination_warehouse.warehouse_name if self.destination_warehouse else "غير محدد"
        return f"{self.transfer_number} - {self.source_warehouse.warehouse_name} → {dest_name}"

    def save(self, *args, **kwargs):
        """حفظ مخصص لتوليد رقم الطلب تلقائياً"""
        if not self.transfer_number:
            from django.utils import timezone
            now = timezone.now()
            count = StockTransfer.objects.filter(
                created_at__year=now.year,
                created_at__month=now.month
            ).count() + 1
            self.transfer_number = f'ST-{now.strftime("%Y%m")}-{count:04d}'
        super().save(*args, **kwargs)

    def can_be_approved(self):
        """التحقق من إمكانية اعتماد الطلب"""
        return self.status == 'PENDING'

    def can_be_cancelled(self):
        """التحقق من إمكانية إلغاء الطلب"""
        return self.status in ['PENDING', 'APPROVED']

    def approve(self, approved_by, destination_warehouse, notes=None):
        """اعتماد الطلب مع تحديد المخزن الهدف"""
        if not self.can_be_approved():
            return False

        from django.utils import timezone
        self.status = 'APPROVED'
        self.approved_by = approved_by
        self.approved_at = timezone.now()
        self.destination_warehouse = destination_warehouse
        if notes:
            self.approval_notes = notes
        self.save()

        # تحديث أرصدة المخزون في كلا المخزنين عند الاعتماد
        for item in self.transfer_items.all():
            item.update_stock_on_approval()

        # إنشاء تنبيه للمسؤول المطلوب منه
        items_count = self.transfer_items.count()
        items_text = f"{items_count} صنف" if items_count > 1 else "صنف واحد"

        Alert.create_alert(
            alert_type='TRANSFER_APPROVED',
            title=f'تم اعتماد طلب النقل {self.transfer_number}',
            message=f'تم اعتماد طلب نقل {items_text} من {self.source_warehouse.warehouse_name} إلى {self.destination_warehouse.warehouse_name}',
            priority='MEDIUM',
            warehouse=self.source_warehouse
        )

        return True

    def reject(self, rejected_by, notes=None):
        """رفض الطلب"""
        if not self.can_be_approved():
            return False

        from django.utils import timezone
        self.status = 'REJECTED'
        self.approved_by = rejected_by
        self.approved_at = timezone.now()
        if notes:
            self.approval_notes = notes
        self.save()

        # إنشاء تنبيه للمسؤول المطلوب منه
        items_count = self.transfer_items.count()
        items_text = f"{items_count} صنف" if items_count > 1 else "صنف واحد"

        Alert.create_alert(
            alert_type='TRANSFER_REJECTED',
            title=f'تم رفض طلب النقل {self.transfer_number}',
            message=f'تم رفض طلب نقل {items_text} من {self.source_warehouse.warehouse_name}. السبب: {notes or "غير محدد"}',
            priority='HIGH',
            warehouse=self.source_warehouse
        )

        return True

    def complete_transfer(self):
        """إكمال عملية النقل وتحديث المخزون لجميع الأصناف"""
        if self.status != 'APPROVED' or not self.destination_warehouse:
            return False

        from django.utils import timezone

        # التحقق من توفر جميع الكميات المطلوبة
        for item in self.transfer_items.all():
            if not item.validate_quantity():
                return False

        # تحديث المخزون لكل صنف
        for item in self.transfer_items.all():
            if not item.update_stock_on_completion():
                return False

        # تحديث حالة الطلب
        self.status = 'COMPLETED'
        self.completed_at = timezone.now()
        self.save()

        # إنشاء تنبيه بإكمال النقل
        items_count = self.transfer_items.count()
        items_text = f"{items_count} صنف" if items_count > 1 else "صنف واحد"

        Alert.create_alert(
            alert_type='TRANSFER_COMPLETED',
            title=f'تم إكمال نقل المخزون {self.transfer_number}',
            message=f'تم إكمال نقل {items_text} من {self.source_warehouse.warehouse_name} إلى {self.destination_warehouse.warehouse_name}',
            priority='MEDIUM',
            warehouse=self.destination_warehouse
        )

        return True

    def get_total_items_count(self):
        """الحصول على عدد الأصناف في الطلب"""
        return self.transfer_items.count()

    def get_items_summary(self):
        """الحصول على ملخص الأصناف"""
        items = []
        for item in self.transfer_items.all():
            items.append(f"{item.item.item_name_ar} ({item.quantity} {item.item.unit_of_measure})")
        return items


class StockTransferItem(models.Model):
    """
    نموذج أصناف طلب نقل المخزون
    Stock Transfer Item Model
    """

    # Primary Key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)

    # Transfer Reference
    transfer = models.ForeignKey(
        StockTransfer,
        on_delete=models.CASCADE,
        related_name='transfer_items',
        verbose_name='طلب النقل'
    )

    # Item and Quantity
    item = models.ForeignKey(
        ItemMaster,
        on_delete=models.PROTECT,
        related_name='transfer_items',
        verbose_name='الصنف'
    )

    quantity = models.DecimalField(
        max_digits=15,
        decimal_places=3,
        validators=[MinValueValidator(0.001)],
        verbose_name='الكمية المطلوبة'
    )

    # Source Location (optional)
    source_bin_location = models.ForeignKey(
        BinLocation,
        on_delete=models.PROTECT,
        related_name='outgoing_transfer_items',
        blank=True,
        null=True,
        verbose_name='موقع التخزين المصدر'
    )

    # Destination Location (optional) - يحدده المدير عند الموافقة
    destination_bin_location = models.ForeignKey(
        BinLocation,
        on_delete=models.PROTECT,
        related_name='incoming_transfer_items',
        blank=True,
        null=True,
        verbose_name='موقع التخزين الهدف'
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')

    class Meta:
        verbose_name = 'صنف طلب النقل'
        verbose_name_plural = 'أصناف طلبات النقل'
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['transfer']),
            models.Index(fields=['item']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.transfer.transfer_number} - {self.item.item_name_ar} ({self.quantity})"

    def get_available_quantity(self):
        """الحصول على الكمية المتاحة في المخزن الذي سيتم النقل منه (الذي سيختاره المدير)"""
        try:
            # Since we don't know which warehouse the manager will select yet,
            # we can check all warehouses for the maximum available quantity
            # This can be refined once the destination warehouse is selected
            balances = StockBalance.objects.filter(
                item=self.item,
            ).exclude(
                warehouse=self.transfer.source_warehouse  # Exclude source warehouse (which will receive)
            ).order_by('-current_quantity')
            
            if balances.exists():
                return balances.first().current_quantity
            return Decimal('0.000')
        except StockBalance.DoesNotExist:
            return Decimal('0.000')

    def validate_quantity(self):
        """التحقق من توفر الكمية المطلوبة في المخزن الهدف (الذي سيختاره المدير)"""
        # In this modified flow, we'll perform validation later when the manager selects the source warehouse
        # For now, we'll assume the quantity is available to allow the transfer request to be created
        # The actual validation will happen when the manager selects the warehouse during approval
        return True

    def update_stock_on_completion(self):
        """تحديث أرصدة المخزون عند إكمال النقل - لا حاجة لها حاليًا لأنها تمت بالفعل عند الاعتماد"""
        # تم نقل منطق التحديث إلى update_stock_on_approval
        return True

    def update_stock_on_completion(self):
        """تحديث أرصدة المخزون عند إكمال النقل"""
        if not self.transfer.destination_warehouse:
            return False

        # Get default bin location for source warehouse if none specified
        source_bin = self.source_bin_location
        if not source_bin:
            # Try to get the first available bin location for the source warehouse
            source_bin = BinLocation.objects.filter(warehouse=self.transfer.source_warehouse).first()
            if not source_bin:
                # Create a default bin location if none exists
                source_bin = BinLocation.objects.create(
                    warehouse=self.transfer.source_warehouse,
                    bin_code='DEFAULT',
                    bin_name='الموقع الافتراضي',
                    is_active=True
                )

        # Get default bin location for destination warehouse if none specified
        dest_bin = self.destination_bin_location
        if not dest_bin:
            # Try to get the first available bin location for the destination warehouse
            dest_bin = BinLocation.objects.filter(warehouse=self.transfer.destination_warehouse).first()
            if not dest_bin:
                # Create a default bin location if none exists
                dest_bin = BinLocation.objects.create(
                    warehouse=self.transfer.destination_warehouse,
                    bin_code='DEFAULT',
                    bin_name='الموقع الافتراضي',
                    is_active=True
                )

        # تقليل الرصيد من المخزن المصدر
        source_balance, created = StockBalance.objects.get_or_create(
            warehouse=self.transfer.source_warehouse,
            item=self.item,
            bin_location=source_bin,
            defaults={'current_quantity': Decimal('0.000')}
        )
        source_balance.current_quantity -= self.quantity
        source_balance.save()

        # زيادة الرصيد في المخزن الهدف
        dest_balance, created = StockBalance.objects.get_or_create(
            warehouse=self.transfer.destination_warehouse,
            item=self.item,
            bin_location=dest_bin,
            defaults={'current_quantity': Decimal('0.000')}
        )
        dest_balance.current_quantity += self.quantity
        dest_balance.save()

        # Get current timestamp for consistent movement numbers
        now = timezone.now()
        timestamp = now.strftime('%Y%m%d%H%M%S')
        
        # تسجيل حركة الصرف من المخزن المصدر
        StockMovement.objects.create(
            movement_number=f'TOUT-{self.transfer.transfer_number}-{timestamp}',
            warehouse=self.transfer.source_warehouse,
            item=self.item,
            bin_location=source_bin,
            movement_type='TRANSFER_OUT',
            quantity=self.quantity,
            reference_document=f'TRANSFER:{self.transfer.transfer_number}',
            movement_date=now,
            unit_of_measure=self.item.unit_of_measure,
            status='COMPLETED',
            notes=f'نقل إلى {self.transfer.destination_warehouse.warehouse_name} - {self.transfer.reason}'
        )

        # تسجيل حركة الوارد للمخزن الهدف
        StockMovement.objects.create(
            movement_number=f'TIN-{self.transfer.transfer_number}-{timestamp}',
            warehouse=self.transfer.destination_warehouse,
            item=self.item,
            bin_location=dest_bin,
            movement_type='TRANSFER_IN',
            quantity=self.quantity,
            reference_document=f'TRANSFER:{self.transfer.transfer_number}',
            movement_date=now,
            unit_of_measure=self.item.unit_of_measure,
            status='COMPLETED',
            notes=f'نقل من {self.transfer.source_warehouse.warehouse_name} - {self.transfer.reason}'
        )

        return True

    def update_stock_on_approval(self):
        """تحديث أرصدة المخزون عند اعتماد النقل"""
        if not self.transfer.destination_warehouse:
            return False
            
        # Get default bin location for source warehouse if none specified
        source_bin = self.source_bin_location
        if not source_bin:
            # Try to get the first available bin location for the source warehouse
            source_bin = BinLocation.objects.filter(warehouse=self.transfer.source_warehouse).first()
            if not source_bin:
                # Create a default bin location if none exists
                source_bin = BinLocation.objects.create(
                    warehouse=self.transfer.source_warehouse,
                    bin_code='DEFAULT',
                    bin_name='الموقع الافتراضي',
                    is_active=True
                )

        # Get default bin location for destination warehouse if none specified
        dest_bin = self.destination_bin_location
        if not dest_bin:
            # Try to get the first available bin location for the destination warehouse
            dest_bin = BinLocation.objects.filter(warehouse=self.transfer.destination_warehouse).first()
            if not dest_bin:
                # Create a default bin location if none exists
                dest_bin = BinLocation.objects.create(
                    warehouse=self.transfer.destination_warehouse,
                    bin_code='DEFAULT',
                    bin_name='الموقع الافتراضي',
                    is_active=True
                )

        # زيادة الرصيد في المخزن المصدر (الذي طلب النقل) - عند وجود الصنف، زيادة الكمية وليس إضافة جديدة
        source_balance, created = StockBalance.objects.get_or_create(
            warehouse=self.transfer.source_warehouse,
            item=self.item,
            bin_location=source_bin,
            defaults={'current_quantity': Decimal('0.000')}
        )
        source_balance.current_quantity += self.quantity
        source_balance.save()

        # تقليل الرصيد من المخزن الهدف (الذي اختاره المدير)
        dest_balance, created = StockBalance.objects.get_or_create(
            warehouse=self.transfer.destination_warehouse,
            item=self.item,
            bin_location=dest_bin,
            defaults={'current_quantity': self.quantity + Decimal('0.000')}
        )
        if not created:
            # إذا كان الصنف موجوداً بالفعل، يتم تقليل الكمية منه
            dest_balance.current_quantity -= self.quantity
            dest_balance.save()

        # Get current timestamp for consistent movement numbers
        now = timezone.now()
        timestamp = now.strftime('%Y%m%d%H%M%S')
        
        # تسجيل حركة الوارد للمخزن المصدر (الذي طلب النقل) - مقدم الطلب
        StockMovement.objects.create(
            movement_number=f'TIN-{self.transfer.transfer_number}-{timestamp}',
            warehouse=self.transfer.source_warehouse,
            item=self.item,
            bin_location=source_bin,
            movement_type='TRANSFER_IN',
            quantity=self.quantity,
            reference_document=f'TRANSFER:{self.transfer.transfer_number}',
            movement_date=now,
            unit_of_measure=self.item.unit_of_measure,
            status='COMPLETED',
            notes=f'نقل من {self.transfer.destination_warehouse.warehouse_name} - {self.transfer.reason}'
        )

        # تسجيل حركة الصرف من المخزن الهدف (الذي اختاره المدير)
        StockMovement.objects.create(
            movement_number=f'TOUT-{self.transfer.transfer_number}-{timestamp}',
            warehouse=self.transfer.destination_warehouse,
            item=self.item,
            bin_location=dest_bin,
            movement_type='TRANSFER_OUT',
            quantity=self.quantity,
            reference_document=f'TRANSFER:{self.transfer.transfer_number}',
            movement_date=now,
            unit_of_measure=self.item.unit_of_measure,
            status='COMPLETED',
            notes=f'نقل إلى {self.transfer.source_warehouse.warehouse_name} - {self.transfer.reason}'
        )

        return True
