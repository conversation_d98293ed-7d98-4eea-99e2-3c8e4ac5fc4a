from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import ItemMaster, Warehouse, BinLocation, StockMovement, StockBalance, Alert, UserProfile, GoodsReceipt, GoodsReceiptItem, GoodsIssue, GoodsIssueItem, StockTransfer, StockTransferItem


@admin.register(ItemMaster)
class ItemMasterAdmin(admin.ModelAdmin):
    """
    إدارة الأصناف في Django Admin
    """
    list_display = [
        'item_code', 'item_name_ar', 'category', 'material_type',
        'unit_of_measure', 'minimum_stock_level', 'hazard_level',
        'is_active', 'created_at'
    ]

    list_filter = [
        'category', 'material_type', 'unit_of_measure',
        'hazard_level', 'is_active', 'created_at'
    ]

    search_fields = [
        'item_code', 'item_name_ar', 'item_name_en',
        'chemical_formula', 'cas_number'
    ]

    readonly_fields = ['item_code', 'created_at', 'updated_at']

    fieldsets = (
        ('معلومات أساسية', {
            'fields': (
                'item_code', 'item_name_ar', 'item_name_en',
                'category', 'material_type'
            )
        }),
        ('معلومات المخزون', {
            'fields': (
                'unit_of_measure', 'default_warehouse', 'default_bin_location',
                'minimum_stock_level', 'reorder_point'
            )
        }),
        ('الخصائص الفيزيائية', {
            'fields': (
                'weight_per_unit', 'density', 'color'
            ),
            'classes': ('collapse',)
        }),
        ('الخصائص الكيميائية', {
            'fields': (
                'chemical_formula', 'cas_number', 'purity_percentage'
            ),
            'classes': ('collapse',)
        }),
        ('معلومات الأمان', {
            'fields': (
                'hazard_level', 'safety_data_sheet'
            )
        }),
        ('شروط التخزين', {
            'fields': (
                'storage_temperature_min', 'storage_temperature_max',
                'humidity_max', 'shelf_life_months'
            ),
            'classes': ('collapse',)
        }),
        ('معلومات إضافية', {
            'fields': (
                'item_image', 'barcode', 'supplier_item_code', 'notes'
            ),
            'classes': ('collapse',)
        }),
        ('حالة وتتبع', {
            'fields': (
                'is_active', 'created_at', 'updated_at', 'created_by'
            )
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(Warehouse)
class WarehouseAdmin(admin.ModelAdmin):
    """
    إدارة المخازن في Django Admin
    """
    list_display = [
        'warehouse_code', 'warehouse_name', 'warehouse_type',
        'city', 'warehouse_manager', 'get_occupancy_display',
        'is_active', 'created_at'
    ]

    list_filter = [
        'warehouse_type', 'city', 'governorate',
        'temperature_controlled', 'humidity_controlled',
        'is_active', 'created_at'
    ]

    search_fields = [
        'warehouse_code', 'warehouse_name', 'city',
        'warehouse_manager', 'manager_phone'
    ]

    readonly_fields = ['created_at', 'updated_at', 'get_occupancy_display']

    fieldsets = (
        ('معلومات أساسية', {
            'fields': (
                'warehouse_code', 'warehouse_name', 'warehouse_type'
            )
        }),
        ('معلومات الموقع', {
            'fields': (
                'address', 'city', 'governorate', 'postal_code'
            )
        }),
        ('معلومات السعة', {
            'fields': (
                'total_capacity', 'current_occupancy', 'get_occupancy_display'
            )
        }),
        ('التحكم البيئي', {
            'fields': (
                'temperature_controlled', 'humidity_controlled'
            )
        }),
        ('معلومات الإدارة', {
            'fields': (
                'warehouse_manager', 'manager_phone', 'manager_email'
            )
        }),
        ('حالة وتتبع', {
            'fields': (
                'is_active', 'created_at', 'updated_at', 'created_by'
            )
        }),
    )

    def get_occupancy_display(self, obj):
        """عرض نسبة الإشغال"""
        percentage = obj.get_occupancy_percentage()
        if percentage >= 95:
            color = 'red'
        elif percentage >= 80:
            color = 'orange'
        else:
            color = 'green'

        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color, percentage
        )
    get_occupancy_display.short_description = 'نسبة الإشغال'

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(BinLocation)
class BinLocationAdmin(admin.ModelAdmin):
    """
    إدارة مواقع التخزين في Django Admin
    """
    list_display = [
        'warehouse', 'bin_code', 'aisle', 'rack', 'level',
        'get_occupancy_display', 'status', 'is_active'
    ]

    list_filter = [
        'warehouse', 'status', 'aisle', 'temperature_zone',
        'hazard_zone', 'is_active'
    ]

    search_fields = [
        'bin_code', 'bin_name', 'aisle', 'rack', 'level'
    ]

    readonly_fields = ['created_at', 'updated_at', 'get_occupancy_display']

    fieldsets = (
        ('معلومات أساسية', {
            'fields': (
                'warehouse', 'bin_code', 'bin_name'
            )
        }),
        ('تفاصيل الموقع', {
            'fields': (
                'aisle', 'rack', 'level'
            )
        }),
        ('معلومات السعة', {
            'fields': (
                'capacity', 'current_quantity', 'get_occupancy_display'
            )
        }),
        ('الحالة والمواصفات', {
            'fields': (
                'status', 'temperature_zone', 'hazard_zone'
            )
        }),
        ('تتبع', {
            'fields': (
                'last_movement_date', 'notes', 'is_active',
                'created_at', 'updated_at'
            )
        }),
    )

    def get_occupancy_display(self, obj):
        """عرض نسبة الإشغال"""
        percentage = obj.get_occupancy_percentage()
        if percentage >= 100:
            color = 'red'
        elif percentage >= 80:
            color = 'orange'
        else:
            color = 'green'

        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color, percentage
        )
    get_occupancy_display.short_description = 'نسبة الإشغال'


@admin.register(StockMovement)
class StockMovementAdmin(admin.ModelAdmin):
    """
    إدارة حركات المخزون في Django Admin
    """
    list_display = [
        'movement_number', 'movement_type', 'movement_date',
        'item', 'warehouse', 'quantity', 'status', 'created_by'
    ]

    list_filter = [
        'movement_type', 'status', 'movement_date',
        'warehouse', 'created_at'
    ]

    search_fields = [
        'movement_number', 'item__item_name_ar', 'item__item_code',
        'batch_number', 'reference_document', 'supplier_invoice'
    ]

    readonly_fields = [
        'movement_number', 'total_cost', 'created_at',
        'updated_at', 'approved_at'
    ]

    fieldsets = (
        ('معلومات الحركة', {
            'fields': (
                'movement_number', 'movement_type', 'movement_date', 'status'
            )
        }),
        ('الصنف والموقع', {
            'fields': (
                'item', 'warehouse', 'bin_location'
            )
        }),
        ('معلومات الكمية', {
            'fields': (
                'quantity', 'unit_of_measure'
            )
        }),
        ('معلومات الدفعة', {
            'fields': (
                'batch_number', 'production_date', 'expiry_date'
            ),
            'classes': ('collapse',)
        }),
        ('المراجع', {
            'fields': (
                'reference_document', 'supplier_invoice', 'container_number'
            ),
            'classes': ('collapse',)
        }),
        ('معلومات التكلفة', {
            'fields': (
                'unit_cost', 'total_cost'
            ),
            'classes': ('collapse',)
        }),
        ('ملاحظات وتتبع', {
            'fields': (
                'notes', 'created_at', 'updated_at', 'created_by',
                'approved_by', 'approved_at'
            )
        }),
    )

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(StockBalance)
class StockBalanceAdmin(admin.ModelAdmin):
    """
    إدارة أرصدة المخزون في Django Admin
    """
    list_display = [
        'item', 'warehouse', 'bin_location', 'current_quantity',
        'available_quantity', 'batch_number', 'expiry_status', 'total_value'
    ]

    list_filter = [
        'warehouse', 'expiry_date', 'last_movement_date'
    ]

    search_fields = [
        'item__item_name_ar', 'item__item_code', 'batch_number'
    ]

    readonly_fields = [
        'available_quantity', 'total_value', 'last_updated'
    ]

    fieldsets = (
        ('الصنف والموقع', {
            'fields': (
                'item', 'warehouse', 'bin_location'
            )
        }),
        ('معلومات الدفعة', {
            'fields': (
                'batch_number', 'production_date', 'expiry_date'
            )
        }),
        ('معلومات الكمية', {
            'fields': (
                'current_quantity', 'reserved_quantity',
                'available_quantity', 'unit_of_measure'
            )
        }),
        ('معلومات التكلفة', {
            'fields': (
                'average_cost', 'total_value'
            )
        }),
        ('تتبع', {
            'fields': (
                'last_movement_date', 'last_updated'
            )
        }),
    )

    def expiry_status(self, obj):
        """عرض حالة انتهاء الصلاحية"""
        if obj.is_expired():
            return format_html('<span style="color: red;">منتهي الصلاحية</span>')
        elif obj.is_near_expiry():
            days = obj.days_to_expiry()
            return format_html(
                '<span style="color: orange;">ينتهي خلال {} يوم</span>',
                days
            )
        else:
            return format_html('<span style="color: green;">صالح</span>')
    expiry_status.short_description = 'حالة الصلاحية'


@admin.register(Alert)
class AlertAdmin(admin.ModelAdmin):
    """
    إدارة التنبيهات في Django Admin
    """
    list_display = [
        'title', 'alert_type', 'priority', 'status',
        'is_read', 'item', 'warehouse', 'created_at'
    ]

    list_filter = [
        'alert_type', 'priority', 'status', 'is_read', 'created_at'
    ]

    search_fields = [
        'title', 'message', 'item__item_name_ar', 'warehouse__warehouse_name'
    ]

    readonly_fields = [
        'created_at', 'acknowledged_at', 'resolved_at'
    ]

    fieldsets = (
        ('معلومات التنبيه', {
            'fields': (
                'alert_type', 'title', 'message', 'priority'
            )
        }),
        ('الكائنات المرتبطة', {
            'fields': (
                'item', 'warehouse', 'bin_location', 'movement'
            )
        }),
        ('الحالة والتتبع', {
            'fields': (
                'status', 'is_read', 'created_at',
                'acknowledged_at', 'acknowledged_by',
                'resolved_at', 'resolved_by'
            )
        }),
        ('ملاحظات', {
            'fields': ('notes',)
        }),
    )

    actions = ['mark_as_read', 'resolve_alerts']

    def mark_as_read(self, request, queryset):
        """تحديد التنبيهات كمقروءة"""
        for alert in queryset:
            alert.mark_as_read(request.user)
        self.message_user(request, f"تم تحديد {queryset.count()} تنبيه كمقروء")
    mark_as_read.short_description = "تحديد كمقروء"

    def resolve_alerts(self, request, queryset):
        """حل التنبيهات"""
        for alert in queryset:
            alert.resolve(request.user)
        self.message_user(request, f"تم حل {queryset.count()} تنبيه")
    resolve_alerts.short_description = "حل التنبيهات"


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """
    إدارة ملفات المستخدمين في Django Admin
    """
    list_display = [
        'user_full_name', 'role', 'assigned_warehouse',
        'phone_number', 'department', 'is_active', 'created_at'
    ]

    list_filter = [
        'role', 'assigned_warehouse', 'is_active', 'created_at'
    ]

    search_fields = [
        'user__first_name', 'user__last_name', 'user__username',
        'phone_number', 'department'
    ]

    fieldsets = (
        ('معلومات المستخدم', {
            'fields': ('user', 'role', 'assigned_warehouse')
        }),
        ('معلومات إضافية', {
            'fields': ('phone_number', 'department')
        }),
        ('الحالة', {
            'fields': ('is_active',)
        }),
    )

    def user_full_name(self, obj):
        return obj.user.get_full_name() or obj.user.username
    user_full_name.short_description = 'الاسم الكامل'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('user', 'assigned_warehouse')


class GoodsReceiptItemInline(admin.TabularInline):
    """
    إدارة أصناف إذن الاستلام كـ Inline
    """
    model = GoodsReceiptItem
    extra = 1
    fields = ['item', 'quantity', 'bin_location', 'batch_number', 'expiry_date', 'unit_cost', 'notes']
    autocomplete_fields = ['item', 'bin_location']


@admin.register(GoodsReceipt)
class GoodsReceiptAdmin(admin.ModelAdmin):
    """
    إدارة إيصالات الاستلام في Django Admin
    """
    list_display = [
        'receipt_number', 'warehouse', 'created_by', 'status',
        'total_items', 'total_quantity', 'receipt_date'
    ]

    list_filter = [
        'status', 'warehouse', 'receipt_date', 'created_by'
    ]

    search_fields = [
        'receipt_number', 'notes'
    ]

    readonly_fields = ['receipt_number', 'total_items', 'total_quantity', 'created_at', 'updated_at']

    fieldsets = (
        ('معلومات الإذن', {
            'fields': ('receipt_number', 'warehouse', 'status', 'notes')
        }),
        ('المجاميع', {
            'fields': ('total_items', 'total_quantity')
        }),
        ('معلومات التتبع', {
            'fields': ('created_by', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    inlines = [GoodsReceiptItemInline]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('warehouse', 'created_by')

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(GoodsReceiptItem)
class GoodsReceiptItemAdmin(admin.ModelAdmin):
    """
    إدارة أصناف إيصالات الاستلام في Django Admin
    """
    list_display = [
        'receipt', 'item', 'quantity', 'bin_location',
        'batch_number', 'expiry_date', 'unit_cost', 'total_cost'
    ]

    list_filter = [
        'receipt__warehouse', 'item__category', 'expiry_date'
    ]

    search_fields = [
        'receipt__receipt_number', 'item__item_name_ar', 'batch_number'
    ]

    autocomplete_fields = ['receipt', 'item', 'bin_location']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('receipt', 'item', 'bin_location')


class GoodsIssueItemInline(admin.TabularInline):
    """إدارة أصناف إذن الصرف كـ Inline"""
    model = GoodsIssueItem
    extra = 1
    fields = ['item', 'quantity', 'unit_of_measure', 'bin_location', 'unit_cost', 'total_cost']
    readonly_fields = ['total_cost']


@admin.register(GoodsIssue)
class GoodsIssueAdmin(admin.ModelAdmin):
    """
    إدارة أذون الصرف في Django Admin
    """
    list_display = [
        'issue_number', 'issue_type', 'issue_date', 'warehouse',
        'total_items', 'total_quantity', 'status', 'created_by'
    ]

    list_filter = [
        'issue_type', 'status', 'issue_date', 'warehouse', 'created_at'
    ]

    search_fields = [
        'issue_number', 'notes'
    ]

    readonly_fields = [
        'issue_number', 'total_items', 'total_quantity',
        'created_at', 'updated_at'
    ]

    fieldsets = (
        ('معلومات الإذن', {
            'fields': (
                'issue_number', 'issue_type', 'issue_date', 'status'
            )
        }),
        ('المخزن والمسؤول', {
            'fields': (
                'warehouse', 'created_by'
            )
        }),
        ('المجاميع', {
            'fields': (
                'total_items', 'total_quantity'
            )
        }),
        ('ملاحظات وتتبع', {
            'fields': (
                'notes', 'created_at', 'updated_at'
            )
        }),
    )

    inlines = [GoodsIssueItemInline]

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(GoodsIssueItem)
class GoodsIssueItemAdmin(admin.ModelAdmin):
    """
    إدارة أصناف أذون الصرف في Django Admin
    """
    list_display = [
        'issue', 'item', 'quantity', 'unit_of_measure',
        'bin_location', 'unit_cost', 'total_cost'
    ]

    list_filter = [
        'issue__issue_type', 'issue__warehouse', 'item__category'
    ]

    search_fields = [
        'issue__issue_number', 'item__item_name_ar', 'item__item_code'
    ]

    readonly_fields = ['total_cost']


class StockTransferItemInline(admin.TabularInline):
    """
    إدارة أصناف طلب النقل كـ Inline
    """
    model = StockTransferItem
    extra = 0
    fields = ['item', 'quantity', 'source_bin_location', 'destination_bin_location']
    readonly_fields = ['created_at']


@admin.register(StockTransfer)
class StockTransferAdmin(admin.ModelAdmin):
    """
    إدارة طلبات نقل المخزون في Django Admin
    """
    list_display = [
        'transfer_number', 'source_warehouse', 'destination_warehouse',
        'get_items_count', 'status', 'requested_by', 'transfer_date'
    ]

    list_filter = [
        'status', 'source_warehouse', 'destination_warehouse',
        'transfer_date', 'approved_at'
    ]

    search_fields = [
        'transfer_number', 'reason', 'notes'
    ]

    readonly_fields = [
        'transfer_number', 'transfer_date', 'created_at', 'updated_at',
        'approved_at', 'completed_at'
    ]

    inlines = [StockTransferItemInline]

    fieldsets = (
        ('معلومات الطلب', {
            'fields': (
                'transfer_number', 'transfer_date', 'status'
            )
        }),
        ('تفاصيل النقل', {
            'fields': (
                'source_warehouse', 'destination_warehouse'
            )
        }),
        ('معلومات الطلب', {
            'fields': (
                'reason', 'notes', 'requested_by'
            )
        }),
        ('معلومات الاعتماد', {
            'fields': (
                'approved_by', 'approved_at', 'approval_notes'
            ),
            'classes': ('collapse',)
        }),
        ('التتبع', {
            'fields': (
                'created_at', 'updated_at', 'completed_at'
            ),
            'classes': ('collapse',)
        }),
    )

    def get_items_count(self, obj):
        """عرض عدد الأصناف في الطلب"""
        return obj.get_total_items_count()
    get_items_count.short_description = 'عدد الأصناف'

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'source_warehouse', 'destination_warehouse',
            'requested_by', 'approved_by'
        ).prefetch_related('transfer_items')

    def save_model(self, request, obj, form, change):
        if not change:  # إذا كان إنشاء جديد
            obj.requested_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(StockTransferItem)
class StockTransferItemAdmin(admin.ModelAdmin):
    """
    إدارة أصناف طلبات نقل المخزون
    """
    list_display = [
        'transfer', 'item', 'quantity', 'source_bin_location',
        'destination_bin_location', 'created_at'
    ]

    list_filter = [
        'transfer__status', 'item', 'created_at'
    ]

    search_fields = [
        'transfer__transfer_number', 'item__item_name_ar', 'item__item_code'
    ]

    readonly_fields = ['created_at', 'updated_at']

    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'transfer', 'item', 'source_bin_location', 'destination_bin_location'
        )


# تخصيص عنوان Django Admin
admin.site.site_header = "KamaVerse - نظام إدارة المخزون"
admin.site.site_title = "KamaVerse Admin"
admin.site.index_title = "لوحة تحكم المخزون"
