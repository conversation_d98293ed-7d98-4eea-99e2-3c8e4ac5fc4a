{% extends 'base.html' %}

{% block title %}الصفحة الرئيسية - KamaVerse{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card gradient-soft border-0">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="card-title mb-3" style="color: var(--ink);">
                                <i class="fas fa-home text-primary-custom me-3"></i>
                                مرحباً {{ user.arabic_name|default:user.username }}
                            </h1>
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge bg-sand text-gold px-3 py-2 rounded-pill me-3">
                                    {{ user.get_user_level_display_ar }}
                                </span>
                                <span class="text-slate fw-semibold">
                                    {{ user.get_department_display_ar }}
                                </span>
                            </div>
                            <p class="card-text mb-0">
                                <small class="text-slate">
                                    <i class="fas fa-clock me-2 text-gold"></i>
                                    آخر دخول: {{ user.last_login|date:"Y/m/d H:i" }}
                                </small>
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="d-flex flex-column align-items-end">
                                <h4 class="text-primary-custom mb-2 fw-bold">{{ company_name }}</h4>
                                <p class="text-gold mb-2 fw-semibold fs-5">{{ system_name }}</p>
                                <small class="text-slate">
                                    <i class="fas fa-calendar-alt me-1 text-gold"></i>
                                    {{ current_time|date:"Y/m/d H:i" }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-5">
        <div class="col-md-3 mb-3">
            <div class="card text-center border-0 h-100" style="background: linear-gradient(135deg, var(--brand-red-light) 0%, var(--white) 100%);">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-center align-items-center mb-3" style="width: 60px; height: 60px; background: var(--brand-red); border-radius: 50%; margin: 0 auto;">
                        <i class="fas fa-users fa-lg text-white"></i>
                    </div>
                    <h5 class="card-title text-slate fw-semibold">المستخدمين النشطين</h5>
                    <h2 class="text-primary-custom fw-bold mb-2">--</h2>
                    <small class="text-slate">متصل الآن</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center border-0 h-100" style="background: linear-gradient(135deg, var(--accent-sand) 0%, var(--white) 100%);">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-center align-items-center mb-3" style="width: 60px; height: 60px; background: var(--success); border-radius: 50%; margin: 0 auto;">
                        <i class="fas fa-boxes fa-lg text-white"></i>
                    </div>
                    <h5 class="card-title text-slate fw-semibold">المنتجات</h5>
                    <h2 class="fw-bold mb-2" style="color: var(--success);">--</h2>
                    <small class="text-slate">إجمالي المنتجات</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center border-0 h-100" style="background: linear-gradient(135deg, var(--brand-gold-light) 0%, var(--white) 100%);">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-center align-items-center mb-3" style="width: 60px; height: 60px; background: var(--brand-gold); border-radius: 50%; margin: 0 auto;">
                        <i class="fas fa-handshake fa-lg text-white"></i>
                    </div>
                    <h5 class="card-title text-slate fw-semibold">الشركات</h5>
                    <h2 class="text-gold fw-bold mb-2">--</h2>
                    <small class="text-slate">موردين وعملاء</small>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card text-center border-0 h-100" style="background: linear-gradient(135deg, #FFF8E1 0%, var(--white) 100%);">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-center align-items-center mb-3" style="width: 60px; height: 60px; background: var(--warning); border-radius: 50%; margin: 0 auto;">
                        <i class="fas fa-chart-line fa-lg text-white"></i>
                    </div>
                    <h5 class="card-title text-slate fw-semibold">المعاملات</h5>
                    <h2 class="fw-bold mb-2" style="color: var(--warning);">--</h2>
                    <small class="text-slate">هذا الشهر</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Modules Section -->
    <div class="row">
        <div class="col-12">
            <div class="d-flex align-items-center mb-5">
                <div class="d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px; background: var(--brand-gold); border-radius: 50%;">
                    <i class="fas fa-th-large text-white"></i>
                </div>
                <h2 class="mb-0 text-ink fw-bold">الموديولات المتاحة</h2>
                <div class="flex-grow-1 mx-3" style="height: 2px; background: linear-gradient(90deg, var(--brand-gold) 0%, var(--line) 100%);"></div>
            </div>
        </div>
    </div>

    <div class="row">
        {% for module in available_modules %}
        <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
            <div class="card module-card h-100 border-0" onclick="navigateToModule('{{ module.url }}')" style="background: var(--white); transition: all 0.4s ease;">
                <div class="card-body text-center p-4 d-flex flex-column">
                    <div class="module-icon mb-3">
                        <div class="d-flex align-items-center justify-content-center mx-auto" style="width: 80px; height: 80px; background: linear-gradient(135deg, var(--brand-gold-light) 0%, var(--brand-gold) 100%); border-radius: 20px; box-shadow: 0 8px 20px rgba(200, 154, 60, 0.3);">
                            <i class="{{ module.icon }} fa-2x text-white"></i>
                        </div>
                    </div>
                    <h5 class="card-title text-ink fw-bold mb-3">{{ module.title }}</h5>
                    <p class="card-text text-slate flex-grow-1 mb-4">{{ module.description }}</p>
                    <div class="mt-auto">
                        <a href="{{ module.url }}" class="btn btn-primary w-100 py-2 fw-semibold" style="border-radius: 12px;">
                            <i class="fas fa-arrow-left me-2"></i>
                            دخول
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="alert border-0 text-center p-5" style="background: linear-gradient(135deg, var(--accent-sand) 0%, var(--white) 100%); border-radius: 16px;">
                <div class="d-flex justify-content-center align-items-center mb-4" style="width: 80px; height: 80px; background: var(--warning); border-radius: 50%; margin: 0 auto;">
                    <i class="fas fa-exclamation-triangle fa-2x text-white"></i>
                </div>
                <h4 class="text-ink fw-bold mb-3">لا توجد موديولات متاحة</h4>
                <p class="text-slate mb-0">يرجى الاتصال بالإدارة لتفعيل الصلاحيات المطلوبة.</p>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Recent Activity -->
    <div class="row mt-5">
        <div class="col-md-6 mb-4">
            <div class="card border-0 h-100">
                <div class="card-header border-0 p-4" style="background: linear-gradient(135deg, var(--brand-red-light) 0%, var(--white) 100%); border-radius: 16px 16px 0 0;">
                    <h5 class="card-title mb-0 text-ink fw-bold">
                        <i class="fas fa-clock me-3 text-primary-custom"></i>
                        النشاط الأخير
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item border-0 px-0 py-3 d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; background: var(--success); border-radius: 50%;">
                                    <i class="fas fa-sign-in-alt text-white"></i>
                                </div>
                                <div>
                                    <div class="fw-semibold text-ink">تسجيل دخول ناجح</div>
                                    <small class="text-slate">تم بنجاح</small>
                                </div>
                            </div>
                            <small class="text-slate">منذ دقائق</small>
                        </div>
                        <div class="list-group-item border-0 px-0 py-3 d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px; background: var(--brand-gold); border-radius: 50%;">
                                    <i class="fas fa-eye text-white"></i>
                                </div>
                                <div>
                                    <div class="fw-semibold text-ink">عرض الصفحة الرئيسية</div>
                                    <small class="text-slate">تصفح النظام</small>
                                </div>
                            </div>
                            <small class="text-slate">الآن</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card border-0 h-100">
                <div class="card-header border-0 p-4" style="background: linear-gradient(135deg, var(--brand-gold-light) 0%, var(--white) 100%); border-radius: 16px 16px 0 0;">
                    <h5 class="card-title mb-0 text-ink fw-bold">
                        <i class="fas fa-bell me-3 text-gold"></i>
                        الإشعارات
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="alert border-0 mb-4" style="background: var(--accent-sand); border-radius: 12px;">
                        <i class="fas fa-info-circle me-2 text-gold"></i>
                        <span class="text-ink fw-semibold">مرحباً بك في نظام KamaVerse الجديد!</span>
                    </div>
                    <div class="text-center">
                        <div class="d-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px; background: var(--line); border-radius: 50%; margin: 0 auto;">
                            <i class="fas fa-bell-slash fa-lg text-slate"></i>
                        </div>
                        <p class="text-slate mb-0">لا توجد إشعارات جديدة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0">
                <div class="card-header border-0 p-4" style="background: linear-gradient(135deg, var(--accent-sand) 0%, var(--white) 100%); border-radius: 16px 16px 0 0;">
                    <div class="d-flex align-items-center">
                        <div class="d-flex align-items-center justify-content-center me-3" style="width: 45px; height: 45px; background: var(--brand-red); border-radius: 50%;">
                            <i class="fas fa-bolt text-white"></i>
                        </div>
                        <h5 class="card-title mb-0 text-ink fw-bold">إجراءات سريعة</h5>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <button class="btn btn-secondary w-100 py-3" onclick="showComingSoon()" style="border-radius: 12px; border: 2px solid var(--brand-red); color: var(--brand-red); background: var(--white);">
                                <i class="fas fa-plus me-2"></i>
                                إضافة منتج جديد
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button class="btn w-100 py-3" onclick="showComingSoon()" style="border-radius: 12px; border: 2px solid var(--success); color: var(--success); background: var(--white);">
                                <i class="fas fa-building me-2"></i>
                                إضافة شركة جديدة
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button class="btn w-100 py-3" onclick="showComingSoon()" style="border-radius: 12px; border: 2px solid var(--brand-gold); color: var(--brand-gold-dark); background: var(--white);">
                                <i class="fas fa-file-invoice me-2"></i>
                                إنشاء فاتورة
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button class="btn w-100 py-3" onclick="showComingSoon()" style="border-radius: 12px; border: 2px solid var(--warning); color: var(--warning); background: var(--white);">
                                <i class="fas fa-chart-bar me-2"></i>
                                عرض التقارير
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function navigateToModule(url) {
        if (url === '#' || url.includes('coming-soon')) {
            showComingSoon();
        } else {
            window.location.href = url;
        }
    }
    
    function showComingSoon() {
        alert('هذه الميزة قيد التطوير وستكون متاحة قريباً!');
    }
    
    // Update current time every minute
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('ar-EG', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
        
        const timeElements = document.querySelectorAll('.current-time');
        timeElements.forEach(element => {
            element.textContent = timeString;
        });
    }
    
    // Update time every minute
    setInterval(updateTime, 60000);
    
    // Add enhanced hover effects to module cards
    document.querySelectorAll('.module-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
            this.style.boxShadow = '0 15px 40px rgba(214, 40, 40, 0.2)';
            this.style.borderColor = 'var(--brand-gold)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 4px 12px rgba(26, 26, 26, 0.08)';
            this.style.borderColor = 'var(--line)';
        });
    });

    // Add hover effects to quick action buttons
    document.querySelectorAll('.btn').forEach(btn => {
        if (!btn.classList.contains('btn-primary')) {
            btn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.15)';
            });

            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        }
    });
</script>
{% endblock %}
