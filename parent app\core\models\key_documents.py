"""
KeyDocuments - إدارة المستندات
إدارة جميع المستندات والملفات في النظام
"""

from django.db import models
import uuid
import os


class KeyDocument(models.Model):
    """
    نموذج المستندات الأساسي
    """
    
    DOCUMENT_TYPES = [
        ('CONTRACT', 'عقد'),
        ('INVOICE', 'فاتورة'),
        ('RECEIPT', 'إيصال'),
        ('CERTIFICATE', 'شهادة'),
        ('REPORT', 'تقرير'),
        ('IMAGE', 'صورة'),
        ('SPREADSHEET', 'جدول بيانات'),
        ('PDF', 'ملف PDF'),
        ('OTHER', 'أخرى'),
    ]
    
    CONFIDENTIALITY_LEVELS = [
        ('PUBLIC', 'عام'),
        ('INTERNAL', 'داخلي'),
        ('CONFIDENTIAL', 'سري'),
        ('RESTRICTED', 'محدود'),
    ]
    
    # Primary key
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Document Information
    document_name = models.CharField(
        max_length=200,
        verbose_name='اسم المستند'
    )
    
    document_type = models.CharField(
        max_length=20,
        choices=DOCUMENT_TYPES,
        verbose_name='نوع المستند'
    )
    
    file = models.FileField(
        upload_to='documents/%Y/%m/',
        verbose_name='الملف'
    )
    
    file_size = models.PositiveIntegerField(
        verbose_name='حجم الملف (بايت)',
        blank=True,
        null=True
    )
    
    file_extension = models.CharField(
        max_length=10,
        verbose_name='امتداد الملف',
        blank=True,
        null=True
    )
    
    # Security
    confidentiality_level = models.CharField(
        max_length=20,
        choices=CONFIDENTIALITY_LEVELS,
        default='INTERNAL',
        verbose_name='مستوى السرية'
    )
    
    # Related Entities
    related_company = models.ForeignKey(
        'core.KeyCompany',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='documents',
        verbose_name='الشركة المرتبطة'
    )

    related_transaction = models.ForeignKey(
        'core.KeyTransaction',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='documents',
        verbose_name='المعاملة المرتبطة'
    )
    
    # Additional Information
    description = models.TextField(
        verbose_name='الوصف',
        blank=True,
        null=True
    )
    
    tags = models.CharField(
        max_length=200,
        verbose_name='العلامات',
        blank=True,
        null=True,
        help_text='علامات مفصولة بفواصل'
    )
    
    # Timestamps
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ آخر تحديث'
    )
    
    created_by = models.ForeignKey(
        'core.KeyUser',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='created_documents',
        verbose_name='أنشئ بواسطة'
    )
    
    class Meta:
        verbose_name = 'مستند'
        verbose_name_plural = 'المستندات'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['document_type']),
            models.Index(fields=['confidentiality_level']),
            models.Index(fields=['created_at']),
            models.Index(fields=['related_company']),
        ]
    
    def __str__(self):
        return self.document_name
    
    def save(self, *args, **kwargs):
        if self.file:
            self.file_size = self.file.size
            self.file_extension = os.path.splitext(self.file.name)[1].lower()
        super().save(*args, **kwargs)
