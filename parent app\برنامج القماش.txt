سنبدأ فى الشركة مشروع جديد لشركة تسمى شركة القماش يمكنك البحث عنها من خلال هذا الرابط 
https://www.facebook.com/kamashgroup
او على الانترنت بشكل عام فهي شركة كبيرة تعتمد اعتماد كلي على استيراد وبيع المواد الاولية للبلاستيك 
خامات البلاستيك والمطاط التي توفرها "القماش جروب"

معلومات عامة ونشأة الشركة

تأسست مجموعة القماش للاستيراد والتصدير عام 1998، ويُعدّ ياسر القماش هو المؤسس ورئيس مجلس الإدارة 

تُعرف المجموعة بكونها رائدة في سوق المواد الخام الصناعية بمصر، وبعملها ضمن معايير عالية من الخبرة، الالتزام، والثقة .

التخصصات والمجالات التي تعمل فيها

مختصة في استيراد وتصدير المواد الخام الصناعية المستخدمة في صناعات متنوعة مثل البلاستيك، الدهانات، المطاط، وغيرها 

بحسب صفحة متخصصة، تشمل المنتجات ما يلي:

مواد خام بلاستيك وكاوتشوك (مطاط)

أرضيات مطاطية

خامات بولي إيثيلين

مثبتات راتنجات PVC وراتنجات PVC

أكسيد الزنك

الكربون الأسود (Carbon Black)

كيماويات صناعية ودهانات

كلوريد وتيلانتيوم سالفيت (Titanium Chloride & Sulfate)

راتنجات (Resin) مثل Resin 67، Resin 70، P100

سيليكلور (Silychlor)

زيت الصويا، حمض إستياريك (Stearic Acid)

مظهر ضوئي (Optical Brightener – OP)

مواسير ومواد خام لمستحضرات التجميل 

مميزات تنافسية

منذ تأسيسها، نجحت الشركة في ترسيخ مكانتها كشركة موثوقة توفر حلولًا بموثوقية وجودة عالية ضمن السوق المصري للمواد الخام 

وفقًا لأحدث منشوراتها، فهي تقدم محلولاً عالي الجودة لـ PVC Compound مثل أنواع الاستابليزر السائلة الإماراتية والتايوانية، والتي تستخدم في صناعات مثل الكابلات 

ملخص سريع
البند	التفاصيل
تاريخ التأسيس	1998
الجهة المؤسسة	ياسر القماش (رئيس مجلس الإدارة)
مجال التخصص	استيراد وتصدير المواد الخام الصناعية (بلاستيك، مطاط، دهانات)
أبرز المنتجات	بولي إيثيلين، راتنجات PVC، مثبتات PVC، أكسيد الزنك، الكربون الأسود، وغيرها
ميزة تنافسية	جودة عالمية، حلول متطورة مثل stabilizer لسوائل PVC صنعت خصيصًا لتطبيقات صناعية

أهم خامات البلاستيك المتوفّرة

المطاط الطبيعي والصناعي (Natural & Synthetic Rubber)
تستورد وتوفّر الشركة أنواعًا مختلفة من المطاط، سواء الطبيعي أو الصناعي، وهو أساسي لصناعات البلاستيك المطاطية 

الإيفا (EVA – Ethylene‑Vinyl Acetate)
تُعد الإيفا من الخامات الأساسية التي توفرها الشركة، وتستخدم — على نطاق واسع — في صناعات مثل الأحذية والتغليف والمواد المرنة 

بولي فينيل كلورايد (PVC)
توفر "القماش جروب" خامات PVC، وهي مادة بلاستيكية متعددة الاستخدام في الأنابيب والعوازل وغيرها 

رذاذات (Blowing Agents) ومواد داعمة أخرى
حسب الإعلانات الوظيفية لديكور مخالص الصناعات البلاستيكية، توفر الشركة مواد مثل "Blowing Agent"، والتي تُستخدم لإنشاء مسام أو تكثيفهيكل الرغوة في بعض منتجات البلاستيك 

كربون أسود (Carbon Black – Grades N220, N330, N550, N660)
توفر الشركة كربون أسود بجودات تتضمن N220 وN330 وN550 وN660، يُستخدم للتلوين ودعم خواص البلاستيك مثل مقاومة الأشعة فوق البنفسجية والمتانة 

أكسيد الزنك (Zinc Oxide)
جزء من نطاق المواد الأساسية التي تستوردها الشركة، ويُستخدم في تعزيز مقاومة الحرارة أو كعامل مساعد في تركيبات البلاستيك 

مواد كيميائية أخرى
تشمل: DCP (ربما Dibasic Calcium Phosphate أو مكوّن مساعد)، DOP (غالبًا Dioctyl Phthalate، وهو ملدن شائع للبلاستيك)، بالإضافة إلى مواد داعمة وتحسينات إضافية 


،،

والان المشروع المطلوب لهم يتكون من 10 موديولات مختلفة منهم 8 خاصين بالنظام الذي سيعمل داخل الشركة على اجهزة الكمبيوتر واللاب توب و2 منهم خاصين بالموبايل ، البرنامج بالكامل يجب ان يكون مجهز ليعمل باللغة العربية فقط ولا يتخلله اللغة الانجليزية فى اى جانب من جوانبه ، ففكرة البرنامج هي ERP متكامل ومترابط مع بعضه البعض باسم KamaVerse سأشرحهم لك بشكل مفصل :

1. وحدة الاستيراد (Import Module)
هذه الوحدة مسؤولة عن كل عملية استيراد تقوم بها الشركة من الموردين الخارجيين. وتشمل:
•	إدارة بيانات الموردين: العناوين، طرق الاتصال، أنواع المواد الموردة، عقود التوريد.
•	جلب عروض الأسعار وعمل طلبات الشراء الدولية: إنشاء وتتبع أوامر الشراء ومطابقتها مع الفواتير الأصلية.
•	متابعة الشحنات: معرفة مكان الشحنة، خطوط النقل، مواعيد الوصول، والموانئ المستخدمة.
•	التكامل الجمركي: تخزين بيانات التخليص الجمركي، الرسوم، الضرائب.
•	التكامل مع المخزون: عند استلام الشحنة يتم تحديث الكميات في مستودعات الشركة تلقائياً.

2. وحدة المخزون (Stock Module)
تركز على إدارة المواد الكيماوية في المستودعات مع مراعاة معايير السلامة.
•	بيانات المنتجات: أسماء المواد، الأكواد، وحدات القياس، شروط التخزين، درجة الخطورة.
•	تتبع الكميات: تسجيل الوارد والصادر وحساب الكميات المتبقية.
•	تنبيه المخزون: إرسال إشعارات عند وصول المخزون إلى الحد الأدنى.
•	تتبع تاريخ الإنتاج والانتهاء لتجنب البيع أو الاستخدام بعد انتهاء الصلاحية.
•	إدارة المواقع داخل المستودع: أماكن التخزين حسب تصنيف المواد.
________________________________________
3. وحدة المالية (Finance Module)
تتعامل مع جميع المعاملات المالية للشركة.
•	الحسابات الدائنة والمدينة: تسجيل المدفوعات للموردين، والمبالغ المستحقة للشركة.
•	الفوترة: إصدار فواتير البيع، وإدخال فواتير الشراء.
•	متابعة النقدية والبنوك: قيود يومية، أرصدة الحسابات البنكية، والتحويلات.
•	إعداد الميزانيات: وضع ميزانيات سنوية أو ربع سنوية لكل قسم.
•	التقارير المالية: أرباح وخسائر، التدفقات النقدية، وتحليل المصاريف.
________________________________________
4. وحدة المبيعات (Sales Module)
تدير عمليات البيع بشكل كامل.
•	أوامر البيع: تسجيل الطلبات من العملاء ومتابعتها حتى التسليم.
•	تسعير العروض: إعداد عروض أسعار بناءً على الكميات والمواصفات المطلوبة.
•	إدارة العقود: متابعة تنفيذ العقود طويلة الأمد.
•	التوافق مع المخزون: منع بيع منتجات غير متوفرة.
•	الخصومات والعروض: إدارة الحملات والعروض الخاصة للعملاء.
________________________________________
5. وحدة إدارة علاقات العملاء (CRM Module)
تركز على العلاقة مع العملاء لجذبهم والحفاظ عليهم.
•	قاعدة بيانات العملاء: تسجيل بيانات الاتصال، النشاطات، وسجل الشراء.
•	التواصل الآلي: إرسال رسائل متابعة أو عروض تسويقية عبر البريد أو الرسائل.
•	سجل التفاعل: تسجيل المكالمات، الاجتماعات، الشكاوى، وردود الدعم الفني.
•	تحليل العملاء: معرفة العملاء الأكثر ربحية وتحديد فرص البيع الجديدة.
________________________________________
6. وحدة الموارد البشرية (HR Module)
تدير الموظفين وشؤونهم الإدارية.
•	ملفات الموظفين: بيانات الهوية، المؤهلات، العقود.
•	إدارة الحضور والانصراف: عن طريق جهاز البصمة أو تسجيل يدوي.
•	الرواتب: حساب الرواتب الشهرية، الضرائب، والحوافز.
•	الإجازات: طلبات وموافقات الإجازة تتبعياً.
•	تقييم الأداء: أنظمة تقييم سنوية أو دورية.
________________________________________
7. وحدة اللوجستيات (Logistics Module)
خاصة بـ إدارة الحركة والنقل داخلياً وخارجياً.
•	تنظيم الشحنات: اختيار وسيلة النقل (بحري، بري، جوي).
•	تتبع الشحنات: معرفة حالة الشحنة في أي وقت.
•	إدارة موردين اللوجستيات: شركات النقل، الأسعار، الاتفاقيات.
•	دمج مع المبيعات والمخزون لضمان تسليم الطلبات في الوقت المحدد.
________________________________________
8. وحدة التقارير (Reporting Module)
تعتبر واجهة الشركة لاتخاذ القرارات.
•	لوحات تحكم (Dashboards) تفاعلية تعرض أداء كل قسم.
•	التقارير المالية: الأرباح، المصروفات، الأرباح الإجمالية.
•	تقارير المخزون: الكميات، المنتجات القريبة من الانتهاء، المنتجات الراكدة.
•	تصدير التقارير: إمكانية حفظ التقرير بصيغة PDF أو Excel.
•	تحليلات تنبؤية: تتوقع المبيعات أو استهلاك المخزون بناءً على البيانات التاريخية.
________________________________________
9. برنامج المحادثة الداخلية (Kamachat)
خاص بالتواصل بين الموظفين والمديرين ورؤساء الاقسام وبعضهم البعض من داخل وخارج الشركة
•	برنامج يشبه واجهة الواتس اب ولكن بديزاين ولوجو مجموعة القماش
•	يسهل التواصل بين الموظفين وبعضهم وارسال الرسائل والملفات الخاصة بالعمل بينهم 
•	من خلال البرنامج يستطيع رؤساء المجموعات والأقسام عمل قائمة مهام وتحديد المهام المطلوبة من الموظفين تحديداً
•	لكل مجموعة فى الشركة جروب خاصة بهم على هذا البرنامج يستطيعون التعامل فيما بينهم مثل برنامج Slack 
•	رؤساء مجلس الادارة يستطيعون الاطلاع على جميع الجروبات والمحادثات والملفات السارية والمهام المعلقة والمنفذة
________________________________________
10. برنامج الصقر (Hawk)
خاص برؤساء مجلس الإدارة فقط ولا يستطيع غيرهم الاطلاع عليه او فتحه الا من خلال اجهزتهم وهواتفهم فقط

•	المهمة الرئيسية لبرنامج الصقر هي قدرة رؤية أي نشاط او أي حركة داخل الشركة من خلال التقارير الفورية
•	تمكن لرؤساء المجموعة ان يعرفو كل تفصيلة كبيرة أو صغيرة داخل ال 8 الموجودة ببرنامج ال ERP العام
•	يمكن للمستخدم ان يصدر تقارير ويقوم بتصديرها على هيئة شيت اكسيل او ملف PDF ليتمكن من مشاركتها ونشرها
•	هناك صفحة وحيدة لتحديد القيم التى يتحكم بها رئيس مجلس الادارة مثل تحديد سعر البيع والموافقات على الشراء التى 		تعرض عليه من قبل الموظفين لآخذ موافقته ويتم تسجيل موافقته فى KamaVerse ليتم اتخاذ الاجراء بناء على ذلك

،

**** هام جداً جداً: من الموديولات المهمة جداً فى هذا البرنامج هو موديول إدارة المستخدمين لانه سيتم من خلاله تحديد صلاحيات لكل موظف على حدة بحيث ان يكون لكل موظف الصلاحيات الخاصة به فقط ولا يستطيع رؤية صفحات الاقسام الاخرى ولا يستطيع ان يقوم بالتعديل او الاضافة الا اذا تم السماح له بذلك من خلال هذه الصفحة 
يكون من حق ال Administrators ومن حق رؤساء مجلس الادارة فقط الحق فى تسجيل واضافة وتعديل اى مستخدم 

،

- يجب ان تراعي كل نقطة من النقاط التى ذكرتها وان لا تقوم باغفال اى نقطة حتى لو وصلت قائمة المهام أكثر من 100 خطوة للتنفيذ واريد منك ان تقوم بعمل ملف باسم Flow.md يتم وضع فيه جميع الخطوات التى سنقوم بتنفيذها معاً على ان يتم التعديل فى هذا الملف وفق التعديل فى قائمة المهام بشكل متزامن ومتناسق وبدون أخطاء

- لا أريدك ان تقوم بانهاء مهمة من المهام الا قبل عمل اختبار كامل لها ولجميع جوانبها للتأكد تمام التأكد بأن المهمة اكتملت بنجاح وبدون خطأ واحد فلا أقبل منك إلا ب 100% نجاح و 0% خطأ 


، والآن لنبدأ بعمل الخطة البرمجية للمشروع :

1- قمت بإرفاق الصورة الأولى تظهر أيقونات وشكل جمالي ملون ومتناسق ومنظم إريد وضع ال 8 موديولات به بالاضافة الى موديول إدارة المستخدمين قبل ظهور هذه الصفحة تظهر شاشة تسجيل الدخول ومن خلالها يظهر الموديول الخاص بالمستخدم الذي قام بعمل تسجيل الدخول 

2- قمت بارفاق الصورة الثانية وهي صورة محتوى الموديول نفسه عند الضغط عليه كمثل ان المستخدم فتح موديول المالية وأيضاً المستخدمين لهم صلاحيات محددة داخل كل موديول لان بعض الموظفين لهم صلاحيات التسجيل وبعضهم صلاحيات الاطلاع وبعضهم صلاحيات تسجيل فى مكان مخصص لهم كموظف مبتدئ لا يجب ان يطلع او يتحكم فى اى جانب من جوانب الموديول لذا هذا سيرجع لصلاحيات المستخدمين الدقيقة والمفصلة 

3- يتم عمل هذا المشروع بلغة Django على ان تكون قاعدة البيانات Postgresql وان يكون البرنامج مقسم الى العديد من الملفات لا تزيد الملفات عن 1000 سطر بأي شكل من الأشكال حتى لو تم تقسيم الصفحة الواحدة الى عدة ملفات لتسهيل عملية التنقل وتسريع الحركة داخل البرنامج وتقليل ال tokens المستهلكة من الذكاء الاصطناعي اثناء التنفيذ

4- قاعدة البيانات تكون واحدة غير منفصلة وغير مفرقة على ان تكون الجداول منسقة ومنظمة والجداول الرئيسية التى تربط بين الموديولات وبعضها تكون بداية اسمها "Key" واريد منك عمل قائمة كاملة بهذه الجداول المرتبطة بين الموديولات وبعضها فى ملف key.md ويتم الاحتفاظ به فى الروت الخاصة بالبرنامج مع شرح بسيط عن وظيفة هذا ال key والموديولات المرتبطة به 

5- يتم عمل الموديولين الأخيرين بتقنية PWA ليتم فتحهم عن طريق الموبايل مؤقتاً لحين الإنتهاء من تجهيز نسخة طبق الأصل منهم ولكن ببرنامج Flutter لوضعها على المتاجر الخاصة بال Android وال IPhone بعد تخطي المراحل الاولى للتشغيل

6- هناك أمر هام جداً سيقوم فريق العمل بالشركة بالعمل على هذا البرنامج كل مبرمج سيقوم بالعمل على موديول منفصل ولكن بالاحتفاظ بالKey الذي سيربط الموديول بباقي الموديولات حين التجميع بينهم فى نهاية منتصف ونهاية وتسليم المشروع

7- اعلم انه فى كل موديول من الموديولات بعض التعديلات الذي سيطلبها العميل عند التسليم الأولي قبل التسليم النهائي لذا لا تجعل البرنامج Fixed او غير قابل للاضافة والتمديد بل يجب ان يكون قابل لإدخال اى تعديلات حتى لو غير منطقية ولكن تتكيف على حسب خط سير البرنامج والترابط القوى مع قاعدة البيانات

ـــــــــــــــ
لا اريد منك تنفيذ المشروع الا بعد عمل خطة محكمة ، ابذل قصارى جهدك لعمل خريطة طريق كاملة ك Project Pipeline منذ البدء الى الإنتهاء

ابدأ واسألني فى أي شئ تشعر بحيرة فى اختياره

