# خطة تصميم الواجهات - مشروع KamaVerse

## نظرة عامة على التصميم

التصميم مستوحى من الصور المرفقة ويركز على:
- **البساطة والوضوح**
- **الألوان المتناسقة والجذابة**
- **التنظيم المنطقي للعناصر**
- **دعم كامل للغة العربية**
- **تجربة مستخدم سلسة**

---

## نظام الألوان الأساسي

### الألوان الرئيسية
```css
:root {
    /* ألوان شركة القماش */
    --primary-color: #2E86AB;      /* أزرق أساسي */
    --secondary-color: #A23B72;    /* بنفسجي */
    --accent-color: #F18F01;       /* برتقالي */
    --success-color: #C73E1D;      /* أحمر للنجاح */
    
    /* ألوان الخلفية */
    --bg-primary: #F8F9FA;         /* خلفية رئيسية */
    --bg-secondary: #FFFFFF;       /* خلفية ثانوية */
    --bg-card: #FFFFFF;            /* خلفية البطاقات */
    
    /* ألوان النص */
    --text-primary: #2C3E50;       /* نص أساسي */
    --text-secondary: #7F8C8D;     /* نص ثانوي */
    --text-light: #BDC3C7;         /* نص فاتح */
    
    /* ألوان الحدود */
    --border-light: #E9ECEF;       /* حدود فاتحة */
    --border-medium: #DEE2E6;      /* حدود متوسطة */
}
```

### ألوان الموديولات
```css
/* ألوان مميزة لكل موديول */
.import-module { --module-color: #3498DB; }      /* أزرق */
.stock-module { --module-color: #2ECC71; }       /* أخضر */
.finance-module { --module-color: #E74C3C; }     /* أحمر */
.sales-module { --module-color: #F39C12; }       /* برتقالي */
.crm-module { --module-color: #9B59B6; }         /* بنفسجي */
.hr-module { --module-color: #1ABC9C; }          /* تركوازي */
.logistics-module { --module-color: #34495E; }   /* رمادي داكن */
.reporting-module { --module-color: #E67E22; }   /* برتقالي داكن */
.users-module { --module-color: #8E44AD; }       /* بنفسجي داكن */
```

---

## تخطيط الصفحة الرئيسية (Dashboard)

### هيكل الصفحة
```html
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KamaVerse - نظام إدارة شركة القماش</title>
    <link rel="stylesheet" href="css/dashboard.css">
</head>
<body>
    <!-- Header -->
    <header class="main-header">
        <div class="logo-section">
            <img src="images/kamash-logo.png" alt="شركة القماش">
            <h1>KamaVerse</h1>
        </div>
        <div class="user-section">
            <div class="notifications">
                <i class="icon-bell"></i>
                <span class="notification-count">5</span>
            </div>
            <div class="user-profile">
                <img src="images/user-avatar.png" alt="المستخدم">
                <span class="user-name">أحمد محمد</span>
                <i class="icon-chevron-down"></i>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="dashboard-main">
        <!-- Stats Cards -->
        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon import-module">
                        <i class="icon-import"></i>
                    </div>
                    <div class="stat-content">
                        <h3>الشحنات الواردة</h3>
                        <p class="stat-number">27.6م</p>
                        <span class="stat-change positive">+2%</span>
                    </div>
                </div>
                <!-- المزيد من البطاقات... -->
            </div>
        </section>

        <!-- Modules Grid -->
        <section class="modules-section">
            <h2>الموديولات</h2>
            <div class="modules-grid">
                <!-- بطاقات الموديولات -->
            </div>
        </section>
    </main>
</body>
</html>
```

### شبكة الموديولات (Modules Grid)
```html
<div class="modules-grid">
    <!-- موديول الاستيراد -->
    <div class="module-card import-module" data-module="import">
        <div class="module-icon">
            <i class="icon-ship"></i>
        </div>
        <h3>الاستيراد</h3>
        <p>إدارة عمليات الاستيراد والموردين</p>
        <div class="module-stats">
            <span>15 شحنة نشطة</span>
        </div>
    </div>

    <!-- موديول المخزون -->
    <div class="module-card stock-module" data-module="stock">
        <div class="module-icon">
            <i class="icon-warehouse"></i>
        </div>
        <h3>المخزون</h3>
        <p>إدارة المخازن والمواد الخام</p>
        <div class="module-stats">
            <span>1,250 صنف</span>
        </div>
    </div>

    <!-- موديول المالية -->
    <div class="module-card finance-module" data-module="finance">
        <div class="module-icon">
            <i class="icon-money"></i>
        </div>
        <h3>المالية</h3>
        <p>إدارة الحسابات والمعاملات المالية</p>
        <div class="module-stats">
            <span>2.5م جنيه</span>
        </div>
    </div>

    <!-- باقي الموديولات... -->
</div>
```

---

## تصميم صفحات الموديولات

### هيكل صفحة الموديول
```html
<div class="module-page">
    <!-- Header الموديول -->
    <header class="module-header">
        <div class="module-title">
            <i class="module-icon icon-import"></i>
            <h1>موديول الاستيراد</h1>
        </div>
        <div class="module-actions">
            <button class="btn btn-primary">إضافة جديد</button>
            <button class="btn btn-secondary">تصدير</button>
        </div>
    </header>

    <!-- Navigation الفرعي -->
    <nav class="module-nav">
        <ul class="nav-tabs">
            <li class="nav-item active">
                <a href="#suppliers">الموردين</a>
            </li>
            <li class="nav-item">
                <a href="#orders">طلبات الشراء</a>
            </li>
            <li class="nav-item">
                <a href="#shipments">الشحنات</a>
            </li>
            <li class="nav-item">
                <a href="#customs">الجمارك</a>
            </li>
        </ul>
    </nav>

    <!-- المحتوى الرئيسي -->
    <main class="module-content">
        <!-- شبكة الأيقونات الفرعية -->
        <div class="sub-modules-grid">
            <div class="sub-module-card">
                <div class="sub-module-icon">
                    <i class="icon-supplier"></i>
                </div>
                <h3>إدارة الموردين</h3>
                <p>إضافة وتعديل بيانات الموردين</p>
            </div>
            <!-- المزيد من البطاقات الفرعية... -->
        </div>
    </main>
</div>
```

---

## تصميم النماذج (Forms)

### نموذج إضافة مورد جديد
```html
<form class="kamash-form">
    <div class="form-header">
        <h2>إضافة مورد جديد</h2>
        <button type="button" class="btn-close">×</button>
    </div>
    
    <div class="form-body">
        <div class="form-row">
            <div class="form-group">
                <label for="company_name">اسم الشركة *</label>
                <input type="text" id="company_name" name="company_name" required>
            </div>
            <div class="form-group">
                <label for="tax_number">الرقم الضريبي</label>
                <input type="text" id="tax_number" name="tax_number">
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="contact_person">الشخص المسؤول</label>
                <input type="text" id="contact_person" name="contact_person">
            </div>
            <div class="form-group">
                <label for="phone">رقم الهاتف</label>
                <input type="tel" id="phone" name="phone">
            </div>
        </div>
        
        <!-- المزيد من الحقول... -->
    </div>
    
    <div class="form-footer">
        <button type="submit" class="btn btn-primary">حفظ</button>
        <button type="button" class="btn btn-secondary">إلغاء</button>
    </div>
</form>
```

---

## تصميم الجداول (Tables)

### جدول الموردين
```html
<div class="table-container">
    <div class="table-header">
        <h3>قائمة الموردين</h3>
        <div class="table-actions">
            <input type="search" placeholder="البحث في الموردين...">
            <button class="btn btn-filter">تصفية</button>
        </div>
    </div>
    
    <table class="kamash-table">
        <thead>
            <tr>
                <th>اسم الشركة</th>
                <th>الدولة</th>
                <th>نوع المواد</th>
                <th>التقييم</th>
                <th>آخر طلب</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    <div class="company-info">
                        <img src="images/company-logo.png" alt="الشركة">
                        <div>
                            <strong>شركة البتروكيماويات المتحدة</strong>
                            <small>الإمارات العربية المتحدة</small>
                        </div>
                    </div>
                </td>
                <td>الإمارات</td>
                <td>
                    <span class="badge badge-primary">PVC</span>
                    <span class="badge badge-secondary">PE</span>
                </td>
                <td>
                    <div class="rating">
                        <i class="icon-star filled"></i>
                        <i class="icon-star filled"></i>
                        <i class="icon-star filled"></i>
                        <i class="icon-star filled"></i>
                        <i class="icon-star"></i>
                    </div>
                </td>
                <td>2024-01-15</td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon" title="عرض">
                            <i class="icon-eye"></i>
                        </button>
                        <button class="btn-icon" title="تعديل">
                            <i class="icon-edit"></i>
                        </button>
                        <button class="btn-icon" title="حذف">
                            <i class="icon-delete"></i>
                        </button>
                    </div>
                </td>
            </tr>
            <!-- المزيد من الصفوف... -->
        </tbody>
    </table>
</div>
```

---

## تصميم التطبيقات المحمولة (PWA)

### Kamachat - واجهة المحادثة
```html
<div class="kamachat-app">
    <!-- Header -->
    <header class="chat-header">
        <div class="chat-title">
            <img src="images/kamash-logo-small.png" alt="KamaChat">
            <h1>KamaChat</h1>
        </div>
        <div class="chat-actions">
            <button class="btn-icon">
                <i class="icon-search"></i>
            </button>
            <button class="btn-icon">
                <i class="icon-menu"></i>
            </button>
        </div>
    </header>

    <!-- Chat List -->
    <div class="chat-list">
        <div class="chat-item active">
            <div class="chat-avatar">
                <img src="images/group-avatar.png" alt="مجموعة">
            </div>
            <div class="chat-info">
                <h3>فريق المبيعات</h3>
                <p>أحمد: تم إرسال العرض للعميل</p>
                <span class="chat-time">10:30 ص</span>
            </div>
            <div class="chat-badge">3</div>
        </div>
        <!-- المزيد من المحادثات... -->
    </div>
</div>
```

### Hawk - لوحة تحكم الإدارة العليا
```html
<div class="hawk-app">
    <!-- Dashboard Header -->
    <header class="hawk-header">
        <h1>🦅 Hawk - لوحة المراقبة</h1>
        <div class="real-time-indicator">
            <span class="status-dot active"></span>
            <span>مباشر</span>
        </div>
    </header>

    <!-- Key Metrics -->
    <section class="key-metrics">
        <div class="metric-card urgent">
            <h3>موافقات معلقة</h3>
            <span class="metric-value">7</span>
        </div>
        <div class="metric-card">
            <h3>المبيعات اليوم</h3>
            <span class="metric-value">2.3م</span>
        </div>
        <!-- المزيد من المقاييس... -->
    </section>
</div>
```

---

## الخطوط والأيقونات

### الخطوط العربية
```css
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.6;
}
```

### مكتبة الأيقونات
- **Font Awesome** للأيقونات العامة
- **أيقونات مخصصة** لشركة القماش
- **أيقونات المواد الكيماوية** المتخصصة

---

## الاستجابة (Responsive Design)

### نقاط التوقف (Breakpoints)
```css
/* Mobile First Approach */
@media (min-width: 576px) { /* Small devices */ }
@media (min-width: 768px) { /* Medium devices */ }
@media (min-width: 992px) { /* Large devices */ }
@media (min-width: 1200px) { /* Extra large devices */ }
```

---

## ملاحظات التطوير

1. **الأداء:** تحسين الصور والخطوط
2. **الوصولية:** دعم قارئات الشاشة
3. **التوافق:** دعم جميع المتصفحات الحديثة
4. **الأمان:** حماية من XSS و CSRF
5. **SEO:** تحسين محركات البحث

---

**آخر تحديث:** 2025-08-15  
**المصمم:** فريق UI/UX - KamaVerse
