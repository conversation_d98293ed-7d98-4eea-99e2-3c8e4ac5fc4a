import os
import sys
import django
import uuid
from decimal import Decimal

# Setup Django
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'kamaverse_inventory.settings')
django.setup()

from inventory.models import Warehouse

# Generate a unique code for our test warehouse
test_code = f"TEST-{uuid.uuid4().hex[:6]}"

# Create a new warehouse
test_warehouse = Warehouse(
    warehouse_code=test_code,
    warehouse_name="مخزن اختبار التعيين التلقائي",
    warehouse_type="BRANCH",
    total_capacity=Decimal("1000.00"),
    current_occupancy=Decimal("0.00"),  # Initial value is zero
    address="عنوان اختباري",
    city="القاهرة",
    governorate="القاهرة",
    warehouse_manager="مدير اختباري",
    manager_phone="01234567890",
    is_active=True
)

# Save the warehouse (this should trigger our custom save method)
test_warehouse.save()

# Retrieve the warehouse from the database to confirm changes were saved
refreshed_warehouse = Warehouse.objects.get(warehouse_code=test_code)

print(f"\n📊 Test Results for Warehouse: {refreshed_warehouse.warehouse_name}")
print(f"  • Total Capacity: {refreshed_warehouse.total_capacity}")
print(f"  • Current Occupancy: {refreshed_warehouse.current_occupancy}")
print(f"  • Occupancy Percentage: {refreshed_warehouse.get_occupancy_percentage():.1f}%")
print(f"  • Vacancy Percentage: {100 - refreshed_warehouse.get_occupancy_percentage():.1f}%")

# Verify the results
occupancy_percentage = refreshed_warehouse.get_occupancy_percentage()
if occupancy_percentage > 0:
    print("\n✅ SUCCESS: Occupancy percentage activated automatically!")
    print(f"   Expected: 25.0%, Actual: {occupancy_percentage:.1f}%")
else:
    print("\n❌ FAILED: Occupancy percentage not activated")
    print(f"   Value: {occupancy_percentage:.1f}%")

# Clean up by deleting the test warehouse
test_warehouse.delete()
print("\n🧹 Test warehouse deleted")